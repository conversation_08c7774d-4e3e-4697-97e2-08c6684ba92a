# backend/core/schemas/document_schemas.py
"""
Document-related Pydantic schemas for validation and serialization.

This module provides schemas for document management including data import/export,
report generation, and calculation standards management.
"""

import json
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field, field_validator, ConfigDict
from enum import Enum

from .base import BaseSoftDeleteSchema, PaginatedResponseSchema


# ============================================================================
# ENUMS AND CONSTANTS
# ============================================================================


class ImportType(str, Enum):
    """Supported import data types."""

    PIPE_DATA = "pipe_data"
    VESSEL_DATA = "vessel_data"
    COMPONENT_DATA = "component_data"
    ELECTRICAL_DATA = "electrical_data"
    SWITCHBOARD_DATA = "switchboard_data"


class DocumentType(str, Enum):
    """Supported document types for export."""

    HEAT_TRACING_REPORT = "heat_tracing_report"
    ELECTRICAL_REPORT = "electrical_report"
    SWITCHBOARD_REPORT = "switchboard_report"
    CALCULATION_SUMMARY = "calculation_summary"
    PROJECT_OVERVIEW = "project_overview"
    COMPLIANCE_REPORT = "compliance_report"


class FileFormat(str, Enum):
    """Supported file formats."""

    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    JSON = "json"


# ============================================================================
# IMPORTED DATA REVISION SCHEMAS
# ============================================================================


class ImportedDataRevisionBaseSchema(BaseModel):
    """Base schema for ImportedDataRevision with common fields."""

    source_filename: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Original filename of imported data",
    )
    revision_identifier: Optional[str] = Field(
        None, max_length=50, description="Revision identifier for tracking versions"
    )
    import_type: ImportType = Field(..., description="Type of data being imported")
    is_active_revision: bool = Field(
        True, description="Whether this is the active revision"
    )

    @field_validator("source_filename")
    @classmethod
    def validate_filename(cls, v: str) -> str:
        """Validate filename format."""
        if not v or not v.strip():
            raise ValueError("Filename cannot be empty")

        # Check for valid file extensions
        valid_extensions = [".xlsx", ".xls", ".csv", ".json"]
        if not any(v.lower().endswith(ext) for ext in valid_extensions):
            raise ValueError(
                f"File must have one of these extensions: {', '.join(valid_extensions)}"
            )

        return v.strip()

    @field_validator("revision_identifier")
    @classmethod
    def validate_revision_identifier(cls, v: Optional[str]) -> Optional[str]:
        """Validate revision identifier format."""
        if v is not None:
            v = v.strip()
            if not v:
                return None
            # Allow alphanumeric, dots, hyphens, underscores
            if not all(c.isalnum() or c in ".-_" for c in v):
                raise ValueError(
                    "Revision identifier can only contain letters, numbers, dots, hyphens, and underscores"
                )
        return v


class ImportedDataRevisionCreateSchema(ImportedDataRevisionBaseSchema):
    """Schema for creating a new imported data revision."""

    project_id: int = Field(
        ..., gt=0, description="ID of the project this import belongs to"
    )
    imported_by_user_id: Optional[int] = Field(
        None, gt=0, description="ID of user who imported the data"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "project_id": 1,
                "imported_by_user_id": 1,
                "source_filename": "pipe_data_v2.xlsx",
                "revision_identifier": "REV-002",
                "import_type": "pipe_data",
                "is_active_revision": True,
            }
        }
    )


class ImportedDataRevisionUpdateSchema(BaseModel):
    """Schema for updating an existing imported data revision."""

    revision_identifier: Optional[str] = Field(
        None, max_length=50, description="Revision identifier for tracking versions"
    )
    is_active_revision: Optional[bool] = Field(
        None, description="Whether this is the active revision"
    )

    # Apply the same validators as base schema
    _validate_revision_identifier = field_validator("revision_identifier")(
        ImportedDataRevisionBaseSchema.validate_revision_identifier
    )


class ImportedDataRevisionReadSchema(
    ImportedDataRevisionBaseSchema, BaseSoftDeleteSchema
):
    """Schema for reading/displaying imported data revision data."""

    project_id: int = Field(..., description="ID of the project this import belongs to")
    imported_by_user_id: Optional[int] = Field(
        None, description="ID of user who imported the data"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "project_id": 1,
                "imported_by_user_id": 1,
                "source_filename": "pipe_data_v2.xlsx",
                "revision_identifier": "REV-002",
                "import_type": "pipe_data",
                "is_active_revision": True,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ImportedDataRevisionSummarySchema(BaseModel):
    """Lightweight schema for imported data revision listings."""

    id: int = Field(..., description="Revision ID")
    source_filename: str = Field(..., description="Original filename")
    revision_identifier: Optional[str] = Field(None, description="Revision identifier")
    import_type: ImportType = Field(..., description="Type of imported data")
    is_active_revision: bool = Field(
        ..., description="Whether this is the active revision"
    )
    created_at: datetime = Field(..., description="Import timestamp")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# EXPORTED DOCUMENT SCHEMAS
# ============================================================================


class ExportedDocumentBaseSchema(BaseModel):
    """Base schema for ExportedDocument with common fields."""

    document_type: DocumentType = Field(
        ..., description="Type of document being exported"
    )
    filename: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Generated filename for the document",
    )
    revision: Optional[str] = Field(
        None, max_length=50, description="Document revision identifier"
    )
    file_path_or_url: Optional[str] = Field(
        None, max_length=500, description="File path or URL for document access"
    )
    is_latest_revision: bool = Field(
        True, description="Whether this is the latest revision"
    )

    @field_validator("filename")
    @classmethod
    def validate_filename(cls, v: str) -> str:
        """Validate filename format."""
        if not v or not v.strip():
            raise ValueError("Filename cannot be empty")

        # Check for valid file extensions
        valid_extensions = [".pdf", ".xlsx", ".xls", ".csv", ".json"]
        if not any(v.lower().endswith(ext) for ext in valid_extensions):
            raise ValueError(
                f"File must have one of these extensions: {', '.join(valid_extensions)}"
            )

        return v.strip()


class ExportedDocumentCreateSchema(ExportedDocumentBaseSchema):
    """Schema for creating a new exported document."""

    project_id: int = Field(
        ..., gt=0, description="ID of the project this document belongs to"
    )
    generated_by_user_id: Optional[int] = Field(
        None, gt=0, description="ID of user who generated the document"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "project_id": 1,
                "generated_by_user_id": 1,
                "document_type": "heat_tracing_report",
                "filename": "HT_Report_Project_Alpha_v1.pdf",
                "revision": "v1.0",
                "file_path_or_url": "/documents/reports/ht_report_1.pdf",
                "is_latest_revision": True,
            }
        }
    )


class ExportedDocumentUpdateSchema(BaseModel):
    """Schema for updating an existing exported document."""

    revision: Optional[str] = Field(
        None, max_length=50, description="Document revision identifier"
    )
    file_path_or_url: Optional[str] = Field(
        None, max_length=500, description="File path or URL for document access"
    )
    is_latest_revision: Optional[bool] = Field(
        None, description="Whether this is the latest revision"
    )


class ExportedDocumentReadSchema(ExportedDocumentBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying exported document data."""

    project_id: int = Field(
        ..., description="ID of the project this document belongs to"
    )
    generated_by_user_id: Optional[int] = Field(
        None, description="ID of user who generated the document"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "project_id": 1,
                "generated_by_user_id": 1,
                "document_type": "heat_tracing_report",
                "filename": "HT_Report_Project_Alpha_v1.pdf",
                "revision": "v1.0",
                "file_path_or_url": "/documents/reports/ht_report_1.pdf",
                "is_latest_revision": True,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ExportedDocumentSummarySchema(BaseModel):
    """Lightweight schema for exported document listings."""

    id: int = Field(..., description="Document ID")
    document_type: DocumentType = Field(..., description="Type of document")
    filename: str = Field(..., description="Generated filename")
    revision: Optional[str] = Field(None, description="Document revision")
    is_latest_revision: bool = Field(
        ..., description="Whether this is the latest revision"
    )
    created_at: datetime = Field(..., description="Generation timestamp")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# CALCULATION STANDARD SCHEMAS
# ============================================================================


class CalculationStandardBaseSchema(BaseModel):
    """Base schema for CalculationStandard with common fields."""

    name: str = Field(..., min_length=1, max_length=100, description="Standard name")
    standard_code: str = Field(
        ..., min_length=1, max_length=50, description="Unique standard code identifier"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Standard description"
    )
    parameters_json: Optional[str] = Field(
        None, description="JSON string containing standard parameters"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate standard name."""
        if not v or not v.strip():
            raise ValueError("Standard name cannot be empty")
        return v.strip()

    @field_validator("standard_code")
    @classmethod
    def validate_standard_code(cls, v: str) -> str:
        """Validate standard code format."""
        if not v or not v.strip():
            raise ValueError("Standard code cannot be empty")

        v = v.strip().upper()

        # Allow alphanumeric, dots, hyphens, underscores
        if not all(c.isalnum() or c in ".-_" for c in v):
            raise ValueError(
                "Standard code can only contain letters, numbers, dots, hyphens, and underscores"
            )

        return v

    @field_validator("parameters_json")
    @classmethod
    def validate_parameters_json(cls, v: Optional[str]) -> Optional[str]:
        """Validate parameters JSON format."""
        if v is not None and v.strip():
            try:
                parsed = json.loads(v)
                if not isinstance(parsed, dict):
                    raise ValueError("Parameters must be a JSON object")
                return v.strip()
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format for parameters")
        return v


class CalculationStandardCreateSchema(CalculationStandardBaseSchema):
    """Schema for creating a new calculation standard."""

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "TR 50410 Heat Tracing Standard",
                "standard_code": "TR-50410",
                "description": "Technical Report 50410 for heat tracing calculations",
                "parameters_json": '{"safety_factor": 1.2, "max_temp_rating": 250, "min_bend_radius": 50}',
            }
        }
    )


class CalculationStandardUpdateSchema(BaseModel):
    """Schema for updating an existing calculation standard."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Standard name"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Standard description"
    )
    parameters_json: Optional[str] = Field(
        None, description="JSON string containing standard parameters"
    )

    # Apply the same validators as base schema
    _validate_name = field_validator("name")(
        CalculationStandardBaseSchema.validate_name
    )
    _validate_parameters_json = field_validator("parameters_json")(
        CalculationStandardBaseSchema.validate_parameters_json
    )


class CalculationStandardReadSchema(
    CalculationStandardBaseSchema, BaseSoftDeleteSchema
):
    """Schema for reading/displaying calculation standard data."""

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "TR 50410 Heat Tracing Standard",
                "standard_code": "TR-50410",
                "description": "Technical Report 50410 for heat tracing calculations",
                "parameters_json": '{"safety_factor": 1.2, "max_temp_rating": 250, "min_bend_radius": 50}',
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class CalculationStandardSummarySchema(BaseModel):
    """Lightweight schema for calculation standard listings."""

    id: int = Field(..., description="Standard ID")
    name: str = Field(..., description="Standard name")
    standard_code: str = Field(..., description="Standard code")
    description: Optional[str] = Field(None, description="Standard description")
    created_at: datetime = Field(..., description="Creation timestamp")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# FILE MANAGEMENT SCHEMAS
# ============================================================================


class FileUploadSchema(BaseModel):
    """Schema for file upload requests."""

    filename: str = Field(
        ..., min_length=1, max_length=255, description="Original filename"
    )
    file_size: int = Field(
        ..., gt=0, le=100 * 1024 * 1024, description="File size in bytes (max 100MB)"
    )
    content_type: str = Field(..., description="MIME content type")
    project_id: int = Field(..., gt=0, description="Project ID for file association")
    import_type: Optional[ImportType] = Field(
        None, description="Type of data import (if applicable)"
    )

    @field_validator("content_type")
    @classmethod
    def validate_content_type(cls, v: str) -> str:
        """Validate content type."""
        allowed_types = [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # .xlsx
            "application/vnd.ms-excel",  # .xls
            "text/csv",  # .csv
            "application/json",  # .json
            "application/pdf",  # .pdf
        ]

        if v not in allowed_types:
            raise ValueError(
                f"Unsupported file type. Allowed types: {', '.join(allowed_types)}"
            )

        return v


class FileDownloadSchema(BaseModel):
    """Schema for file download responses."""

    filename: str = Field(..., description="Download filename")
    content_type: str = Field(..., description="MIME content type")
    file_size: int = Field(..., description="File size in bytes")
    download_url: str = Field(..., description="Download URL")


class DocumentGenerationRequestSchema(BaseModel):
    """Schema for document generation requests."""

    project_id: int = Field(..., gt=0, description="Project ID")
    document_type: DocumentType = Field(..., description="Type of document to generate")
    file_format: FileFormat = Field(FileFormat.PDF, description="Output file format")
    include_calculations: bool = Field(
        True, description="Include detailed calculations"
    )
    include_diagrams: bool = Field(True, description="Include diagrams and charts")
    custom_parameters: Optional[dict] = Field(
        None, description="Custom generation parameters"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "project_id": 1,
                "document_type": "heat_tracing_report",
                "file_format": "pdf",
                "include_calculations": True,
                "include_diagrams": True,
                "custom_parameters": {
                    "show_detailed_specs": True,
                    "include_cost_analysis": False,
                },
            }
        }
    )


# ============================================================================
# PAGINATED RESPONSE SCHEMAS
# ============================================================================


class ImportedDataRevisionListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for imported data revision listings."""

    revisions: List[ImportedDataRevisionSummarySchema] = Field(
        ..., description="List of imported data revisions"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "revisions": [
                    {
                        "id": 1,
                        "source_filename": "pipe_data_v2.xlsx",
                        "revision_identifier": "REV-002",
                        "import_type": "pipe_data",
                        "is_active_revision": True,
                        "created_at": "2024-01-15T10:30:00Z",
                    }
                ],
                "total": 25,
                "page": 1,
                "per_page": 10,
                "total_pages": 3,
            }
        }
    )


class ExportedDocumentListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for exported document listings."""

    documents: List[ExportedDocumentSummarySchema] = Field(
        ..., description="List of exported documents"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "documents": [
                    {
                        "id": 1,
                        "document_type": "heat_tracing_report",
                        "filename": "HT_Report_Project_Alpha_v1.pdf",
                        "revision": "v1.0",
                        "is_latest_revision": True,
                        "created_at": "2024-01-15T10:30:00Z",
                    }
                ],
                "total": 15,
                "page": 1,
                "per_page": 10,
                "total_pages": 2,
            }
        }
    )


class CalculationStandardListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for calculation standard listings."""

    standards: List[CalculationStandardSummarySchema] = Field(
        ..., description="List of calculation standards"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "standards": [
                    {
                        "id": 1,
                        "name": "TR 50410 Heat Tracing Standard",
                        "standard_code": "TR-50410",
                        "description": "Technical Report 50410 for heat tracing calculations",
                        "created_at": "2024-01-15T10:30:00Z",
                    }
                ],
                "total": 8,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )
