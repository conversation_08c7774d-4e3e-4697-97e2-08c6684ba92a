# src/core/errors/error_templates.py
ERROR_MESSAGES = {
    "PROJECT_NOT_FOUND": "Project with ID '{project_id}' not found.",
    "COMPONENT_ALREADY_EXISTS": "Component '{name}' in category '{category}' already exists.",
    "VALIDATION_ERROR": "Input validation failed: {details}.",
    "DB_OPERATION_FAILED": "Database operation failed: {reason}.",
    "CALCULATION_INPUT_INVALID": "Invalid input for calculation: {details}.",
    # ... other templates
}