### Data Import Architecture Specification

**1. Purpose & Role**
The Data Import layer is responsible for ingesting structured data from various external file formats into the application's database. Its primary purposes are to:
* **Populate Initial Data:** Allow users to easily load existing project data, material catalogs, or equipment lists.
* **Facilitate Integration:** Provide a standardized way to consume data from external sources or other software tools.
* **Reduce Manual Entry:** Automate the process of populating the database, minimizing human error and saving time.
* **Ensure Data Quality:** Implement robust validation mechanisms to ensure imported data conforms to application rules and integrity constraints.

**2. Location (`src/core/data_import/`)**
The Data Import layer will be organized within `src/core/data_import/`. This structure allows for modularity based on file type and import scope.

```
src/core/data_import/
├── __init__.py
├── global_importer.py              # Orchestrates global data imports
├── project_importer.py             # Orchestrates project-specific data imports
├── parsers/                        # Sub-package for file format-specific parsers
│   ├── __init__.py
│   ├── xlsx_parser.py              # Handles parsing of .xlsx files (using openpyxl/pandas)
│   ├── json_parser.py              # Handles parsing of .json files (using built-in json)
│   └── csv_parser.py               # Handles parsing of .csv files (using csv/pandas)
├── validators/                     # Sub-package for import-specific validation logic
│   ├── __init__.py
│   └── import_data_validator.py    # General validation against Pydantic schemas
├── mappers/                        # Sub-package for mapping parsed data to ORM models
│   ├── __init__.py
│   ├── project_data_mapper.py      # Maps parsed project data to specific ORM models
│   └── catalog_data_mapper.py      # Maps parsed catalog data to global ORM models
└── import_service.py               # Facade service for triggering imports (can be part of ProjectService)
```

**3. Scope of Import**

* **Global Data Import:**
    * **Use Case:** Importing data that is universally applicable across all projects or serves as a global lookup (e.g., standard heating cable catalogs, material properties, default insulation types, industry regulations).
    * **Mechanism:** Typically accessed via dedicated API endpoints (e.g., `POST /api/v1/admin/import/cable_catalog`) and handled by a `GlobalImporter`. This data is persisted into core, globally accessible database tables.
* **Project-Specific Data Import:**
    * **Use Case:** Importing data relevant to a particular design project (e.g., pipe schedules, specific equipment lists, existing heating elements, localized material conditions).
    * **Mechanism:** Accessed via project-scoped API endpoints (e.g., `POST /api/v1/projects/{project_id}/import/pipes`) and handled by a `ProjectImporter`. This data is linked to the specific project entity.

**4. Supported File Types (Initially)**

The initial implementation will support the following file formats, chosen for their commonality in engineering data exchange and ease of parsing in Python:

* **`.xlsx` (Excel Spreadsheets):**
    * **Use Cases:** Highly common for structured tabular data like lists of pipes, components, instruments, material specifications, or simple BOMs (Bill of Materials). Allows for multiple sheets within a single file.
    * **Parsing Library:** `openpyxl` (for direct cell access and basic manipulation) or `pandas` (for robust data frame operations, cleaning, and transformation).
* **`.json` (JSON Files):**
    * **Use Cases:** Excellent for hierarchical or nested data structures. Useful for importing a complete project snapshot, complex configuration settings, or richly defined entities with sub-components.
    * **Parsing Library:** Python's built-in `json` module.
* **`.csv` (Comma Separated Values):**
    * **Use Cases:** Widely adopted for simple, flat tabular data. Often preferred for its simplicity and efficiency for large datasets where the rich formatting of Excel is not required.
    * **Parsing Library:** Python's built-in `csv` module or `pandas` for more advanced parsing and data cleaning.

**Other Data Types (Not Initially Supported, but for Future Consideration):**

* **`_txt` (Plain Text - Delimited/Fixed-Width):** While possible, it's less common for new integrations.
* **Proprietary CAD/BIM Formats (e.g., .dwg, .dxf, .ifc, .rvt):** These formats are highly complex and typically require specialized libraries, commercial SDKs, or dedicated integration platforms. Supporting them would involve a significant increase in scope and complexity and are not recommended for initial implementation. The focus should be on structured data, not graphical models.
* **XML:** While XML is a structured format, JSON has largely superseded it for new integrations due to its simpler syntax. Support can be added if a specific legacy integration requires it.

**5. Core Responsibilities**

* **File Parsing (`parsers/`):** Reads the raw content of the input file and extracts the data into a generic, format-agnostic intermediate structure (e.g., a list of dictionaries).
*   **Data Validation (`validators/`):**
    *   **Schema Validation:** Uses [Pydantic schemas](../../schemas/schemas-architecture.md) (defined in `src/core/schemas/`) to validate the structure and basic data types of each record or object extracted from the file.
    * **Business Rule Validation:** Applies more complex business rules (e.g., uniqueness constraints, valid enumerations, range checks, cross-field consistency) against the parsed data.
    * **Referential Integrity Checks:** Validates if referenced entities (e.g., a material ID) exist in the database or are part of the current import batch.
* **Data Transformation/Mapping (`mappers/`):** Converts the validated intermediate data into the specific SQLAlchemy ORM model instances required by the application. This may involve:
    * Performing lookups for foreign key relationships.
    * Applying default values.
    * Performing necessary unit conversions for persistence.
*   **Database Persistence:** Utilizes the [Repository Layer](../../repositories/repositories-architecture.md) to save the fully validated and transformed [ORM model instances](../../models/models-architecture.md) to the database.
*   **Error Reporting:** Provides detailed, user-friendly feedback on import failures. This includes:
    * Identifying the file (and potentially sheet/tab) where the error occurred.
    * Specifying the row/line number of the error.
    * Providing the specific validation message (e.g., "Invalid temperature value," "Duplicate project code").
    * Summarizing total records processed, successful imports, and failures.
* **Progress Tracking:** For potentially large imports, the system can report progress (e.g., percentage complete, current record count).
* **Transactional Import:** Ensures that a single import operation (e.g., importing one XLSX file) is atomic. If any record within the file fails to import due to critical errors, the entire batch should be rolled back to maintain data integrity.

**6. Interaction with Other Layers**

* **API Layer:** Provides endpoints for triggering data imports. These endpoints will handle file uploads and pass the file content or path to the relevant Service Layer.
* **Service Layer (`src/core/services/` - potentially `ImportService` or integrated into `ProjectService` for project-specific imports):**
    * Acts as the orchestrator for the import process.
    * Receives import requests, manages file uploads (if necessary), and invokes the appropriate `Importer` (e.g., `GlobalImporter`, `ProjectImporter`).
    * **Error Handling:** Catches `ImportError` (from `src/core/errors/exceptions.py`) raised by the Data Import Layer, logs the details, and transforms them into appropriate Service Layer exceptions for the API to return.
* **Repository Layer:** The `mappers/` and importers will interact with the Repository Layer to query existing data (for lookups and uniqueness checks) and to persist new or updated records.
* **Schemas Layer (`src/core/schemas/`):** Defines the Pydantic schemas that represent the expected structure and validation rules for the incoming imported data.
*   **Error Handling (`src/core/errors/exceptions.py`):** The Data Import layer will raise `ImportError` (a custom exception) and potentially `InvalidInputError`, `DuplicateEntryError`, or `NotFoundError` with rich context for any parsing, validation, or persistence failures.

    See the [Error and Exception Handling Architecture](../../errors/errors-architecture.md) for more details.

*   **File Storage (Temporary):** For larger file uploads, the [API layer](../../../api/api-architecture.md) might temporarily store the file, and the Data Import layer would then read from this temporary location.

**7. Key Principles**

* **Robustness:** Design for resilience against malformed input files and invalid data, providing clear feedback instead of crashing.
* **Validation First:** Prioritize thorough validation of imported data before any attempt to persist it to the database.
* **User Feedback:** Provide comprehensive and actionable error messages to the user for any import failures.
* **Extensibility:** Design the parsing and mapping components to be easily extendable to support new file formats or new data types in the future.
* **Transactionality:** Ensure that imports are atomic operations to prevent partial or inconsistent data from entering the database.

This architecture provides a solid foundation for robust and flexible data import capabilities.