# backend/core/database/initialization.py
"""
Database Initialization

This module handles database initialization, including running Alembic migrations
and setting up the database for the application.
"""

import os
from pathlib import Path

from alembic import command
from alembic.config import Config
from sqlalchemy import Engine
from sqlalchemy.exc import SQLAlchemyError

try:
    from backend.config.logging_config import get_logger
    from backend.config.settings import settings
except ImportError:
    from config.logging_config import get_logger
    from config.settings import settings
try:
    from backend.core.errors.exceptions import DatabaseError
except ImportError:
    from core.errors.exceptions import DatabaseError

from .engine import create_engine, get_engine
from .session import create_tables, get_session_factory

logger = get_logger(__name__)


def initialize_database(
    run_migrations: bool = True, create_tables_if_needed: bool = True
) -> Engine:
    """
    Initialize the database with proper error handling and logging.

    Args:
        run_migrations: Whether to run Alembic migrations
        create_tables_if_needed: Whether to create tables if migrations fail

    Returns:
        Initialized database engine

    Raises:
        RuntimeError: If database initialization fails completely
    """
    logger.info("Starting database initialization...")

    try:
        # Create the database engine with fallback logic
        engine = create_engine(echo=settings.DB_ECHO)
        logger.info("Database engine created successfully")

        # Initialize session factory
        get_session_factory(engine)
        logger.info("Session factory initialized")

        if run_migrations:
            try:
                run_alembic_migrations()
                logger.info("Database migrations completed successfully")
            except Exception as e:
                logger.warning(f"Migration failed: {e}")
                if create_tables_if_needed:
                    logger.info("Attempting to create tables directly...")
                    create_tables(engine)
                    logger.info("Tables created successfully")
                else:
                    raise

        # Verify database connection
        _verify_database_connection(engine)
        logger.info("Database initialization completed successfully")

        return engine

    except Exception as e:
        logger.critical(f"Database initialization failed: {e}", exc_info=True)
        raise DatabaseError(
            reason=f"Failed to initialize database: {str(e)}",
            original_exception=e,
        )


def run_alembic_migrations() -> None:
    """
    Run Alembic migrations programmatically.

    Raises:
        RuntimeError: If migrations fail
    """
    try:
        # Get the alembic.ini path
        alembic_cfg_path = _get_alembic_config_path()

        if not os.path.exists(alembic_cfg_path):
            raise FileNotFoundError(f"Alembic config not found at: {alembic_cfg_path}")

        # Create Alembic config and set the script location relative to the backend directory
        alembic_cfg = Config(alembic_cfg_path)

        # Set the script location to the correct path
        backend_dir = Path(alembic_cfg_path).parent
        script_location = backend_dir / "alembic"
        alembic_cfg.set_main_option("script_location", str(script_location))

        # Run migrations to head
        logger.info("Running Alembic migrations...")
        command.upgrade(alembic_cfg, "head")
        logger.info("Alembic migrations completed")

    except Exception as e:
        logger.error(f"Alembic migration failed: {e}")
        raise RuntimeError(f"Migration failed: {e}")


def create_initial_migration(message: str = "Initial migration") -> None:
    """
    Create the initial Alembic migration.

    Args:
        message: Migration message

    Raises:
        RuntimeError: If migration creation fails
    """
    try:
        alembic_cfg_path = _get_alembic_config_path()
        alembic_cfg = Config(alembic_cfg_path)

        # Set the script location to the correct path
        backend_dir = Path(alembic_cfg_path).parent
        script_location = backend_dir / "alembic"
        alembic_cfg.set_main_option("script_location", str(script_location))

        logger.info(f"Creating initial migration: {message}")
        command.revision(alembic_cfg, message=message, autogenerate=True)
        logger.info("Initial migration created successfully")

    except Exception as e:
        logger.error(f"Failed to create initial migration: {e}")
        raise RuntimeError(f"Migration creation failed: {e}")


def check_migration_status() -> dict:
    """
    Check the current migration status.

    Returns:
        Dictionary with migration status information
    """
    try:
        alembic_cfg_path = _get_alembic_config_path()
        alembic_cfg = Config(alembic_cfg_path)  # noqa: F841

        # This would require more complex logic to get current and head revisions
        # For now, return basic status
        return {
            "config_found": os.path.exists(alembic_cfg_path),
            "database_url": settings.effective_database_url,
            "environment": settings.ENVIRONMENT,
        }

    except Exception as e:
        logger.error(f"Failed to check migration status: {e}")
        return {"error": str(e)}


def _get_alembic_config_path() -> str:
    """Get the path to the alembic.ini file."""
    # Get the backend directory (where this file is located)
    # This file is in backend/core/database/, so we need to go up 2 levels to get to backend/
    backend_dir = Path(__file__).parent.parent.parent
    alembic_cfg_path = backend_dir / "alembic.ini"
    return str(alembic_cfg_path.absolute())


def _verify_database_connection(engine: Engine) -> None:
    """
    Verify that the database connection is working.

    Args:
        engine: SQLAlchemy engine to test

    Raises:
        SQLAlchemyError: If connection test fails
    """
    try:
        with engine.connect() as conn:
            from sqlalchemy import text

            result = conn.execute(text("SELECT 1"))
            result.fetchone()
        logger.debug("Database connection verified")
    except SQLAlchemyError as e:
        logger.error(f"Database connection verification failed: {e}")
        raise


def reset_database(confirm: bool = False) -> None:
    """
    Reset the database by dropping and recreating all tables.

    Args:
        confirm: Must be True to actually perform the reset

    Warning:
        This will delete all data! Use with extreme caution.
    """
    if not confirm:
        raise ValueError("Database reset requires explicit confirmation")

    logger.warning("Resetting database - ALL DATA WILL BE LOST!")

    try:
        engine = get_engine()

        # Drop all tables
        from .session import drop_tables

        drop_tables(engine)

        # Recreate tables
        create_tables(engine)

        logger.warning("Database reset completed")

    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        raise RuntimeError(f"Database reset failed: {e}")
