# backend/config/logging_config.py
"""
Centralized Logging Configuration

This module provides the application-wide logging configuration following
the documented best practices for the Ultimate Electrical Designer backend.
"""

import logging
import sys
from pathlib import Path
from typing import Optional

try:
    from backend.config.settings import settings
except ImportError:
    from config.settings import settings


def setup_logging(
    log_file: Optional[str] = None,
    level: Optional[int] = None,
    force_reconfigure: bool = False,
) -> logging.Logger:
    """
    Configures the application-wide logger with proper formatting and handlers.

    Args:
        log_file: Path to log file. If None, uses default based on environment
        level: Logging level. If None, uses settings.LOG_LEVEL
        force_reconfigure: Whether to force reconfiguration even if already set up

    Returns:
        Configured logger instance
    """
    # Use settings for defaults
    if level is None:
        level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)

    if log_file is None:
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        log_file = str(log_dir / "ultimate_electrical_designer.log")

    # Get the root logger for the application
    logger = logging.getLogger("ultimate_electrical_designer")

    # Avoid reconfiguring if already set up (unless forced)
    if logger.handlers and not force_reconfigure:
        return logger

    # Clear existing handlers if reconfiguring
    if force_reconfigure:
        logger.handlers.clear()

    logger.setLevel(level or logging.INFO)

    # Create formatters
    detailed_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s (%(filename)s:%(lineno)d)",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    simple_formatter = logging.Formatter("%(levelname)s - %(name)s - %(message)s")

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level or logging.INFO)

    # Use simple format for console in production, detailed in development
    if settings.ENVIRONMENT == "production":
        console_handler.setFormatter(simple_formatter)
    else:
        console_handler.setFormatter(detailed_formatter)

    # File handler
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    file_handler.setLevel(logging.DEBUG)  # Always log everything to file
    file_handler.setFormatter(detailed_formatter)

    # Add handlers to the logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    # Prevent propagation to root logger to avoid duplicate messages
    logger.propagate = False

    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module.

    This is the recommended way to get loggers in individual modules.
    All loggers will be children of the main application logger.

    Args:
        name: Logger name, typically __name__ from the calling module

    Returns:
        Logger instance configured as child of main app logger

    Example:
        # In any module
        from backend.config.logging_config import get_logger
        logger = get_logger(__name__)
        logger.info("This is a log message")
    """
    # Ensure the main logger is configured
    if not logging.getLogger("ultimate_electrical_designer").handlers:
        setup_logging()

    # Return a child logger
    return logging.getLogger(f"ultimate_electrical_designer.{name}")


def configure_external_loggers():
    """Configure logging levels for external libraries."""
    # Reduce noise from external libraries
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    logging.getLogger("alembic").setLevel(logging.INFO)
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)


# Initialize the main application logger
app_logger = setup_logging()

# Configure external library loggers
configure_external_loggers()
