# How-To: Transition to UUID Primary Keys

This document provides a guide on the process and considerations for transitioning from integer primary keys to UUIDs (Universally Unique Identifiers) in the database and application.

Transitioning primary keys from auto-incrementing integers to UUIDs (Universally Unique Identifiers) is a significant architectural decision. It's a strategic move, especially for applications considering future cloud synchronization or multi-user collaboration.

### When to Make the Transition

For your current V1.0 (offline, single-user, SQLite), **you should stick with auto-incrementing integers**. They are simpler, more performant for SQLite, and easier to debug.

The ideal time to transition to UUIDs would be when you actively begin development on:
* **FC4: Cloud-based project storage and synchronization.**
* **FC5: Multi-user collaboration features.**

These features inherently introduce scenarios where independent data generation (e.g., two users creating a new pipe offline simultaneously, or a user creating data while disconnected from the cloud) would lead to primary key collisions if sequential integers were used. UUIDs solve this problem natively.

### Recommended UUID Version: UUIDv7

If and when you decide to transition, the **UUIDv7** standard is highly recommended.

* **Why UUIDv7?** It combines the best of both worlds:
    * **Time-ordered:** It starts with a 48-bit Unix timestamp (milliseconds), ensuring that new UUIDs are generally ordered, which is crucial for efficient database indexing (B-tree performance).
    * **Strong Randomness:** The remaining bits are random, guaranteeing global uniqueness and preventing privacy concerns associated with MAC addresses (as in UUIDv1).
    * **Modern Standard:** It addresses limitations found in older UUID versions (like UUIDv1's MAC address exposure or UUIDv4's random indexing issues).
* **Python Support:** Python 3.12+ has native support for UUIDv7 via `uuid.uuid7()`. For earlier Python versions, you might use a third-party library that implements UUIDv7.

### How to Transition to UUIDs in the ORM Schema

This is a database migration, and it's generally done using a tool like Alembic. It's a complex process that requires careful planning and execution.

#### 1. Modify the `CommonColumns` Mixin

This is the most impactful change. You'd modify the `id` column definition in `src/core/models/base.py`.

**Original (Integer ID):**
```python
# In src/core/models/base.py
class CommonColumns:
    # ...
    @declared_attr
    def id(cls) -> Mapped[int]:
        return mapped_column(primary_key=True, autoincrement=True)
```

**New (UUIDv7 ID):**
You would need to import the `UUID` type from `sqlalchemy.dialects` (for database-native UUID types like PostgreSQL) or define it generally, and import `uuid` for generation.

```python
# In src/core/models/base.py
import uuid # Standard Python UUID module
from sqlalchemy.dialects import postgresql # Example for PostgreSQL UUID type
# For SQLite, UUIDs are typically stored as CHAR(36) or BLOB
from sqlalchemy import CHAR # For string representation

class CommonColumns:
    # ...
    @declared_attr
    def id(cls) -> Mapped[uuid.UUID]: # Type hint for Python objects
        # For PostgreSQL, use postgresql.UUID:
        # return mapped_column(postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid7)

        # For SQLite or other databases that don't have a native UUID type,
        # store as CHAR(36) for string representation:
        return mapped_column(CHAR(36), primary_key=True, default=lambda: str(uuid.uuid7()), unique=True)
        # Note: 'unique=True' is crucial here, as it's not autoincrementing.
        # 'as_uuid=True' (for postgresql.UUID) ensures it converts to/from uuid.UUID object.
        # For CHAR(36), you'd manually convert str <-> uuid.UUID in application code or via custom TypeDecorator
        # if you want uuid.UUID objects directly from the column.
        # However, for simplicity, storing as string and converting implicitly is common.
```
*Self-correction*: For SQLAlchemy's `CHAR(36)` mapping to `uuid.UUID` objects automatically, you can use a `TypeDecorator` or rely on `sqlalchemy.types.UUID` if available in a more generic form. Given `CHAR(36)` is just a string, `default=lambda: str(uuid.uuid7())` is the most straightforward and portable way for string-based UUIDs.

#### 2. Update Foreign Keys and Relationships

Since primary keys become UUIDs, all corresponding foreign key columns must also be updated to the UUID type.

**Original (Integer FK):**
```python
# Example from Project model
# project_id: Mapped[int] = mapped_column(ForeignKey('Project.id'), nullable=False)
```

**New (UUID FK):**
```python
# Example from Project model
# project_id: Mapped[uuid.UUID] = mapped_column(ForeignKey('Project.id'), nullable=False)
```
You would need to update all foreign key columns across your schema (`project_id`, `user_id`, `component_id`, etc.) to match the UUID type of their referenced primary keys.

#### 3. Database Migration Steps (Using Alembic)

1.  **Generate Migration Script:** After modifying your ORM models, you would use Alembic to detect the schema changes:
    ```bash
    alembic revision --autogenerate -m "Migrate to UUID primary keys"
    ```
2.  **Edit Migration Script:** The auto-generated script will try to drop and recreate tables, which is undesirable for existing data. You'll need to manually edit the migration script (`versions/<timestamp>_migrate_to_uuid.py`) to perform the following steps for each table:
    * **Add new UUID columns:** For each existing `id` column and all foreign key columns, add a new temporary column with the UUID type (e.g., `new_id`, `new_fk`).
    * **Populate new UUID columns:**
        * For primary keys, generate a UUID for each existing integer ID. Store the mapping (old\_int\_id -> new\_uuid) in a temporary table or in memory if the dataset is small.
        * For foreign keys, use the mapping created in the previous step to populate the new UUID foreign key columns.
    * **Drop old integer columns:** After data is copied and verified.
    * **Rename new UUID columns:** Rename `new_id` to `id`, `new_fk` to the original foreign key name.
    * **Recreate/Adjust Constraints:** Recreate primary key, foreign key, and unique constraints using the new UUID columns.
3.  **Run Migration:**
    ```bash
    alembic upgrade head
    ```

**Challenges during Migration:**

* **Downtime:** For larger databases, this process may require significant downtime.
* **Data Integrity:** Maintaining data integrity during the transformation is paramount. Thorough testing is crucial.
* **Performance:** The migration itself can be resource-intensive.
* **Complex Relations:** Tables with many foreign keys or circular dependencies will complicate the migration script.

#### 4. Application Code Adjustments

* **ID Handling:** Any part of your application code that directly uses IDs (e.g., in URLs, API responses, logging, internal dictionaries, `GET` queries) will need to be updated to expect and handle UUID strings instead of integers.
* **Testing:** Comprehensive unit, integration, and system tests are essential to ensure everything works correctly after the migration.

In summary, transitioning to UUIDs is a strategic decision for future scalability and distributed capabilities. If that future is certain, planning for UUIDv7 now and performing a well-executed database migration (likely via Alembic) when the need arises is the recommended path.