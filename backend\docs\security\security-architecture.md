### Key Rotation Architecture Specification

**1. Purpose & Role**
The Key Rotation module automates and manages the lifecycle of cryptographic keys (e.g., [JWT signing keys](#3-core-responsibilities--functionality-jwt-based-authentication)). Its purpose is to enhance security by regularly changing sensitive keys, minimizing the impact of a potential key compromise, and enabling seamless, zero-downtime key transitions.

**2. Location (`src/core/security/key_rotation.py`)**
This module belongs within `src/core/security/`, as it directly contributes to the application's cryptographic [security posture](security-architecture.md).

**3. Core Responsibilities & Functionality**

*   **Key Generation:** Provides functions to securely generate new cryptographic keys (e.g., using `secrets.token_urlsafe` or `cryptography` library for stronger keys).
*   **Version Tracking & Storage:**
    *   Stores multiple active key versions in a secure, persistent location. The [**database**](../../core/database/architecture.md) ([`src/core/models/`](../core/models/models-architecture.md)) is a suitable choice for a local, persistent backend for its atomicity and [transactional guarantees](../core/database/transaction-architecture.md). A dedicated table (e.g., `key_store`) would hold key IDs, the key material itself (encrypted at rest if database encryption is not guaranteed), creation timestamp, and expiration timestamp.
    *   Each key will have a unique identifier (e.g., `kid` in [JWT headers](#3-core-responsibilities--functionality-jwt-based-authentication)).

    See the [Database Layer Architecture](../core/database/architecture.md), [Model Layer Architecture](../core/models/models-architecture.md), and [Transaction Management Architecture](../core/database/transaction-architecture.md) for more details.

*   **Key Retrieval:**
    *   **Current Key:** Provides a mechanism to retrieve the *current active key* for signing new data (e.g., new [JWTs](#3-core-responsibilities--functionality-jwt-based-authentication)).
    *   **Previous Keys:** Provides mechanisms to retrieve *recently active keys* (within a configurable "grace period" or "rotation window") for verifying older signatures. This is crucial for backward compatibility during rotation.
*   **Rotation Logic:**
    *   **Triggering:** Supports both manual (e.g., via an [admin API endpoint](../../../api/api-architecture.md)) and automated (e.g., a scheduled background task or a [lifespan event check](../../lifespan-architecture.md)) key rotation.
    *   **Activation:** Newly generated keys are first stored, then marked as "active" at a specific time.
    *   **Deactivation/Archival:** Old keys are marked as "inactive" after a grace period, but remain available for verification for a set duration.
    *   **Deletion:** Provides a mechanism to permanently delete expired and inactive keys after a safe retention period.
*   **Error Handling:** Manages failures during key generation, storage, or retrieval, logging warnings or raising specific exceptions.

    See the [Error and Exception Handling Architecture](../core/errors/errors-architecture.md) for more details.

*   **Auditing:** Logs all key lifecycle events (generation, activation, deactivation, deletion) for security auditing purposes.

    See the [Logging How-To](../../how-to/how-to_logging.md) for more details on logging.

**4. Interaction with Other Layers**

*   **Authentication Layer (`src/api/v1/auth_routes.py`, `src/core/services/user_service.py`):** The [JWT token generation and verification logic](#3-core-responsibilities--functionality-jwt-based-authentication) will directly call methods in `key_rotation.py` to obtain the current signing key and valid verification keys.

    See the [API Layer Architecture](../../../api/api-architecture.md) and [Service Layer Architecture](../core/services/services-architecture.md) for related documentation.

*   **Database Layer (`src/core/models/`, `src/core/repositories/`):** Utilizes the [Repository Layer](../core/repositories/repositories-architecture.md) to persist and retrieve key versions from the [database](../core/database/architecture.md).

    See the [Database Layer Architecture](../core/database/architecture.md) and [Repository Layer Architecture](../core/repositories/repositories-architecture.md) for more details.

*   **Lifespan Events (`src/app.py`):** Can trigger an initial key load or a check for overdue rotation during application startup.

    See the [Lifespan Events Architecture](../../lifespan-architecture.md) for more details.

*   **Admin API (`src/api/v1/admin_routes.py` - if created):** A dedicated admin endpoint could be created to manually trigger key rotation or inspect key status.

    See the [API Layer Architecture](../../../api/api-architecture.md) for more details on API endpoints.

**5. Key Principles**

*   **Zero Downtime:** Key rotation must occur without interrupting user service.
*   **Backward Compatibility:** Older keys must remain valid for verification for a configurable period during rotation.
*   **Automation:** Minimize manual intervention for key lifecycle management.
*   **Secure Storage:** Keys stored in the database must be encrypted at rest if the database layer itself isn't fully encrypted. The key material itself should be protected.
*   **Auditable Lifecycle:** Every change to a key's status or version must be logged.
