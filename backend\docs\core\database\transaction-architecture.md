### Transaction Management Architecture Specification

**1. Purpose & Role**
The Transaction Management module provides dedicated utilities to control database transactions, ensuring **atomicity, consistency, isolation, and durability (ACID)** for database operations. Its primary purpose is to:
* Guarantee data integrity by ensuring that multi-step business operations either fully succeed or fully fail.
* Simplify the application of transactional behavior to business logic.
* Enhance reliability by incorporating retry mechanisms for transient database failures.

**2. Location (`src/core/database/transaction_manager.py` or `src/core/transaction.py`)**
This module, which directly interacts with database session management, is best placed in `src/core/database/transaction_manager.py` to clearly signify its role within the database interaction components.

**3. Core Responsibilities & Functionality**

* **Transactional Context Manager (`transaction()`):**
    * **Session Management:** Provides a context manager (`async def transaction(db: Session = Depends(get_db)):`) that receives an active database session (typically injected by FastAPI at the request level) or creates a new one if not provided (though dependency injection is preferred).

    See the [Database Layer Architecture](../architecture.md) for details on database sessions and the [Dependency Management Architecture](../../../dependency-management-architecture.md) for dependency injection.
    * **Atomic Scope:** Initiates a database transaction upon entering the context.
    * **Commit on Success:** Automatically commits the transaction if the code block within the `with transaction():` statement executes without exceptions.
    * **Rollback on Failure:** Automatically rolls back the transaction if any exception occurs within the code block, ensuring that no partial changes are persisted.
    * **Session Cleanup:** Ensures the session is properly closed or returned to the pool after the transaction, regardless of success or failure.
    * **Nested Transactions (Optional):** Can support SQLAlchemy's nested transaction feature (subtransactions) for cases where a function might be called both within and outside an existing transaction.
* **Transactional Decorator (`@transactional`):**
    * **Syntactic Sugar:** Provides a convenient decorator that wraps a function (typically a Service Layer method) with the transactional context manager logic.
    * **Session Injection:** Automatically manages the database session lifecycle for the decorated function, potentially injecting the session as an argument into the function.
    * **Error Propagation:** Propagates exceptions from the wrapped function after ensuring a rollback.
* **Bulk Operations Support:**
    * The transaction manager facilitates **atomic execution of multiple ORM operations** (inserts, updates, deletes, bulk operations performed by repositories) by providing the transactional context.
    * It doesn't directly perform the bulk operations, but rather ensures that a series of such operations by repositories are treated as a single, atomic unit.
* **Retry Mechanism:**
    * **Transient Error Handling:** Implements logic to detect transient database errors (e.g., deadlocks, connection resets, concurrency conflicts).
    * **Configurable Retries:** Allows configuring the number of retries and the backoff strategy (e.g., exponential backoff) between retry attempts.
    * **Idempotency Consideration:** While the manager handles retries, the underlying business logic within the transaction should ideally be designed to be idempotent where possible, to prevent unintended side effects if a transaction commits successfully but the client doesn't receive confirmation and retries.

**4. Interaction with Other Layers**

* **Service Layer (Primary Consumer):**
    * Service methods that perform complex business logic involving multiple database interactions (e.g., creating a project and its default switchboard, or updating multiple related entities) are the primary consumers.
    * They will use either the `@transactional` decorator or the `with transaction():` context manager to define explicit transactional boundaries.
    * Example: A `ProjectService.create_full_project()` method would be decorated with `@transactional`.

    See the [Service Layer Architecture](../../services/services-architecture.md) for more details on service methods.

*   **Repository Layer (`src/core/repositories/`):**
    * Repositories receive the `Session` object (provided by the transaction manager or FastAPI's dependency injection) and perform their CRUD operations *within* that session.
    * Repositories **do not** initiate or commit/rollback transactions themselves; they rely on the `Session` being part of an active transaction managed by the layer above (Service Layer via the Transaction Manager).
* **API Layer (`src/api/`):**
    * The API layer does *not* directly interact with the transaction manager. It delegates all business logic, including transactional concerns, to the Service Layer.
* **Database Layer (`src/core/database/`):** Relies on SQLAlchemy's session and ORM capabilities to execute operations within the transactional scope.
* **Error Handling (`src/core/errors/`):** Exceptions raised within a transactional block are caught by the transaction manager, triggering a rollback, and then potentially re-raised as [application-specific exceptions](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy).

    See the [Error and Exception Handling Architecture](../../errors/errors-architecture.md) for more details.

**5. Key Principles**

* **Atomicity:** Ensures that all database operations within a transaction either complete successfully as a single unit or are entirely undone.
* **Consistency:** Guarantees that a transaction brings the database from one valid state to another.
* **Reliability:** Enhanced by the built-in retry mechanisms for transient errors.
* **Separation of Concerns:** Clearly separates transactional control logic from core business logic in services and data access logic in repositories.
* **Developer Ergonomics:** Provides easy-to-use tools (decorator, context manager) for applying transactional behavior.
* **Single Responsibility:** The transaction manager is solely responsible for managing the transactional state of the database session, not for performing actual data operations.