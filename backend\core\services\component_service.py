# backend/core/services/component_service.py
"""
Service layer for Component and ComponentCategory business logic.

This module handles all business operations related to components including
CRUD operations, validation, and orchestration with other services.
"""

import logging
from typing import Optional

from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from backend.core.errors.exceptions import (
    ComponentNotFoundError,
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
)
from backend.core.models.components import Component
from backend.core.repositories.component_repository import (
    ComponentCategoryRepository,
    ComponentRepository,
)
from backend.core.schemas.component_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryListResponseSchema,
    ComponentCategoryReadSchema,
    ComponentCreateSchema,
    ComponentListResponseSchema,
    ComponentReadSchema,
    ComponentSummarySchema,
    ComponentUpdateSchema,
)

logger = logging.getLogger(__name__)


class ComponentService:
    """
    Service for Component business logic and operations.

    This service handles all business operations related to components including
    CRUD operations, validation, and orchestration with other services.
    """

    def __init__(
        self,
        component_repository: ComponentRepository,
        category_repository: ComponentCategoryRepository,
    ):
        """
        Initialize the Component service.

        Args:
            component_repository: Repository for Component data access
            category_repository: Repository for ComponentCategory data access
        """
        self.component_repository = component_repository
        self.category_repository = category_repository
        logger.debug("ComponentService initialized with repositories")

    def create_component(
        self, component_data: ComponentCreateSchema
    ) -> ComponentReadSchema:
        """
        Create a new component with business validation.

        Args:
            component_data: Validated component creation data

        Returns:
            ComponentReadSchema: Created component data

        Raises:
            DuplicateEntryError: If component name already exists in category
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Attempting to create new component: '{component_data.name}' in category {component_data.category_id}"
        )

        try:
            # Business validation
            self._validate_component_creation(component_data)

            # Convert schema to dict for repository
            component_dict = component_data.model_dump()

            # Create component via repository
            new_component = self.component_repository.create(component_dict)

            # Commit the transaction
            self.component_repository.db_session.commit()
            self.component_repository.db_session.refresh(new_component)

            logger.info(
                f"Component '{new_component.name}' (ID: {new_component.id}) created successfully"
            )

            # Convert to read schema
            return ComponentReadSchema.model_validate(new_component)

        except IntegrityError as e:
            self.component_repository.db_session.rollback()
            logger.warning(
                f"Duplicate component detected: {component_data.name} in category {component_data.category_id}"
            )

            # Determine which field caused the duplicate
            error_msg = str(e.orig).lower()
            if "uq_component_name_category" in error_msg:
                raise DuplicateEntryError(
                    message=f"A component with the name '{component_data.name}' already exists in this category.",
                    original_exception=e,
                )
            else:
                raise DuplicateEntryError(
                    message="A component with the given unique constraint already exists.",
                    original_exception=e,
                )

        except SQLAlchemyError as e:
            self.component_repository.db_session.rollback()
            logger.error(
                f"Database error creating component '{component_data.name}': {e}",
                exc_info=True,
            )
            raise DatabaseError(
                reason=f"Failed to create component due to database error: {str(e)}",
                original_exception=e,
            )
        except Exception as e:
            self.component_repository.db_session.rollback()
            logger.error(
                f"Unexpected error creating component '{component_data.name}': {e}",
                exc_info=True,
            )
            raise

    def get_component_details(self, component_id: int) -> ComponentReadSchema:
        """
        Retrieve detailed component information.

        Args:
            component_id: Component ID

        Returns:
            ComponentReadSchema: Component details

        Raises:
            ComponentNotFoundError: If component doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving component details for ID: {component_id}")

        try:
            component = self.component_repository.get_by_id(component_id)

            if component is None or component.is_deleted:
                logger.warning(f"Component not found: {component_id}")
                raise ComponentNotFoundError(component_id_or_name=str(component_id))

            logger.debug(f"Component found: '{component.name}' (ID: {component.id})")
            return ComponentReadSchema.model_validate(component)

        except ComponentNotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving component {component_id}: {e}",
                exc_info=True,
            )
            raise DatabaseError(
                reason=f"Failed to retrieve component due to database error: {str(e)}",
                original_exception=e,
            )

    def update_component(
        self, component_id: int, component_data: ComponentUpdateSchema
    ) -> ComponentReadSchema:
        """
        Update an existing component.

        Args:
            component_id: Component ID
            component_data: Validated component update data

        Returns:
            ComponentReadSchema: Updated component data

        Raises:
            ComponentNotFoundError: If component doesn't exist
            DuplicateEntryError: If updated name conflicts
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to update component: {component_id}")

        try:
            # Get existing component
            existing_component = self.component_repository.get_by_id(component_id)

            if existing_component is None or existing_component.is_deleted:
                raise ComponentNotFoundError(component_id_or_name=str(component_id))

            # Business validation for updates
            self._validate_component_update(existing_component, component_data)

            # Update only provided fields
            update_dict = component_data.model_dump(exclude_unset=True)

            if not update_dict:
                logger.debug(f"No fields to update for component {component_id}")
                return ComponentReadSchema.model_validate(existing_component)

            # Update the component
            for field, value in update_dict.items():
                setattr(existing_component, field, value)

            # Commit the transaction
            self.component_repository.db_session.commit()
            self.component_repository.db_session.refresh(existing_component)

            logger.info(
                f"Component '{existing_component.name}' (ID: {existing_component.id}) updated successfully"
            )
            return ComponentReadSchema.model_validate(existing_component)

        except (ComponentNotFoundError, DuplicateEntryError):
            self.component_repository.db_session.rollback()
            raise
        except IntegrityError as e:
            self.component_repository.db_session.rollback()
            logger.warning(
                f"Duplicate constraint violation updating component {component_id}"
            )

            error_msg = str(e.orig).lower()
            if "uq_component_name_category" in error_msg:
                raise DuplicateEntryError(
                    message=f"A component with the name '{component_data.name}' already exists in this category.",
                    original_exception=e,
                )
            else:
                raise DuplicateEntryError(
                    message="Update would violate unique constraint.",
                    original_exception=e,
                )
        except SQLAlchemyError as e:
            self.component_repository.db_session.rollback()
            logger.error(
                f"Database error updating component {component_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to update component due to database error: {str(e)}",
                original_exception=e,
            )

    def delete_component(
        self, component_id: int, deleted_by_user_id: Optional[int] = None
    ) -> None:
        """
        Soft delete a component.

        Args:
            component_id: Component ID
            deleted_by_user_id: ID of user performing the deletion

        Raises:
            ComponentNotFoundError: If component doesn't exist
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to delete component: {component_id}")

        try:
            # Get existing component
            existing_component = self.component_repository.get_by_id(component_id)

            if existing_component is None or existing_component.is_deleted:
                logger.warning(f"Component {component_id} not found or already deleted")
                raise ComponentNotFoundError(component_id_or_name=str(component_id))

            # Perform soft delete
            from datetime import datetime, timezone

            existing_component.is_deleted = True
            existing_component.deleted_at = datetime.now(timezone.utc)
            existing_component.deleted_by_user_id = deleted_by_user_id

            # Commit the transaction
            self.component_repository.db_session.commit()

            logger.info(
                f"Component '{existing_component.name}' (ID: {existing_component.id}) deleted successfully"
            )

        except ComponentNotFoundError:
            self.component_repository.db_session.rollback()
            raise
        except SQLAlchemyError as e:
            self.component_repository.db_session.rollback()
            logger.error(
                f"Database error deleting component {component_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to delete component due to database error: {str(e)}",
                original_exception=e,
            )

    def get_components_list(
        self,
        page: int = 1,
        per_page: int = 10,
        category_id: Optional[int] = None,
        search_term: Optional[str] = None,
        manufacturer: Optional[str] = None,
        include_deleted: bool = False,
    ) -> ComponentListResponseSchema:
        """
        Get paginated list of components with filtering.

        Args:
            page: Page number (1-based)
            per_page: Number of components per page
            category_id: Optional category filter
            search_term: Optional search term
            manufacturer: Optional manufacturer filter
            include_deleted: Whether to include soft-deleted components

        Returns:
            ComponentListResponseSchema: Paginated component list

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving components list: page={page}, per_page={per_page}")

        try:
            # Calculate offset
            skip = (page - 1) * per_page

            # Get components from repository based on filters
            if search_term or manufacturer:
                components = self.component_repository.search_components(
                    search_term=search_term or "",
                    category_id=category_id,
                    manufacturer=manufacturer,
                    skip=skip,
                    limit=per_page,
                )
            elif category_id:
                components = self.component_repository.get_by_category(
                    category_id=category_id, skip=skip, limit=per_page
                )
            else:
                components = self.component_repository.get_active_components(
                    skip=skip, limit=per_page
                )

            # Filter out deleted components if not requested
            if not include_deleted:
                components = [c for c in components if not c.is_deleted]

            # Get total count (simplified for now)
            if category_id:
                total = self.component_repository.count_by_category(category_id)
            else:
                all_components = self.component_repository.get_active_components(
                    skip=0, limit=1000
                )
                if not include_deleted:
                    all_components = [c for c in all_components if not c.is_deleted]
                total = len(all_components)

            # Convert to summary schemas
            component_summaries = [
                ComponentSummarySchema.model_validate(c) for c in components
            ]

            # Calculate total pages
            import math

            total_pages = math.ceil(total / per_page) if total > 0 else 1

            logger.debug(
                f"Retrieved {len(component_summaries)} components (total: {total})"
            )

            return ComponentListResponseSchema(
                components=component_summaries,
                total=total,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
            )

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving components list: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to retrieve components list due to database error: {str(e)}",
                original_exception=e,
            )

    def _validate_component_creation(
        self, component_data: ComponentCreateSchema
    ) -> None:
        """
        Perform business validation for component creation.

        Args:
            component_data: Component creation data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Validate category exists
        category = self.category_repository.get_by_id(component_data.category_id)
        if category is None or category.is_deleted:
            raise DataValidationError(
                details={
                    "category_id": f"Category with ID {component_data.category_id} does not exist"
                }
            )

        # Check for duplicate name in category
        existing_component = self.component_repository.get_by_name_and_category(
            component_data.name, component_data.category_id
        )
        if existing_component:
            raise DataValidationError(
                details={
                    "name": f"Component with name '{component_data.name}' already exists in this category"
                }
            )

    def _validate_component_update(
        self, existing_component: Component, component_data: ComponentUpdateSchema
    ) -> None:
        """
        Perform business validation for component updates.

        Args:
            existing_component: Current component data
            component_data: Update data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Validate category exists if being updated
        if component_data.category_id is not None:
            category = self.category_repository.get_by_id(component_data.category_id)
            if category is None or category.is_deleted:
                raise DataValidationError(
                    details={
                        "category_id": f"Category with ID {component_data.category_id} does not exist"
                    }
                )

        # Check for duplicate name in category if name or category is being updated
        if component_data.name is not None or component_data.category_id is not None:
            check_name = (
                component_data.name
                if component_data.name is not None
                else existing_component.name
            )
            check_category_id = (
                component_data.category_id
                if component_data.category_id is not None
                else existing_component.category_id
            )

            existing_with_name = self.component_repository.get_by_name_and_category(
                check_name, check_category_id
            )
            if existing_with_name and existing_with_name.id != existing_component.id:
                raise DataValidationError(
                    details={
                        "name": f"Component with name '{check_name}' already exists in this category"
                    }
                )


class ComponentCategoryService:
    """
    Service for ComponentCategory business logic and operations.

    This service handles all business operations related to component categories.
    """

    def __init__(self, category_repository: ComponentCategoryRepository):
        """
        Initialize the ComponentCategory service.

        Args:
            category_repository: Repository for ComponentCategory data access
        """
        self.category_repository = category_repository
        logger.debug("ComponentCategoryService initialized")

    def create_category(
        self, category_data: ComponentCategoryCreateSchema
    ) -> ComponentCategoryReadSchema:
        """
        Create a new component category.

        Args:
            category_data: Validated category creation data

        Returns:
            ComponentCategoryReadSchema: Created category data

        Raises:
            DuplicateEntryError: If category name already exists
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to create new category: '{category_data.name}'")

        try:
            # Business validation
            self._validate_category_creation(category_data)

            # Convert schema to dict for repository
            category_dict = category_data.model_dump()

            # Create category via repository
            new_category = self.category_repository.create(category_dict)

            # Commit the transaction
            self.category_repository.db_session.commit()
            self.category_repository.db_session.refresh(new_category)

            logger.info(
                f"Category '{new_category.name}' (ID: {new_category.id}) created successfully"
            )

            # Convert to read schema
            return ComponentCategoryReadSchema.model_validate(new_category)

        except IntegrityError as e:
            self.category_repository.db_session.rollback()
            logger.warning(f"Duplicate category detected: {category_data.name}")

            raise DuplicateEntryError(
                message=f"A category with the name '{category_data.name}' already exists.",
                original_exception=e,
            )

        except SQLAlchemyError as e:
            self.category_repository.db_session.rollback()
            logger.error(
                f"Database error creating category '{category_data.name}': {e}",
                exc_info=True,
            )
            raise DatabaseError(
                reason=f"Failed to create category due to database error: {str(e)}",
                original_exception=e,
            )
        except Exception as e:
            self.category_repository.db_session.rollback()
            logger.error(
                f"Unexpected error creating category '{category_data.name}': {e}",
                exc_info=True,
            )
            raise

    def get_category_details(self, category_id: int) -> ComponentCategoryReadSchema:
        """
        Retrieve detailed category information.

        Args:
            category_id: Category ID

        Returns:
            ComponentCategoryReadSchema: Category details

        Raises:
            ComponentNotFoundError: If category doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving category details for ID: {category_id}")

        try:
            category = self.category_repository.get_by_id(category_id)

            if category is None or category.is_deleted:
                logger.warning(f"Category not found: {category_id}")
                raise ComponentNotFoundError(component_id_or_name=str(category_id))

            logger.debug(f"Category found: '{category.name}' (ID: {category.id})")
            return ComponentCategoryReadSchema.model_validate(category)

        except ComponentNotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving category {category_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to retrieve category due to database error: {str(e)}",
                original_exception=e,
            )

    def get_categories_list(
        self,
        page: int = 1,
        per_page: int = 10,
        parent_id: Optional[int] = None,
        include_deleted: bool = False,
    ) -> ComponentCategoryListResponseSchema:
        """
        Get paginated list of categories.

        Args:
            page: Page number (1-based)
            per_page: Number of categories per page
            parent_id: Optional parent category filter (None for root categories)
            include_deleted: Whether to include soft-deleted categories

        Returns:
            ComponentCategoryListResponseSchema: Paginated category list

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving categories list: page={page}, per_page={per_page}")

        try:
            # Calculate offset
            skip = (page - 1) * per_page

            # Get categories from repository
            if parent_id is None:
                categories = self.category_repository.get_root_categories(
                    skip=skip, limit=per_page
                )
            else:
                categories = self.category_repository.get_subcategories(
                    parent_id=parent_id, skip=skip, limit=per_page
                )

            # Filter out deleted categories if not requested
            if not include_deleted:
                categories = [c for c in categories if not c.is_deleted]

            # Get total count (simplified for now)
            if parent_id is None:
                all_categories = self.category_repository.get_root_categories(
                    skip=0, limit=1000
                )
            else:
                all_categories = self.category_repository.get_subcategories(
                    parent_id=parent_id, skip=0, limit=1000
                )

            if not include_deleted:
                all_categories = [c for c in all_categories if not c.is_deleted]
            total = len(all_categories)

            # Convert to read schemas
            category_schemas = [
                ComponentCategoryReadSchema.model_validate(c) for c in categories
            ]

            # Calculate total pages
            import math

            total_pages = math.ceil(total / per_page) if total > 0 else 1

            logger.debug(
                f"Retrieved {len(category_schemas)} categories (total: {total})"
            )

            return ComponentCategoryListResponseSchema(
                categories=category_schemas,
                total=total,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
            )

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving categories list: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to retrieve categories list due to database error: {str(e)}",
                original_exception=e,
            )

    def _validate_category_creation(
        self, category_data: ComponentCategoryCreateSchema
    ) -> None:
        """
        Perform business validation for category creation.

        Args:
            category_data: Category creation data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Check for duplicate name
        existing_category = self.category_repository.get_by_name(category_data.name)
        if existing_category:
            raise DataValidationError(
                details={
                    "name": f"Category with name '{category_data.name}' already exists"
                }
            )

        # Validate parent category exists if specified
        if category_data.parent_category_id is not None:
            parent_category = self.category_repository.get_by_id(
                category_data.parent_category_id
            )
            if parent_category is None or parent_category.is_deleted:
                raise DataValidationError(
                    details={
                        "parent_category_id": f"Parent category with ID {category_data.parent_category_id} does not exist"
                    }
                )
