# backend/core/database/engine.py
"""
Database Engine Management

This module handles the creation and configuration of SQLAlchemy engines
with automatic fallback from SQL Server to SQLite based on environment
and connection availability.
"""

from typing import Any, Dict, Optional

from sqlalchemy import Engine
from sqlalchemy import create_engine as sqlalchemy_create_engine
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import StaticPool

try:
    from backend.config.logging_config import get_logger
    from backend.config.settings import settings
except ImportError:
    from config.logging_config import get_logger
    from config.settings import settings
try:
    from backend.core.errors.exceptions import DatabaseError
except ImportError:
    from core.errors.exceptions import DatabaseError

logger = get_logger(__name__)

# Global engine instance
_engine: Optional[Engine] = None


def create_engine(
    database_url: Optional[str] = None, echo: bool = False, force_recreate: bool = False
) -> Engine:
    """
    Create and configure a SQLAlchemy engine with automatic fallback logic.

    Args:
        database_url: Optional database URL. If None, uses settings.effective_database_url
        echo: Whether to echo SQL statements to stdout
        force_recreate: Whether to force recreation of the engine even if one exists

    Returns:
        Configured SQLAlchemy Engine instance

    Raises:
        RuntimeError: If unable to create any database connection
    """
    global _engine

    if _engine is not None and not force_recreate:
        logger.debug("Returning existing engine instance")
        return _engine

    if database_url is None:
        database_url = settings.effective_database_url

    logger.info(f"Creating database engine for environment: {settings.ENVIRONMENT}")

    # Try to create engine with the provided URL
    engine = _create_engine_with_fallback(database_url, echo)

    if engine is None:
        error_msg = "Failed to create database engine with any available configuration"
        logger.critical(error_msg)
        raise DatabaseError(
            reason=error_msg,
        )

    _engine = engine
    logger.info(
        f"Database engine created successfully using: {_get_db_type_from_url(engine.url)}"
    )
    return _engine


def _create_engine_with_fallback(database_url: str, echo: bool) -> Optional[Engine]:
    """
    Create engine with fallback logic from SQL Server to SQLite.

    Args:
        database_url: Primary database URL to try
        echo: Whether to echo SQL statements

    Returns:
        Engine instance or None if all attempts fail
    """
    # First, try the provided database URL
    engine = _try_create_engine(database_url, echo)
    if engine is not None:
        return engine

    # If the primary URL failed and it's not SQLite, try SQLite fallback
    if not database_url.startswith("sqlite"):
        logger.warning("Primary database connection failed, falling back to SQLite")
        sqlite_url = settings._get_sqlite_url()
        engine = _try_create_engine(sqlite_url, echo)
        if engine is not None:
            logger.info("Successfully connected to SQLite fallback database")
            return engine

    logger.error("All database connection attempts failed")
    return None


def _try_create_engine(database_url: str, echo: bool) -> Optional[Engine]:
    """
    Attempt to create and test a database engine.

    Args:
        database_url: Database URL to try
        echo: Whether to echo SQL statements

    Returns:
        Engine instance if successful, None otherwise
    """
    try:
        # Configure engine based on database type
        engine_kwargs: Dict[str, Any] = {
            "echo": echo,
            "future": True,  # Use SQLAlchemy 2.0 style
        }

        # Special configuration for SQLite
        if database_url.startswith("sqlite"):
            engine_kwargs["poolclass"] = StaticPool
            engine_kwargs["connect_args"] = {
                "check_same_thread": False,  # Allow SQLite to be used across threads
                "timeout": 20,  # 20 second timeout for database locks
            }

        # Create the engine
        engine = sqlalchemy_create_engine(database_url, **engine_kwargs)

        # Test the connection
        with engine.connect() as conn:
            from sqlalchemy import text

            conn.execute(text("SELECT 1"))

        logger.debug(
            f"Successfully created and tested engine for: {_get_db_type_from_url(engine.url)}"
        )
        return engine

    except SQLAlchemyError as e:
        logger.warning(
            f"Failed to create engine for {_get_db_type_from_url(database_url)}: {e}"
        )
        return None
    except Exception as e:
        logger.error(
            f"Unexpected error creating engine for {_get_db_type_from_url(database_url)}: {e}"
        )
        return None


def _get_db_type_from_url(url) -> str:
    """Extract database type from URL for logging purposes."""
    url_str = str(url)
    if url_str.startswith("sqlite"):
        return "SQLite"
    elif url_str.startswith("mssql") or url_str.startswith("sqlserver"):
        return "SQL Server"
    elif url_str.startswith("postgresql"):
        return "PostgreSQL"
    elif url_str.startswith("mysql"):
        return "MySQL"
    else:
        return "Unknown"


def get_engine() -> Engine:
    """
    Get the current engine instance.

    Returns:
        Current Engine instance

    Raises:
        RuntimeError: If no engine has been created yet
    """
    global _engine
    if _engine is None:
        raise RuntimeError(
            "Database engine not initialized. Call create_engine() first."
        )
    return _engine


def close_engine() -> None:
    """Close the current engine and reset the global instance."""
    global _engine
    if _engine is not None:
        _engine.dispose()
        _engine = None
        logger.info("Database engine closed and disposed")
