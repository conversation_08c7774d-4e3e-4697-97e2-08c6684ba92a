### Frontend-Backend Integration Architecture Specification

**1. Purpose & Role**
This specification defines the architectural principles and mechanisms governing the interaction between the **FastAPI Python backend** and the **Next.js React/TypeScript frontend**. Its primary purpose is to ensure seamless, efficient, secure, and reliable communication, enabling the frontend to consume backend services and present data to the user, while the backend processes business logic and manages data persistence.

**2. Key Principles**

* **Loose Coupling:** Frontend and backend should be largely independent, communicating via well-defined APIs. Changes in one layer should ideally not require significant changes in the other, as long as the API contract is maintained.
* **Clear Contracts:** Data structures and API behaviors are explicitly defined and enforced, minimizing ambiguity and integration errors.
* **Security First:** All communication channels are secured, and authentication/authorization mechanisms are robustly implemented across the boundary.
* **Performance Optimization:** Communication is optimized for speed and efficiency, minimizing latency and bandwidth usage.
* **Error Transparency:** Errors originating from the backend are communicated to the frontend in a standardized, actionable, and user-friendly manner.
* **Scalability:** The integration approach should support future scaling of both frontend and backend components.

**3. Communication Protocol**

* **Standard:** **RESTful HTTP/HTTPS** (for secure communication, even locally via Electron's HTTPS capabilities if configured, or standard HTTP for local dev).
* **Data Format:** **JSON (JavaScript Object Notation)** for all request and response bodies. This is the native format for FastAPI's Pydantic serialization and easily consumed by JavaScript/TypeScript.
* **Methods:** Standard HTTP methods (GET, POST, PUT, DELETE, PATCH) are used semantically for CRUD operations on resources.

**4. Data Contract & Type Safety**

* **Backend Source of Truth:** The **Pydantic schemas** in the backend's `src/core/schemas/` layer are the definitive source of truth for all data structures.
* **OpenAPI Specification:** FastAPI automatically generates an **OpenAPI (Swagger) specification** (available at `/docs` endpoint in development) based on Pydantic schemas and route definitions.
* **Frontend Type Generation:**
    * A crucial step is to **automatically generate TypeScript interfaces/types** for the frontend directly from the backend's OpenAPI specification.
    * **Tooling:** Libraries like `openapi-typescript-codegen` or `Orval` will be used in the frontend's build process to create a `src/types/backend.ts` (or similar) file.
    * **Benefit:** This ensures end-to-end type safety, where frontend components consuming backend data have compile-time guarantees that the data structure matches the backend's expectations, significantly reducing integration bugs.
* **Request/Response Alignment:** Frontend components will send data conforming to backend's Pydantic request schemas and expect responses conforming to backend's Pydantic response schemas.

**5. Authentication & Authorization Flow**

* **Authentication Mechanism:** **JWT (JSON Web Tokens)** will be used for authentication.
    * **Login:** Frontend sends user credentials (username/password) to the backend's `/api/v1/auth/login` endpoint.
    * **Token Issuance:** Backend authenticates the user and, upon success, generates and returns a JWT (access token and optionally a refresh token).
    * **Token Storage:** The frontend will securely store the access token. For an Electron-wrapped application, options include:
        * **`localStorage` / `sessionStorage`:** Simple for access tokens, but vulnerable to XSS. Requires careful mitigation.
        * **`httpOnly` Cookies:** More secure against XSS, as JavaScript cannot access them. Requires backend to set these cookies. This is generally preferred for access tokens.
        * **Secure Storage (Electron-specific):** For highly sensitive tokens or refresh tokens, Electron's native secure storage (e.g., `keytar` or OS-level credential managers) could be explored, but this adds complexity.
    * **Token Transmission:** For subsequent authenticated requests, the frontend will include the access token in the `Authorization` header as a Bearer token (e.g., `Authorization: Bearer <JWT>`).
* **Authorization:**
    * Backend's `SecurityMiddleware` (`src/middleware/security.py`) will validate the JWT on every protected endpoint.
    * Backend's Service Layer methods (and potentially API route decorators) will perform granular authorization checks based on user roles and permissions (`src/core/security/`).
    * Frontend will conditionally render UI elements or enable/disable features based on the authenticated user's roles/permissions (received as part of the user object after login or from a dedicated user info endpoint).

**6. Error Handling**

* **Backend's Role:** The backend's **Error Handling Layer (`src/core/errors/`)** will catch all application exceptions and translate them into standardized JSON error responses, conforming to the `ErrorResponseSchema` (`src/core/schemas/error.py`).
* **Frontend's Role:**
    * The frontend's API client (`Axios` or `fetch`) will intercept HTTP error responses.
    * It will parse the `ErrorResponseSchema` to extract `code`, `detail`, `category`, `status_code`, and `errors` (for validation issues).
    * **User Feedback:** These parsed error details will be used to display user-friendly messages (e.g., toast notifications, inline form errors, modal dialogs) to the user.
    * **Logging:** Frontend can also log these errors to its own logging system (e.g., browser console, or a frontend-specific telemetry service).

**7. File Management (Uploads & Downloads)**

* **File Uploads (Frontend to Backend):**
    * Frontend will use standard HTML `<input type="file">` elements or drag-and-drop components.
    * Files will be sent to the backend's `Data Import` API endpoints (`POST /api/v1/data/import/...`) using `multipart/form-data` content type.
    * FastAPI handles file uploads efficiently.
* **File Downloads (Backend to Frontend):**
    * Frontend will make `GET` requests to backend's `Report Generation` or `Data Export` API endpoints (e.g., `GET /api/v1/reports/{report_id}/download`).
    * Backend will return the file content (e.g., PDF, XLSX) with appropriate `Content-Type` and `Content-Disposition` headers, allowing the browser to download the file.

**8. Asynchronous Operations & Long-Running Tasks**

* **Immediate Response:** For long-running backend tasks (e.g., complex reports, large data imports), the backend API endpoint will respond *immediately* with a `202 Accepted` status code and a **task ID**.
* **Polling/WebSockets (Future):**
    * **Polling:** Frontend can periodically poll a status endpoint (`GET /api/v1/tasks/{task_id}/status`) to check the progress and eventual result of the long-running task.
    * **WebSockets (Future Enhancement):** For real-time updates and a more responsive UI, a WebSocket connection could be established (e.g., using `FastAPI's WebSockets`) to push task progress updates directly to the frontend. This would be a significant future enhancement.
* **Task Management:** The backend would use an internal task queue/worker system (e.g., Celery, RQ) to process these long-running tasks asynchronously, preventing API request timeouts.

**9. Deployment & Packaging Considerations (Electron Wrapper)**

* **Local Backend Server:** The Python FastAPI backend will likely run as a local server process alongside the Electron application.
* **Inter-Process Communication:** Electron's main process will be responsible for starting and managing the Python backend process.
* **Frontend Access:** The React frontend (running in the Electron renderer process) will make HTTP requests to the local FastAPI server (e.g., `http://localhost:8000/api/...`).
* **Packaging:** The Electron build process will need to bundle the Python environment, dependencies, and your FastAPI application code, ensuring it's self-contained and runnable on the target OS.

**10. Development Workflow**

* **Independent Development:** Frontend and backend teams can develop largely independently, relying on the OpenAPI specification as their shared contract.
* **Mocking:** Frontend can use mock API responses (e.g., using `MSW - Mock Service Worker`) during early development to proceed without a fully functional backend.
* **Cross-Origin Resource Sharing (CORS):** During development, the FastAPI backend will need to be configured to allow CORS requests from the frontend's development server origin (e.g., `http://localhost:3000` for Next.js dev server). This will be handled by the `CORS middleware` in `src/middleware/`.

This integration architecture provides a robust and clear roadmap for how your frontend and backend will communicate and collaborate to deliver the Heat Tracing Design Application.