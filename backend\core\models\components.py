from sqlalchemy import ForeignKey, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import (  # EnumType not directly used here, but can be if specific_data is typed
    Base,
    CommonColumns,
    SoftDeleteColumns,
)

# from .enums import ElectricalComponentType # Uncomment if you want to use this enum for specific_data validation/typing
# from .electrical import ElectricalNode


class ComponentCategory(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "ComponentCategory"

    parent_category_id: Mapped[int | None] = mapped_column(
        ForeignKey("ComponentCategory.id"), nullable=True
    )
    description: Mapped[str | None] = mapped_column(Text, nullable=True)

    # Relationships
    parent_category: Mapped["ComponentCategory | None"] = relationship(
        "ComponentCategory",
        remote_side=lambda: ComponentCategory.id,
        back_populates="sub_categories",
    )
    sub_categories: Mapped[list["ComponentCategory"]] = relationship(
        "ComponentCategory",
        back_populates="parent_category",
        cascade="all, delete-orphan",
    )
    components: Mapped[list["Component"]] = relationship(
        back_populates="category", cascade="all, delete-orphan"
    )

    __table_args__ = (UniqueConstraint("name", name="uq_component_category_name"),)

    def __repr__(self):
        return f"<ComponentCategory(id={self.id}, name='{self.name}')>"


class Component(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Component"

    category_id: Mapped[int] = mapped_column(
        ForeignKey("ComponentCategory.id"), nullable=False
    )
    manufacturer: Mapped[str | None] = mapped_column(nullable=True)
    model: Mapped[str | None] = mapped_column(nullable=True)
    specific_data: Mapped[str | None] = mapped_column(
        Text, nullable=True
    )  # JSON field for specific attributes

    # Relationships
    category: Mapped["ComponentCategory"] = relationship(back_populates="components")
    electrical_node: Mapped["ElectricalNode | None"] = relationship(
        "ElectricalNode", back_populates="related_component", uselist=False
    )

    __table_args__ = (
        UniqueConstraint("name", "category_id", name="uq_component_name_category"),
    )

    def __repr__(self):
        return f"<Component(id={self.id}, name='{self.name}', category='{self.category.name}')>"
