import datetime

from sqlalchemy import DateTime, ForeignKey, Text, func
from sqlalchemy.orm import Mapped, mapped_column, relationship


from .base import Base  # ActivityLog does not use CommonColumns or SoftDeleteColumns
# from .users import User


class ActivityLog(Base):
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)
    timestamp: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=func.now(), nullable=False
    )
    user_id: Mapped[int | None] = mapped_column(ForeignKey("User.id"), nullable=True)
    event_type: Mapped[str] = mapped_column(nullable=False)
    entity_type: Mapped[str | None] = mapped_column(nullable=True)
    entity_id: Mapped[int | None] = mapped_column(nullable=True)
    details: Mapped[str | None] = mapped_column(Text, nullable=True)

    # Relationships
    # TODO: Uncomment when User.activity_logs is implemented
    # user: Mapped["User | None"] = relationship(back_populates="activity_logs")

    def __repr__(self):
        return f"<ActivityLog(id={self.id}, event='{self.event_type}', user_id={self.user_id}, time={self.timestamp.strftime('%Y-%m-%d %H:%M:%S')})>"
