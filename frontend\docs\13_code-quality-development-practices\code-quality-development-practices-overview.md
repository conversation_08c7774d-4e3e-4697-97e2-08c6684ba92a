## **Code Quality & Development Practices**

This section outlines specific practices and tooling to ensure high code quality, consistency, and an efficient development workflow across the frontend application.

### **Linting & Formatting**

* **Tools:** **ESLint** for static code analysis (identifying problematic patterns and enforcing style rules) and **Prettier** for consistent code formatting.
* **Configuration:**
  * ESLint configuration will extend recommended rulesets (e.g., eslint:recommended, @typescript-eslint/recommended, next/core-web-vitals).
  * Custom rules specific to project conventions will be added as needed.
  * <PERSON><PERSON><PERSON> will handle all stylistic formatting, with ESLint configured to disable conflicting stylistic rules.
* **Enforcement:**
  * **Pre-commit Hooks:** Utilize husky and lint-staged to automatically run <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> on staged files before each commit. This ensures no unformatted or rule-breaking code enters the repository.
  * **CI/CD Integration:** ESLint and Prettier checks will be part of the CI pipeline to enforce standards across the team.
  * **Editor Integration:** Developers are encouraged to configure their IDEs (e.g., VS Code extensions) to automatically format and lint on save.

### **Code Generation (Codegen for UI Components)**

* **Purpose:** To reduce boilerplate and ensure consistency when creating new components, features, or files that follow repetitive patterns.
* **Tooling:** Explore using tools like **Hygen** or **Plop** for creating custom command-line generators.
* **Usage:** Define templates for:
  * New React Components (e.g., feature/MyFeatureComponent.tsx, MyFeatureComponent.module.css, MyFeatureComponent.test.tsx).
  * Custom Hooks (e.g., useMyHook.ts, useMyHook.test.ts).
  * Contexts and their associated Providers/Hooks.
  * Potentially new API resource files (though much of this is already covered by OpenAPI generation).
* **Benefit:** Accelerates development, reduces human error, and ensures adherence to the established directory structure and coding standards.

### **Naming Conventions**

* **Variables, Functions, Components:** Use camelCase.
* **Constants:** Use SCREAMING_SNAKE_CASE for global, immutable constants (src/lib/constants.ts).
* **Types & Interfaces:** Use PascalCase.
* **Files/Folders:** Use kebab-case for consistency with URL conventions (e.g., my-component.tsx, my-feature-folder/).
* **CSS Classes (if applicable):** Follow Tailwind's utility-first approach. For custom @apply classes, kebab-case is preferred.
* **Enforcement:** Primarily through code reviews and ESLint rules.

### **Commenting Practices**

* **Purpose:** To explain *why* certain code decisions were made, complex algorithms, or non-obvious logic, rather than *what* the code does (which should be clear from its structure and naming).
* **TypeDoc/JSDoc:** Use JSDoc comments for documenting the public API of functions, custom hooks, and components, especially their props, return values, and side effects. This facilitates auto-generated documentation and IDE autocompletion.
* **TODO/FIXME Comments:** Use sparingly and follow a consistent format (e.g., // TODO: [description], // FIXME: [description]). These should be tracked and addressed.

### **Immutability Practices**

* **Principle:** Favor immutable data structures wherever possible, especially when managing state.
* **Tools/Techniques:**
  * Use const for variable declarations.
  * Avoid direct mutation of arrays (use spread operator [...], map, filter, reduce) and objects (use spread operator {...}, Object.assign).
  * Leverage Lodash's immutable-first functions (e.g., _.cloneDeep, or using lodash/fp for more functional transformations).
* **Benefit:** Simplifies state management, prevents unexpected side effects, and makes debugging easier.

### **Error Logging & Monitoring Configuration**

* **Frontend Error Capture:** Ensure all unhandled errors (React Errors, uncaught exceptions, unhandled promise rejections) are captured by the ErrorMonitoringService (as described in Section 7.3).
* **Contextual Information:** Log errors with relevant context (user ID, route, component name, application state snapshot) to aid in debugging.
* **Development vs. Production:** Differentiate logging levels and reporting for development vs. production environments (e.g., more verbose logging in dev, only critical errors to monitoring in prod).

### **Dependency Management & Versioning**

To ensure stability, reproducibility, and a controlled upgrade process, we will explicitly manage our project's dependencies and their versions.

* **Version Pinning:** Core dependencies and major frameworks will be explicitly pinned to specific versions in package.json to prevent unexpected breaking changes from automatic minor/patch updates. For minor dependencies where compatibility is less critical, caret (^) ranges may be used to allow for patch and minor updates.
* **Current Key Dependency Versions (as of May 30, 2025):**
  * react: 19.1.0
  * next: 15.3.3
  * typescript: 5.8.3
  * tailwindcss: 4.0.0
  * @tanstack/react-query: 5.36.0
  * zustand: 4.5.2
  * vitest: 3.1.4
  * @testing-library/react: 14.0.0
  * cypress: 14.3.2
  * eslint: 9.6.0
  * prettier: 3.3.3
  * lodash-es: 4.17.21
  * next-i18next: 15.2.0
  * msw: 2.8.6
*Note: These versions represent the stable releases at the time of this specification's last update. Regular reviews and controlled upgrades are essential.*
* **Compatibility Notes:**
  * @testing-library/react@14.0.0 now requires react@^19.0.0 as a peer dependency. Ensure React is at least version 19 for component testing.
  * Next.js 15.x.x versions are designed to work optimally with React 19.x.x.
* **Automated Dependency Updates:** Tools like Dependabot or Renovate will be configured to automatically create pull requests for dependency updates. For major version updates, these PRs will require manual review, thorough testing, and potential code adjustments.
* **Upgrade Process:**
  * Major version upgrades will be handled in dedicated branches.
  * Comprehensive unit, integration, and E2E tests must pass.
  * A manual regression testing phase will be conducted.
  * Any breaking changes and required code modifications will be clearly documented in release notes or ADRs.

### **Environment Management**

* **Tool:** Utilize dotenv or Next.js's built-in environment variable support.
* **Validation:** Implement schema validation for environment variables (e.g., using Zod or a simple joi-like structure) at application startup to catch misconfigurations early.
* **Security:** Emphasize that sensitive keys should *not* be exposed to the client-side (NEXT_PUBLIC_ prefix for client-side access in Next.js).
