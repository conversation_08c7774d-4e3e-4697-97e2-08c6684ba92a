### Utility Functions Architecture Specification

**1. Purpose & Role**
The Utility Functions layer provides a collection of generic, reusable functions and helpers that encapsulate common, reusable logic not specific to any particular business domain. Its primary purpose is to:
*   **Promote Code Reusability:** Avoid duplicating code for common tasks across different modules.
*   **Improve Readability:** Keep core business logic clean and focused by delegating common operations to dedicated utilities.
*   **Enhance Maintainability:** Centralize common patterns, making it easier to update or fix logic across the entire application.
*   **Increase Consistency:** Ensure standard approaches are used for common operations (e.g., UUID generation, date formatting).

See the [Backend Codebase Overview](../../overview.md) for the location of the `utils/` directory within the `src/` structure.

**2. Location (`src/core/utils/`)**
Utility modules will be organized within the `src/core/utils/` directory, categorized by their functional area (e.g., `datetime_utils.py`, `file_io_utils.py`).

**3. Key Utility Implementations**

*   **Endpoint Factory (`src/core/utils/crud_endpoint_factory.py`):**
    *   **Purpose:** Significantly reduce boilerplate code for standard CRUD (Create, Read, Update, Delete) [API endpoints](../../../api/api-architecture.md).
    *   **Functionality:** A factory function (or class) that takes a [Pydantic schema](../schemas/schemas-architecture.md), a [service](../services/services-architecture.md)/[repository](../repositories/repositories-architecture.md), and optionally custom dependencies, then dynamically generates and returns a [FastAPI `APIRouter`](../../../api/api-architecture.md) pre-configured with `GET`, `POST`, `PUT`, `DELETE` routes for a given resource.
    *   **Benefits:** Accelerates [API development](../../../api/api-architecture.md), ensures consistency in [endpoint definitions](../../../api/api-architecture.md), and simplifies the [API layer](../../../api/api-architecture.md).
    *   **Limitations:** Best suited for straightforward CRUD; complex business logic will still require custom endpoints.

    See the [API Layer Architecture](../../../api/api-architecture.md) for more details.

*   **UUIDv7 (Timestamp-Ordered):** This is a newer UUID standard designed to address the [database performance issues](../database/architecture.md) of UUIDv4. UUIDv7s incorporate a **Unix timestamp in milliseconds** as their most significant bits, followed by random bits. This makes them:
    *   **Lexicographically Sortable:** They naturally sort in time order, similar to how an auto-incrementing integer primary key would behave.
    *   **Database Friendly:** Their time-ordered nature greatly reduces index fragmentation, leading to much better write performance (fewer page splits) and often better read performance (data is clustered on disk by time).
    *   **Still Universally Unique:** They retain the probabilistic uniqueness guarantees of other UUID versions.

    See the [How-To: Transition to UUID](../../how-to/how-to_transition-to-uuid.md) for more context on UUID usage and potential migration.

*   **JSON Column Validation (`src/core/utils/json_validation.py`):**
    *   **Purpose:** Implement a custom mechanism for validating structured JSON data that is stored directly within [database columns](../database/architecture.md) (e.g., `JSONB` in PostgreSQL, or JSON string types in other databases).
    *   **Functionality:** Functions that:
        *   Accept a raw JSON string/object and a [Pydantic schema](../schemas/schemas-architecture.md).
        *   Use the [Pydantic schema](../schemas/schemas-architecture.md) to parse and validate the JSON data.
        *   Raise validation errors if the JSON does not conform to the schema.
    *   **Integration:** Can be used in [Repository](../repositories/repositories-architecture.md) or [Model layers](../models/models-architecture.md) (e.g., within SQLAlchemy custom types or `before_insert`/`before_update` event listeners) to ensure the integrity of JSON column data before persistence.

    See the [Schemas Layer Architecture](../schemas/schemas-architecture.md), [Repository Layer Architecture](../repositories/repositories-architecture.md), and [Model Layer Architecture](../models/models-architecture.md) for related documentation.

*   **Date/Time Utilities (`src/core/utils/datetime_utils.py`):**
    *   **Purpose:** Standardize common date, time, and timezone-aware operations.
    *   **Functionality:** Helper functions for:
        *   Getting current UTC timezone-aware datetimes (`utcnow_aware()`).
        *   Formatting datetimes to standard strings and parsing strings back to datetimes.
        *   Converting between timezones.
        *   Calculating time differences.
*   **File I/O Utilities (`src/core/utils/file_io_utils.py`):**
    *   **Purpose:** Abstract common [file system operations](file_operations.md), particularly useful for [import](../data_import/data_import-architecture.md)/[export features](../reports/reports-architecture.md), temporary file handling, or log file management.
    *   **Functionality:** Helper functions for:
        *   Securely saving files to disk.
        *   Creating and managing temporary files/directories.
        *   Validating file paths.
        *   Reading/writing specific file types (e.g., CSV, basic JSON file operations).

    See the [Data Import Layer Architecture](../data_import/data_import-architecture.md) and [Report Generation Architecture](../reports/reports-architecture.md) for related documentation.

*   **String Utilities (`src/core/utils/string_utils.py`):**
    *   **Purpose:** Provide common string manipulation functions.
    *   **Functionality:** Helpers for:
        *   Slugification (converting strings to URL-friendly slugs).
        *   Text sanitization.
        *   Hashing simple strings (non-cryptographic, for quick checks).
        *   Truncating or padding strings.
*   **Pagination & Filtering Utilities (`src/core/utils/query_utils.py` / `pagination_utils.py`):**
    *   **Purpose:** Standardize the application of pagination, sorting, and common filtering logic to [database queries](../database/architecture.md).
    *   **Functionality:** Functions that take [query parameters](../../../api/api-architecture.md) (e.g., `page`, `limit`, `sort_by`, `filter_by`) and apply them to SQLAlchemy query objects, simplifying the implementation of list [endpoints](../../../api/api-architecture.md).

    Can be used by [API layers](../../../api/api-architecture.md) or [Service layers](../services/services-architecture.md) to handle paginated data.

*   **Unit Conversion Utilities (`src/core/utils/unit_conversion_utils.py` - Specific to Engineering App):**
    *   **Purpose:** Provide helper functions for converting between different units of measurement (e.g., °C to °F, meters to feet, kW to BTU/hr).
    *   **Functionality:** Functions for specific conversions relevant to [heat tracing calculations](../calculations/calculations-architecture.md). This is a very domain-specific utility.

    See the [Calculations Layer Architecture](../calculations/calculations-architecture.md) for related documentation.

**4. Interaction with Other Layers**

*   **API Layer:** Heavily uses the Endpoint Factory, UUID utilities, and Pagination/Filtering utilities for building standardized [endpoints](../../../api/api-architecture.md).

    See the [API Layer Architecture](../../../api/api-architecture.md) for more details.

*   **Service Layer:** Utilizes Date/Time, File I/O, String, and potentially Unit Conversion utilities when performing business logic that involves these common operations.

    See the [Service Layer Architecture](../services/services-architecture.md) for more details.

*   **Repository Layer:** May use JSON Validation utilities when interacting with [database columns](../database/architecture.md) storing JSON data, and UUID utilities for ID management.

    See the [Repository Layer Architecture](../repositories/repositories-architecture.md) for more details.

*   **Core Schemas/Models:** UUID utilities can be used when defining ID fields. JSON validation can be triggered by custom [SQLAlchemy types](../database/architecture.md).

    See the [Schemas Layer Architecture](../schemas/schemas-architecture.md) and [Model Layer Architecture](../models/models-architecture.md) for more details.

*   **Consumed by various layers:** The [Utilities Layer](utils-architecture.md) is a supporting layer and is consumed by various other layers as needed. It generally does not consume other layers itself.

**5. Key Principles**

*   **DRY (Don't Repeat Yourself):** Minimize code duplication by centralizing common logic.
*   **Single Responsibility Principle:** Each utility function or module should have a clear, specific purpose.
*   **Pure Functions:** Ideally, utility functions should be pure, meaning they produce the same output for the same input and have no side effects, making them easy to test and reason about.
*   **Generality:** Designed to be broadly applicable across different parts of the application, not tied to specific business entities or workflows.
*   **Testability:** Each utility should be easily testable in isolation.
