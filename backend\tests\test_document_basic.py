# backend/tests/test_document_basic.py
"""
Basic tests for Document entity functionality.

This test verifies that the Document entity implementation works
without complex relationship dependencies.
"""

import pytest
from sqlalchemy.orm import Session

from core.models.documents import CalculationStandard
from core.schemas.document_schemas import (
    CalculationStandardCreateSchema,
    CalculationStandardReadSchema,
)


class TestDocumentBasic:
    """Test basic Document entity functionality."""

    def test_calculation_standard_model_creation(self, db_session: Session):
        """Test creating a CalculationStandard model directly."""
        # Create a calculation standard directly
        standard = CalculationStandard(
            name="Test Standard",
            standard_code="TEST-001",
            description="Test description",
            parameters_json='{"test": "value"}',
        )
        
        db_session.add(standard)
        db_session.commit()
        db_session.refresh(standard)
        
        assert standard.id is not None
        assert standard.name == "Test Standard"
        assert standard.standard_code == "TEST-001"
        assert standard.description == "Test description"
        assert standard.parameters_json == '{"test": "value"}'
        assert standard.created_at is not None
        assert standard.is_deleted is False

    def test_calculation_standard_schema_validation(self):
        """Test CalculationStandard schema validation."""
        # Test valid data
        valid_data = {
            "name": "Valid Standard",
            "standard_code": "VALID-001",
            "description": "Valid description",
            "parameters_json": '{"valid": "json"}',
        }
        
        schema = CalculationStandardCreateSchema(**valid_data)
        assert schema.name == "Valid Standard"
        assert schema.standard_code == "VALID-001"
        
        # Test that the schema can be converted to dict
        schema_dict = schema.model_dump()
        assert schema_dict["name"] == "Valid Standard"
        assert schema_dict["standard_code"] == "VALID-001"

    def test_calculation_standard_read_schema(self, db_session: Session):
        """Test CalculationStandard read schema conversion."""
        # Create a calculation standard
        standard = CalculationStandard(
            name="Read Test Standard",
            standard_code="RTS-001",
            description="Read test description",
        )
        
        db_session.add(standard)
        db_session.commit()
        db_session.refresh(standard)
        
        # Convert to read schema
        read_schema = CalculationStandardReadSchema.model_validate(standard)
        
        assert read_schema.id == standard.id
        assert read_schema.name == "Read Test Standard"
        assert read_schema.standard_code == "RTS-001"
        assert read_schema.description == "Read test description"
        assert read_schema.created_at is not None
        assert read_schema.is_deleted is False

    def test_calculation_standard_unique_constraints(self, db_session: Session):
        """Test that unique constraints work for CalculationStandard."""
        # Create first standard
        standard1 = CalculationStandard(
            name="Unique Test Standard",
            standard_code="UTS-001",
            description="First standard",
        )
        
        db_session.add(standard1)
        db_session.commit()
        
        # Try to create second standard with same code
        standard2 = CalculationStandard(
            name="Another Standard",
            standard_code="UTS-001",  # Same code
            description="Second standard",
        )
        
        db_session.add(standard2)
        
        # This should raise an integrity error
        with pytest.raises(Exception):  # Could be IntegrityError or similar
            db_session.commit()

    def test_calculation_standard_soft_delete(self, db_session: Session):
        """Test soft delete functionality for CalculationStandard."""
        # Create a calculation standard
        standard = CalculationStandard(
            name="Delete Test Standard",
            standard_code="DTS-001",
            description="Will be deleted",
        )
        
        db_session.add(standard)
        db_session.commit()
        db_session.refresh(standard)
        
        # Verify it's not deleted initially
        assert standard.is_deleted is False
        assert standard.deleted_at is None
        
        # Soft delete it
        from datetime import datetime, timezone
        standard.is_deleted = True
        standard.deleted_at = datetime.now(timezone.utc)
        
        db_session.commit()
        db_session.refresh(standard)
        
        # Verify it's marked as deleted
        assert standard.is_deleted is True
        assert standard.deleted_at is not None

    def test_calculation_standard_json_parameters(self, db_session: Session):
        """Test JSON parameters handling in CalculationStandard."""
        # Create standard with complex JSON parameters
        complex_params = {
            "safety_factor": 1.2,
            "max_temp_rating": 250,
            "min_bend_radius": 50,
            "voltage_ratings": [120, 240, 480, 600],
            "environmental_conditions": {
                "min_temp": -40,
                "max_temp": 85,
                "humidity_max": 95
            }
        }
        
        import json
        standard = CalculationStandard(
            name="JSON Test Standard",
            standard_code="JTS-001",
            description="Standard with complex JSON parameters",
            parameters_json=json.dumps(complex_params),
        )
        
        db_session.add(standard)
        db_session.commit()
        db_session.refresh(standard)
        
        # Verify JSON can be parsed back
        parsed_params = json.loads(standard.parameters_json)
        assert parsed_params["safety_factor"] == 1.2
        assert parsed_params["voltage_ratings"] == [120, 240, 480, 600]
        assert parsed_params["environmental_conditions"]["min_temp"] == -40

    def test_document_schema_enums(self):
        """Test that document schema enums work correctly."""
        from core.schemas.document_schemas import ImportType, DocumentType, FileFormat
        
        # Test ImportType enum
        assert ImportType.PIPE_DATA == "pipe_data"
        assert ImportType.VESSEL_DATA == "vessel_data"
        assert ImportType.COMPONENT_DATA == "component_data"
        assert ImportType.ELECTRICAL_DATA == "electrical_data"
        assert ImportType.SWITCHBOARD_DATA == "switchboard_data"
        
        # Test DocumentType enum
        assert DocumentType.HEAT_TRACING_REPORT == "heat_tracing_report"
        assert DocumentType.ELECTRICAL_REPORT == "electrical_report"
        assert DocumentType.SWITCHBOARD_REPORT == "switchboard_report"
        assert DocumentType.CALCULATION_SUMMARY == "calculation_summary"
        assert DocumentType.PROJECT_OVERVIEW == "project_overview"
        assert DocumentType.COMPLIANCE_REPORT == "compliance_report"
        
        # Test FileFormat enum
        assert FileFormat.PDF == "pdf"
        assert FileFormat.EXCEL == "excel"
        assert FileFormat.CSV == "csv"
        assert FileFormat.JSON == "json"

    def test_document_schema_validation_errors(self):
        """Test that document schema validation catches errors correctly."""
        from pydantic import ValidationError
        
        # Test invalid JSON in CalculationStandardCreateSchema
        with pytest.raises(ValidationError):
            CalculationStandardCreateSchema(
                name="Invalid JSON Standard",
                standard_code="IJS-001",
                parameters_json="invalid json string",
            )
        
        # Test empty name
        with pytest.raises(ValidationError):
            CalculationStandardCreateSchema(
                name="",
                standard_code="EMPTY-001",
            )
        
        # Test empty standard code
        with pytest.raises(ValidationError):
            CalculationStandardCreateSchema(
                name="Valid Name",
                standard_code="",
            )

    def test_document_models_exist(self):
        """Test that all document models can be imported and instantiated."""
        from core.models.documents import (
            ImportedDataRevision,
            ExportedDocument,
            CalculationStandard,
        )
        
        # Test that models can be instantiated (without saving to DB)
        revision = ImportedDataRevision(
            project_id=1,
            source_filename="test.xlsx",
            import_type="pipe_data",
            is_active_revision=True,
        )
        assert revision.source_filename == "test.xlsx"
        
        document = ExportedDocument(
            project_id=1,
            document_type="heat_tracing_report",
            filename="test.pdf",
            is_latest_revision=True,
        )
        assert document.filename == "test.pdf"
        
        standard = CalculationStandard(
            name="Test Standard",
            standard_code="TEST-001",
        )
        assert standard.name == "Test Standard"
