# backend/core/repositories/switchboard_repository.py
"""
Switchboard Repository

This module provides data access layer for Switchboard entities, extending the base
repository with switchboard-specific query methods and operations.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import and_, func, select, update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

try:
    from backend.config.logging_config import get_logger
    from backend.core.models.switchboard import (
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON>rComponent,
        Switchboard,
        SwitchboardComponent,
    )
    from backend.core.repositories.base_repository import BaseRepository
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.models.switchboard import (
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>omponent,
        Switchboard,
        SwitchboardComponent,
    )
    from core.repositories.base_repository import BaseRepository

# Initialize logger for this module
logger = get_logger(__name__)


class SwitchboardRepository(BaseRepository[Switchboard]):
    """
    Repository for Switchboard entity data access operations.

    Extends BaseRepository with switchboard-specific query methods and
    enhanced error handling for switchboard operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the Switchboard repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Switchboard)
        logger.debug("SwitchboardRepository initialized")

    def get_by_project_id(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[Switchboard]:
        """
        Get switchboards by project ID.

        Args:
            project_id: Project ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Switchboard]: List of switchboards in the project

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving switchboards for project {project_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} switchboards for project {project_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving switchboards for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "switchboard")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_by_voltage_level(
        self, project_id: int, voltage_level_v: int, skip: int = 0, limit: int = 100
    ) -> List[Switchboard]:
        """
        Get switchboards by voltage level within a project.

        Args:
            project_id: Project ID
            voltage_level_v: Voltage level in volts to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Switchboard]: List of switchboards with specified voltage level

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching for switchboards with voltage level: {voltage_level_v}V in project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.voltage_level_v == voltage_level_v,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} switchboards with voltage level: {voltage_level_v}V"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for switchboards by voltage level {voltage_level_v}: {e}"
            )
            self._handle_db_exception(e, "switchboard")
            raise

    def get_by_type(
        self, project_id: int, switchboard_type: str, skip: int = 0, limit: int = 100
    ) -> List[Switchboard]:
        """
        Get switchboards by type within a project.

        Args:
            project_id: Project ID
            switchboard_type: Switchboard type to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Switchboard]: List of switchboards of specified type

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Searching for switchboards with type: {switchboard_type} in project {project_id}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.project_id == project_id,
                        self.model.type == switchboard_type,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Found {len(results)} switchboards with type: {switchboard_type}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error searching for switchboards by type {switchboard_type}: {e}"
            )
            self._handle_db_exception(e, "switchboard")
            raise

    def count_by_project(self, project_id: int) -> int:
        """
        Count total number of active switchboards in a project.

        Args:
            project_id: Project ID

        Returns:
            int: Number of active switchboards

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting switchboards for project {project_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.project_id == project_id, self.model.is_deleted == False
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total switchboards in project {project_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting switchboards for project {project_id}: {e}"
            )
            self._handle_db_exception(e, "switchboard")
            raise

    def update(
        self, switchboard_id: int, update_data: Dict[str, Any]
    ) -> Optional[Switchboard]:
        """
        Update switchboard with given data.

        Args:
            switchboard_id: ID of switchboard to update
            update_data: Dictionary of fields to update

        Returns:
            Optional[Switchboard]: Updated switchboard or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Updating switchboard {switchboard_id} with data: {list(update_data.keys())}"
        )

        try:
            # First check if switchboard exists
            existing_switchboard = self.get_by_id(switchboard_id)
            if existing_switchboard is None:
                logger.debug(f"Switchboard {switchboard_id} not found for update")
                return None

            # Update the switchboard
            stmt = (
                update(self.model)
                .where(self.model.id == switchboard_id)
                .values(**update_data)
            )
            self.db_session.execute(stmt)

            # Return updated switchboard
            updated_switchboard = self.get_by_id(switchboard_id)
            logger.debug(f"Switchboard {switchboard_id} updated successfully")
            return updated_switchboard

        except SQLAlchemyError as e:
            logger.error(f"Database error updating switchboard {switchboard_id}: {e}")
            self._handle_db_exception(e, "switchboard")
            raise

    def soft_delete(
        self, switchboard_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete a switchboard.

        Args:
            switchboard_id: ID of switchboard to delete
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if deletion was successful, False if switchboard not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting switchboard {switchboard_id}")

        try:
            from datetime import datetime, timezone

            update_data = {
                "is_deleted": True,
                "deleted_at": datetime.now(timezone.utc),
                "deleted_by_user_id": deleted_by_user_id,
            }

            stmt = (
                update(self.model)
                .where(
                    and_(
                        self.model.id == switchboard_id, self.model.is_deleted == False
                    )
                )
                .values(**update_data)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"Switchboard {switchboard_id} soft deleted successfully")
                return True
            else:
                logger.debug(
                    f"Switchboard {switchboard_id} not found or already deleted"
                )
                return False

        except SQLAlchemyError as e:
            logger.error(
                f"Database error soft deleting switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "switchboard")
            raise


class FeederRepository(BaseRepository[Feeder]):
    """
    Repository for Feeder entity data access operations.

    Extends BaseRepository with feeder-specific query methods and
    enhanced error handling for feeder operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the Feeder repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Feeder)
        logger.debug("FeederRepository initialized")

    def get_by_switchboard_id(
        self, switchboard_id: int, skip: int = 0, limit: int = 100
    ) -> List[Feeder]:
        """
        Get feeders by switchboard ID.

        Args:
            switchboard_id: Switchboard ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Feeder]: List of feeders in the switchboard

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving feeders for switchboard {switchboard_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.switchboard_id == switchboard_id,
                        self.model.is_deleted == False,
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} feeders for switchboard {switchboard_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving feeders for switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "feeder")
            raise

    def count_by_switchboard(self, switchboard_id: int) -> int:
        """
        Count total number of active feeders in a switchboard.

        Args:
            switchboard_id: Switchboard ID

        Returns:
            int: Number of active feeders

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting feeders for switchboard {switchboard_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                and_(
                    self.model.switchboard_id == switchboard_id,
                    self.model.is_deleted == False,
                )
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total feeders in switchboard {switchboard_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting feeders for switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "feeder")
            raise


class SwitchboardComponentRepository(BaseRepository[SwitchboardComponent]):
    """
    Repository for SwitchboardComponent entity data access operations.

    Extends BaseRepository with switchboard component-specific query methods and
    enhanced error handling for switchboard component operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the SwitchboardComponent repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, SwitchboardComponent)
        logger.debug("SwitchboardComponentRepository initialized")

    def get_by_switchboard_id(
        self, switchboard_id: int, skip: int = 0, limit: int = 100
    ) -> List[SwitchboardComponent]:
        """
        Get switchboard components by switchboard ID.

        Args:
            switchboard_id: Switchboard ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[SwitchboardComponent]: List of components in the switchboard

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving components for switchboard {switchboard_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(self.model.switchboard_id == switchboard_id)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.position, self.model.id)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} components for switchboard {switchboard_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving components for switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "switchboard_component")
            raise

    def get_by_component_id(
        self, component_id: int, skip: int = 0, limit: int = 100
    ) -> List[SwitchboardComponent]:
        """
        Get switchboard components by component ID.

        Args:
            component_id: Component ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[SwitchboardComponent]: List of switchboard installations for the component

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving switchboard installations for component {component_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(self.model.component_id == component_id)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.switchboard_id, self.model.position)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(
                f"Retrieved {len(results)} switchboard installations for component {component_id}"
            )
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving switchboard installations for component {component_id}: {e}"
            )
            self._handle_db_exception(e, "switchboard_component")
            raise

    def count_by_switchboard(self, switchboard_id: int) -> int:
        """
        Count total number of components in a switchboard.

        Args:
            switchboard_id: Switchboard ID

        Returns:
            int: Number of components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting components for switchboard {switchboard_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                self.model.switchboard_id == switchboard_id
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total components in switchboard {switchboard_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting components for switchboard {switchboard_id}: {e}"
            )
            self._handle_db_exception(e, "switchboard_component")
            raise


class FeederComponentRepository(BaseRepository[FeederComponent]):
    """
    Repository for FeederComponent entity data access operations.

    Extends BaseRepository with feeder component-specific query methods and
    enhanced error handling for feeder component operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the FeederComponent repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, FeederComponent)
        logger.debug("FeederComponentRepository initialized")

    def get_by_feeder_id(
        self, feeder_id: int, skip: int = 0, limit: int = 100
    ) -> List[FeederComponent]:
        """
        Get feeder components by feeder ID.

        Args:
            feeder_id: Feeder ID to filter by
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[FeederComponent]: List of components in the feeder

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving components for feeder {feeder_id}: skip={skip}, limit={limit}"
        )

        try:
            stmt = (
                select(self.model)
                .where(self.model.feeder_id == feeder_id)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.position, self.model.id)
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} components for feeder {feeder_id}")
            return results

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving components for feeder {feeder_id}: {e}"
            )
            self._handle_db_exception(e, "feeder_component")
            raise

    def count_by_feeder(self, feeder_id: int) -> int:
        """
        Count total number of components in a feeder.

        Args:
            feeder_id: Feeder ID

        Returns:
            int: Number of components

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Counting components for feeder {feeder_id}")

        try:
            stmt = select(func.count(self.model.id)).where(
                self.model.feeder_id == feeder_id
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total components in feeder {feeder_id}: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(
                f"Database error counting components for feeder {feeder_id}: {e}"
            )
            self._handle_db_exception(e, "feeder_component")
            raise
