# backend/core/calculations/__init__.py
"""
Calculations Layer - Engineering calculations and algorithms.

This module provides the core engineering calculations required for heat tracing design,
including heat loss calculations, electrical sizing, and circuit design algorithms.

The calculations layer is organized into specialized sub-packages:
- heat_loss: Heat loss calculations for pipes and vessels
- electrical_sizing: Cable sizing and electrical calculations  
- circuit_design: Circuit breaker sizing and control logic
- common_properties: Material properties and lookup tables
- utils: Calculation utilities and helpers

All calculations follow strict error handling patterns and integrate with
the standards validation layer.
"""

from .calculation_service import CalculationService

__all__ = [
    "CalculationService",
]
