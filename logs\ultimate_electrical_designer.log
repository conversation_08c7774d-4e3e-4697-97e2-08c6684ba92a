2025-05-29 20:13:08 - ultimate_electrical_designer.test_module - INFO - Test log message from module logger (<string>:9)
2025-05-29 20:13:08 - ultimate_electrical_designer - INFO - Test log message from app logger (<string>:10)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Starting database initialization... (initialization.py:43)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.engine - INFO - Creating database engine for environment: development (engine.py:53)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:70)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Database engine created successfully (initialization.py:48)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Session factory initialized (initialization.py:52)
2025-05-29 20:13:08 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Running Alembic migrations... (initialization.py:107)
2025-05-29 20:14:32 - ultimate_electrical_designer.test - INFO - Testing logging configuration (<string>:4)
2025-05-29 20:16:28 - ultimate_electrical_designer.test_module - INFO - Test log message from module logger (final_verification.py:22)
2025-05-29 20:16:28 - ultimate_electrical_designer - INFO - Test log message from app logger (final_verification.py:26)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Starting database initialization... (initialization.py:43)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.engine - INFO - Creating database engine for environment: development (engine.py:53)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:70)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Database engine created successfully (initialization.py:48)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Session factory initialized (initialization.py:52)
2025-05-29 20:16:28 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Running Alembic migrations... (initialization.py:107)
2025-05-29 20:17:25 - ultimate_electrical_designer.test_module - INFO - Module logger test (<string>:9)
2025-05-29 20:17:25 - ultimate_electrical_designer - INFO - App logger test (<string>:10)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Starting database initialization... (initialization.py:43)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.engine - INFO - Creating database engine for environment: development (engine.py:53)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.engine - INFO - Database engine created successfully using: SQLite (engine.py:70)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Database engine created successfully (initialization.py:48)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Session factory initialized (initialization.py:52)
2025-05-29 20:17:26 - ultimate_electrical_designer.backend.core.database.initialization - INFO - Running Alembic migrations... (initialization.py:107)
2025-05-29 21:04:41 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:70)
2025-05-29 21:04:41 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) created successfully (project_service.py:86)
2025-05-29 21:06:20 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-05-29 21:06:20 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-05-29 21:06:44 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-05-29 21:06:44 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) created successfully (project_service.py:90)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Project' (TEST-001) (project_service.py:72)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - ERROR - Unexpected error creating project 'Test Project': Input validation failed. (project_service.py:133)
Traceback (most recent call last):
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 78, in create_project
    self._validate_project_creation(project_data)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "D:\Projects\zzz_ultimate-electrical-designer\backend\core\services\project_service.py", line 443, in _validate_project_creation
    raise DataValidationError(
    ...<3 lines>...
    )
backend.core.errors.exceptions.DataValidationError: Input validation failed.
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to create new project: 'Test Heat Tracing Project' (HT-TEST-001) (project_service.py:72)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - WARNING - Duplicate project detected: Test Heat Tracing Project or HT-TEST-001 (project_service.py:99)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - WARNING - Project not found: TEST-001 (project_service.py:170)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to update project: 1 (project_service.py:210)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Updated Test Project' (ID: 1) updated successfully (project_service.py:234)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Attempting to delete project: 1 (project_service.py:288)
2025-05-29 21:06:57 - ultimate_electrical_designer.backend.core.services.project_service - INFO - Project 'Test Heat Tracing Project' (ID: 1) deleted successfully (project_service.py:308)
