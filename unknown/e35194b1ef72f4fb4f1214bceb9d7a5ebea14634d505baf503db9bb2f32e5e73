[tool:pytest]
# Pytest configuration for Ultimate Electrical Designer Backend

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=backend/core
    --cov=backend/api
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=85

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: API endpoint tests
    repository: Repository layer tests
    service: Service layer tests
    schema: Schema validation tests

# Minimum version
minversion = 6.0

# Test session configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
