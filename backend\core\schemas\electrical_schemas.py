# backend/core/schemas/electrical_schemas.py
"""
Pydantic schemas for Electrical entities validation, serialization, and deserialization.

This module defines the data contracts for Electrical-related operations including:
- ElectricalNode schemas: For electrical connection points and nodes
- CableRoute schemas: For cable routing between electrical nodes
- CableSegment schemas: For individual cable segments with specifications
- LoadCalculation schemas: For electrical load calculations and power requirements
- VoltageDropCalculation schemas: For voltage drop calculations and cable sizing
- Calculation schemas: For integration with calculations layer
- Validation schemas: For electrical engineering constraints and business logic
"""

import json
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from ..models.enums import (
    CableInstallationMethod,
    ElectricalNodeType,
)
from .base import BaseSoftDeleteSchema, PaginatedResponseSchema

# ============================================================================
# CALCULATION INTEGRATION SCHEMAS
# ============================================================================


class CableSizingCalculationInputSchema(BaseModel):
    """Schema for cable sizing calculation inputs."""

    required_power_kw: float = Field(..., gt=0, description="Required power in kW")
    cable_length_m: float = Field(..., gt=0, description="Cable length in meters")
    supply_voltage_v: float = Field(..., gt=0, description="Supply voltage in volts")
    ambient_temperature_c: float = Field(
        25.0, ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    installation_method: CableInstallationMethod = Field(
        ..., description="Cable installation method"
    )
    cable_type: Optional[str] = Field(
        None, description="Preferred cable type (optional)"
    )
    max_voltage_drop_percent: float = Field(
        5.0, gt=0, le=50, description="Maximum allowed voltage drop percentage"
    )
    power_factor: float = Field(
        1.0, gt=0, le=1, description="Power factor (default 1.0 for resistive loads)"
    )
    safety_factor: float = Field(
        1.2, ge=1, le=3, description="Safety factor for cable sizing"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "required_power_kw": 2.5,
                "cable_length_m": 100.0,
                "supply_voltage_v": 240.0,
                "ambient_temperature_c": 25.0,
                "installation_method": "CABLE_TRAY",
                "cable_type": "self-regulating",
                "max_voltage_drop_percent": 5.0,
                "power_factor": 1.0,
                "safety_factor": 1.2,
            }
        }
    )


class CableSizingCalculationResultSchema(BaseModel):
    """Schema for cable sizing calculation results."""

    recommended_cable_type: str = Field(..., description="Recommended cable type")
    cable_power_per_meter: float = Field(
        ..., description="Cable power per meter in W/m"
    )
    total_cable_length: float = Field(..., description="Total cable length in meters")
    current_draw: float = Field(..., description="Current draw in amperes")
    voltage_drop: float = Field(..., description="Voltage drop in volts")
    voltage_drop_percent: float = Field(..., description="Voltage drop percentage")
    power_density: float = Field(..., description="Power density in W/m²")
    is_compliant: bool = Field(..., description="Meets voltage drop requirements")
    safety_margin_percent: float = Field(..., description="Safety margin percentage")
    calculation_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Calculation metadata"
    )


class VoltageDropCalculationInputSchema(BaseModel):
    """Schema for voltage drop calculation inputs."""

    supply_voltage_v: float = Field(..., gt=0, description="Supply voltage in volts")
    load_current_a: float = Field(..., gt=0, description="Load current in amperes")
    cable_length_m: float = Field(..., gt=0, description="Cable length in meters")
    cable_resistance_ohm_per_m: float = Field(
        ..., ge=0, description="Cable resistance per meter in ohm/m"
    )
    cable_reactance_ohm_per_m: float = Field(
        0.0, ge=0, description="Cable reactance per meter in ohm/m"
    )
    power_factor: float = Field(
        1.0, gt=0, le=1, description="Power factor (default 1.0)"
    )
    ambient_temperature_c: float = Field(
        25.0, ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    derating_factor: float = Field(
        1.0, gt=0, le=1, description="Derating factor for temperature/grouping"
    )
    max_voltage_drop_percent: float = Field(
        5.0, gt=0, le=50, description="Maximum allowed voltage drop percentage"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "supply_voltage_v": 240.0,
                "load_current_a": 10.4,
                "cable_length_m": 100.0,
                "cable_resistance_ohm_per_m": 0.0184,
                "cable_reactance_ohm_per_m": 0.0,
                "power_factor": 1.0,
                "ambient_temperature_c": 25.0,
                "derating_factor": 1.0,
                "max_voltage_drop_percent": 5.0,
            }
        }
    )


class VoltageDropCalculationResultSchema(BaseModel):
    """Schema for voltage drop calculation results."""

    calculated_voltage_drop_v: float = Field(..., description="Voltage drop in volts")
    calculated_voltage_drop_percent: float = Field(
        ..., description="Voltage drop percentage"
    )
    calculated_power_loss_w: float = Field(..., description="Power loss in watts")
    calculated_efficiency_percent: float = Field(
        ..., description="Cable efficiency percentage"
    )
    is_compliant: bool = Field(..., description="Meets voltage drop requirements")
    compliance_margin_percent: float = Field(
        ..., description="Compliance margin percentage"
    )
    calculation_method: str = Field(..., description="Calculation method used")
    calculation_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Calculation metadata"
    )


class ElectricalStandardsValidationInputSchema(BaseModel):
    """Schema for electrical standards validation inputs."""

    cable_sizing_result: CableSizingCalculationResultSchema
    voltage_drop_result: VoltageDropCalculationResultSchema
    design_parameters: Dict[str, Any] = Field(default_factory=dict)
    project_standards: List[str] = Field(default_factory=list)
    hazardous_area_zone: Optional[str] = Field(None, description="Hazardous area zone")
    gas_group: Optional[str] = Field(None, description="Gas group classification")
    temperature_class: Optional[str] = Field(None, description="Temperature class")
    installation_environment: Optional[str] = Field(
        None, description="Installation environment"
    )


class ElectricalStandardsValidationResultSchema(BaseModel):
    """Schema for electrical standards validation results."""

    is_compliant: bool = Field(..., description="Overall compliance status")
    standard: str = Field(..., description="Applied standard")
    violations: List[str] = Field(
        default_factory=list, description="Compliance violations"
    )
    warnings: List[str] = Field(default_factory=list, description="Compliance warnings")
    applied_factors: Dict[str, float] = Field(
        default_factory=dict, description="Applied safety factors"
    )
    recommendations: List[str] = Field(
        default_factory=list, description="Design recommendations"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Validation metadata"
    )


# ============================================================================
# ELECTRICAL NODE SCHEMAS
# ============================================================================


class ElectricalNodeBaseSchema(BaseModel):
    """Base schema for ElectricalNode with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Electrical node name/identifier"
    )
    node_type: ElectricalNodeType = Field(..., description="Electrical node type")
    location_description: Optional[str] = Field(
        None, max_length=200, description="Location description"
    )
    voltage_v: Optional[float] = Field(None, gt=0, description="Voltage in volts")
    power_capacity_kva: Optional[float] = Field(
        None, gt=0, description="Power capacity in kVA"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate electrical node name."""
        if not v or not v.strip():
            raise ValueError("Electrical node name cannot be empty")
        return v.strip()


class ElectricalNodeCreateSchema(ElectricalNodeBaseSchema):
    """Schema for creating a new electrical node."""

    project_id: int = Field(..., description="Project ID")
    related_switchboard_id: Optional[int] = Field(
        None, description="Related switchboard ID"
    )
    related_feeder_id: Optional[int] = Field(None, description="Related feeder ID")
    related_control_circuit_id: Optional[int] = Field(
        None, description="Related control circuit ID"
    )
    related_pipe_id: Optional[int] = Field(None, description="Related pipe ID")
    related_vessel_id: Optional[int] = Field(None, description="Related vessel ID")
    related_component_id: Optional[int] = Field(
        None, description="Related component ID"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Main Switchboard Incoming",
                "project_id": 1,
                "node_type": "SWITCHBOARD_INCOMING",
                "location_description": "Main electrical room",
                "voltage_v": 415.0,
                "power_capacity_kva": 100.0,
                "related_switchboard_id": 1,
            }
        }
    )


class ElectricalNodeUpdateSchema(BaseModel):
    """Schema for updating an existing electrical node."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="Electrical node name/identifier",
    )
    node_type: Optional[ElectricalNodeType] = Field(
        None, description="Electrical node type"
    )
    location_description: Optional[str] = Field(
        None, max_length=200, description="Location description"
    )
    voltage_v: Optional[float] = Field(None, gt=0, description="Voltage in volts")
    power_capacity_kva: Optional[float] = Field(
        None, gt=0, description="Power capacity in kVA"
    )
    related_switchboard_id: Optional[int] = Field(
        None, description="Related switchboard ID"
    )
    related_feeder_id: Optional[int] = Field(None, description="Related feeder ID")
    related_control_circuit_id: Optional[int] = Field(
        None, description="Related control circuit ID"
    )
    related_pipe_id: Optional[int] = Field(None, description="Related pipe ID")
    related_vessel_id: Optional[int] = Field(None, description="Related vessel ID")
    related_component_id: Optional[int] = Field(
        None, description="Related component ID"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(ElectricalNodeBaseSchema.validate_name)


class ElectricalNodeReadSchema(ElectricalNodeBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying electrical node data."""

    project_id: int = Field(..., description="Project ID")
    related_switchboard_id: Optional[int] = Field(
        None, description="Related switchboard ID"
    )
    related_feeder_id: Optional[int] = Field(None, description="Related feeder ID")
    related_control_circuit_id: Optional[int] = Field(
        None, description="Related control circuit ID"
    )
    related_pipe_id: Optional[int] = Field(None, description="Related pipe ID")
    related_vessel_id: Optional[int] = Field(None, description="Related vessel ID")
    related_component_id: Optional[int] = Field(
        None, description="Related component ID"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Main Switchboard Incoming",
                "project_id": 1,
                "node_type": "SWITCHBOARD_INCOMING",
                "location_description": "Main electrical room",
                "voltage_v": 415.0,
                "power_capacity_kva": 100.0,
                "related_switchboard_id": 1,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ElectricalNodeSummarySchema(BaseModel):
    """Lightweight schema for electrical node listings."""

    id: int = Field(..., description="Electrical node ID")
    name: str = Field(..., description="Electrical node name")
    project_id: int = Field(..., description="Project ID")
    node_type: ElectricalNodeType = Field(..., description="Electrical node type")
    voltage_v: Optional[float] = Field(None, description="Voltage in volts")
    power_capacity_kva: Optional[float] = Field(
        None, description="Power capacity in kVA"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# CABLE ROUTE SCHEMAS
# ============================================================================


class CableRouteBaseSchema(BaseModel):
    """Base schema for CableRoute with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Cable route name/identifier"
    )
    length_m: float = Field(..., gt=0, description="Cable route length in meters")
    number_of_runs: int = Field(1, ge=1, le=10, description="Number of cable runs")
    installation_method: CableInstallationMethod = Field(
        ..., description="Cable installation method"
    )
    max_ambient_temp_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Maximum ambient temperature in Celsius"
    )
    min_ambient_temp_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Minimum ambient temperature in Celsius"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate cable route name."""
        if not v or not v.strip():
            raise ValueError("Cable route name cannot be empty")
        return v.strip()

    @model_validator(mode="after")
    def validate_temperature_range(self):
        """Validate temperature range."""
        if (
            self.max_ambient_temp_c is not None
            and self.min_ambient_temp_c is not None
            and self.max_ambient_temp_c <= self.min_ambient_temp_c
        ):
            raise ValueError(
                "Maximum ambient temperature must be higher than minimum ambient temperature"
            )
        return self


class CableRouteCreateSchema(CableRouteBaseSchema):
    """Schema for creating a new cable route."""

    project_id: int = Field(..., description="Project ID")
    from_node_id: int = Field(..., description="From electrical node ID")
    to_node_id: int = Field(..., description="To electrical node ID")
    cable_component_id: int = Field(..., description="Cable component ID")

    @model_validator(mode="after")
    def validate_different_nodes(self):
        """Validate that from and to nodes are different."""
        if self.from_node_id == self.to_node_id:
            raise ValueError("From node and to node must be different")
        return self

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Main Feed to HT Panel",
                "project_id": 1,
                "from_node_id": 1,
                "to_node_id": 2,
                "cable_component_id": 25,
                "length_m": 150.0,
                "number_of_runs": 1,
                "installation_method": "CABLE_TRAY",
                "max_ambient_temp_c": 40.0,
                "min_ambient_temp_c": -10.0,
            }
        }
    )


class CableRouteUpdateSchema(BaseModel):
    """Schema for updating an existing cable route."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Cable route name/identifier"
    )
    from_node_id: Optional[int] = Field(None, description="From electrical node ID")
    to_node_id: Optional[int] = Field(None, description="To electrical node ID")
    cable_component_id: Optional[int] = Field(None, description="Cable component ID")
    length_m: Optional[float] = Field(
        None, gt=0, description="Cable route length in meters"
    )
    number_of_runs: Optional[int] = Field(
        None, ge=1, le=10, description="Number of cable runs"
    )
    installation_method: Optional[CableInstallationMethod] = Field(
        None, description="Cable installation method"
    )
    max_ambient_temp_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Maximum ambient temperature in Celsius"
    )
    min_ambient_temp_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Minimum ambient temperature in Celsius"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(CableRouteBaseSchema.validate_name)


class CableRouteReadSchema(CableRouteBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying cable route data."""

    project_id: int = Field(..., description="Project ID")
    from_node_id: int = Field(..., description="From electrical node ID")
    to_node_id: int = Field(..., description="To electrical node ID")
    cable_component_id: int = Field(..., description="Cable component ID")
    calculated_voltage_drop_v: Optional[float] = Field(
        None, description="Calculated voltage drop in volts"
    )
    calculated_current_capacity_a: Optional[float] = Field(
        None, description="Calculated current capacity in amperes"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Main Feed to HT Panel",
                "project_id": 1,
                "from_node_id": 1,
                "to_node_id": 2,
                "cable_component_id": 25,
                "length_m": 150.0,
                "number_of_runs": 1,
                "installation_method": "CABLE_TRAY",
                "calculated_voltage_drop_v": 8.5,
                "calculated_current_capacity_a": 32.0,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class CableRouteSummarySchema(BaseModel):
    """Lightweight schema for cable route listings."""

    id: int = Field(..., description="Cable route ID")
    name: str = Field(..., description="Cable route name")
    project_id: int = Field(..., description="Project ID")
    from_node_id: int = Field(..., description="From electrical node ID")
    to_node_id: int = Field(..., description="To electrical node ID")
    length_m: float = Field(..., description="Cable route length in meters")
    calculated_voltage_drop_v: Optional[float] = Field(
        None, description="Calculated voltage drop in volts"
    )
    installation_method: CableInstallationMethod = Field(
        ..., description="Cable installation method"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# CABLE SEGMENT SCHEMAS
# ============================================================================


class CableSegmentBaseSchema(BaseModel):
    """Base schema for CableSegment with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Cable segment name/identifier"
    )
    segment_order: int = Field(1, ge=1, description="Segment order in route")
    length_m: float = Field(..., gt=0, description="Segment length in meters")
    installation_method: CableInstallationMethod = Field(
        ..., description="Cable installation method"
    )
    ambient_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    ground_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ground temperature in Celsius"
    )
    burial_depth_m: Optional[float] = Field(
        None, ge=0, description="Burial depth in meters"
    )
    conductor_size_mm2: Optional[float] = Field(
        None, gt=0, description="Conductor size in mm²"
    )
    insulation_type: Optional[str] = Field(
        None, max_length=50, description="Insulation type"
    )
    sheath_type: Optional[str] = Field(None, max_length=50, description="Sheath type")
    armour_type: Optional[str] = Field(None, max_length=50, description="Armour type")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate cable segment name."""
        if not v or not v.strip():
            raise ValueError("Cable segment name cannot be empty")
        return v.strip()


class CableSegmentCreateSchema(CableSegmentBaseSchema):
    """Schema for creating a new cable segment."""

    project_id: int = Field(..., description="Project ID")
    cable_route_id: int = Field(..., description="Cable route ID")
    cable_component_id: int = Field(..., description="Cable component ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Segment 1 - Underground",
                "project_id": 1,
                "cable_route_id": 1,
                "cable_component_id": 25,
                "segment_order": 1,
                "length_m": 50.0,
                "installation_method": "DIRECT_BURIED",
                "ambient_temperature_c": 25.0,
                "ground_temperature_c": 20.0,
                "burial_depth_m": 0.8,
                "conductor_size_mm2": 2.5,
                "insulation_type": "XLPE",
                "sheath_type": "PVC",
                "armour_type": "SWA",
            }
        }
    )


class CableSegmentUpdateSchema(BaseModel):
    """Schema for updating an existing cable segment."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Cable segment name/identifier"
    )
    cable_component_id: Optional[int] = Field(None, description="Cable component ID")
    segment_order: Optional[int] = Field(
        None, ge=1, description="Segment order in route"
    )
    length_m: Optional[float] = Field(
        None, gt=0, description="Segment length in meters"
    )
    installation_method: Optional[CableInstallationMethod] = Field(
        None, description="Cable installation method"
    )
    ambient_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    ground_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ground temperature in Celsius"
    )
    burial_depth_m: Optional[float] = Field(
        None, ge=0, description="Burial depth in meters"
    )
    conductor_size_mm2: Optional[float] = Field(
        None, gt=0, description="Conductor size in mm²"
    )
    insulation_type: Optional[str] = Field(
        None, max_length=50, description="Insulation type"
    )
    sheath_type: Optional[str] = Field(None, max_length=50, description="Sheath type")
    armour_type: Optional[str] = Field(None, max_length=50, description="Armour type")

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(CableSegmentBaseSchema.validate_name)


class CableSegmentReadSchema(CableSegmentBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying cable segment data."""

    project_id: int = Field(..., description="Project ID")
    cable_route_id: int = Field(..., description="Cable route ID")
    cable_component_id: int = Field(..., description="Cable component ID")
    calculated_resistance_ohm_per_m: Optional[float] = Field(
        None, description="Calculated resistance in ohm/m"
    )
    calculated_reactance_ohm_per_m: Optional[float] = Field(
        None, description="Calculated reactance in ohm/m"
    )
    calculated_current_capacity_a: Optional[float] = Field(
        None, description="Calculated current capacity in amperes"
    )
    calculated_voltage_drop_v: Optional[float] = Field(
        None, description="Calculated voltage drop in volts"
    )
    calculated_power_loss_w: Optional[float] = Field(
        None, description="Calculated power loss in watts"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Segment 1 - Underground",
                "project_id": 1,
                "cable_route_id": 1,
                "cable_component_id": 25,
                "segment_order": 1,
                "length_m": 50.0,
                "installation_method": "DIRECT_BURIED",
                "calculated_resistance_ohm_per_m": 0.0184,
                "calculated_current_capacity_a": 32.0,
                "calculated_voltage_drop_v": 4.2,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class CableSegmentSummarySchema(BaseModel):
    """Lightweight schema for cable segment listings."""

    id: int = Field(..., description="Cable segment ID")
    name: str = Field(..., description="Cable segment name")
    cable_route_id: int = Field(..., description="Cable route ID")
    segment_order: int = Field(..., description="Segment order in route")
    length_m: float = Field(..., description="Segment length in meters")
    installation_method: CableInstallationMethod = Field(
        ..., description="Cable installation method"
    )
    calculated_voltage_drop_v: Optional[float] = Field(
        None, description="Calculated voltage drop in volts"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# LOAD CALCULATION SCHEMAS
# ============================================================================


class LoadCalculationBaseSchema(BaseModel):
    """Base schema for LoadCalculation with common fields."""

    name: str = Field(
        ...,
        min_length=3,
        max_length=100,
        description="Load calculation name/identifier",
    )
    load_type: str = Field(
        ..., min_length=1, description="Load type (e.g., heat_tracing, motor, lighting)"
    )
    load_description: Optional[str] = Field(
        None, max_length=200, description="Load description"
    )
    rated_power_kw: float = Field(..., gt=0, description="Rated power in kW")
    rated_voltage_v: float = Field(..., gt=0, description="Rated voltage in volts")
    rated_current_a: float = Field(..., gt=0, description="Rated current in amperes")
    power_factor: float = Field(1.0, gt=0, le=1, description="Power factor")
    efficiency_percent: float = Field(
        100.0, gt=0, le=100, description="Efficiency percentage"
    )
    operating_hours_per_day: float = Field(
        24.0, ge=0, le=24, description="Operating hours per day"
    )
    load_factor_percent: float = Field(
        100.0, gt=0, le=100, description="Load factor percentage"
    )
    diversity_factor: float = Field(1.0, gt=0, description="Diversity factor")
    safety_factor: float = Field(1.2, ge=1, description="Safety factor")
    design_margin_percent: float = Field(
        20.0, ge=0, description="Design margin percentage"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate load calculation name."""
        if not v or not v.strip():
            raise ValueError("Load calculation name cannot be empty")
        return v.strip()

    @model_validator(mode="after")
    def validate_electrical_parameters(self):
        """Validate electrical parameters consistency."""
        # Check if power, voltage, and current are consistent (P = V * I * PF)
        expected_power = (
            self.rated_voltage_v * self.rated_current_a * self.power_factor
        ) / 1000
        power_tolerance = 0.1  # 10% tolerance
        if abs(self.rated_power_kw - expected_power) > (
            expected_power * power_tolerance
        ):
            raise ValueError(
                f"Power, voltage, and current are not consistent. "
                f"Expected power: {expected_power:.2f}kW, provided: {self.rated_power_kw}kW"
            )
        return self


class LoadCalculationCreateSchema(LoadCalculationBaseSchema):
    """Schema for creating a new load calculation."""

    project_id: int = Field(..., description="Project ID")
    electrical_node_id: int = Field(..., description="Electrical node ID")
    related_pipe_id: Optional[int] = Field(None, description="Related pipe ID")
    related_vessel_id: Optional[int] = Field(None, description="Related vessel ID")
    related_htcircuit_id: Optional[int] = Field(
        None, description="Related HT circuit ID"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Heat Tracing Load HTC-001",
                "project_id": 1,
                "electrical_node_id": 2,
                "load_type": "heat_tracing",
                "load_description": "Heat tracing for main process line",
                "rated_power_kw": 2.5,
                "rated_voltage_v": 240.0,
                "rated_current_a": 10.4,
                "power_factor": 1.0,
                "efficiency_percent": 95.0,
                "operating_hours_per_day": 24.0,
                "load_factor_percent": 80.0,
                "diversity_factor": 0.9,
                "safety_factor": 1.2,
                "design_margin_percent": 20.0,
                "related_pipe_id": 1,
            }
        }
    )


class LoadCalculationUpdateSchema(BaseModel):
    """Schema for updating an existing load calculation."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="Load calculation name/identifier",
    )
    electrical_node_id: Optional[int] = Field(None, description="Electrical node ID")
    load_type: Optional[str] = Field(None, min_length=1, description="Load type")
    load_description: Optional[str] = Field(
        None, max_length=200, description="Load description"
    )
    rated_power_kw: Optional[float] = Field(None, gt=0, description="Rated power in kW")
    rated_voltage_v: Optional[float] = Field(
        None, gt=0, description="Rated voltage in volts"
    )
    rated_current_a: Optional[float] = Field(
        None, gt=0, description="Rated current in amperes"
    )
    power_factor: Optional[float] = Field(None, gt=0, le=1, description="Power factor")
    efficiency_percent: Optional[float] = Field(
        None, gt=0, le=100, description="Efficiency percentage"
    )
    operating_hours_per_day: Optional[float] = Field(
        None, ge=0, le=24, description="Operating hours per day"
    )
    load_factor_percent: Optional[float] = Field(
        None, gt=0, le=100, description="Load factor percentage"
    )
    diversity_factor: Optional[float] = Field(
        None, gt=0, description="Diversity factor"
    )
    safety_factor: Optional[float] = Field(None, ge=1, description="Safety factor")
    design_margin_percent: Optional[float] = Field(
        None, ge=0, description="Design margin percentage"
    )
    related_pipe_id: Optional[int] = Field(None, description="Related pipe ID")
    related_vessel_id: Optional[int] = Field(None, description="Related vessel ID")
    related_htcircuit_id: Optional[int] = Field(
        None, description="Related HT circuit ID"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(LoadCalculationBaseSchema.validate_name)


class LoadCalculationReadSchema(LoadCalculationBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying load calculation data."""

    project_id: int = Field(..., description="Project ID")
    electrical_node_id: int = Field(..., description="Electrical node ID")
    related_pipe_id: Optional[int] = Field(None, description="Related pipe ID")
    related_vessel_id: Optional[int] = Field(None, description="Related vessel ID")
    related_htcircuit_id: Optional[int] = Field(
        None, description="Related HT circuit ID"
    )
    calculated_operating_power_kw: Optional[float] = Field(
        None, description="Calculated operating power in kW"
    )
    calculated_operating_current_a: Optional[float] = Field(
        None, description="Calculated operating current in amperes"
    )
    calculated_daily_energy_kwh: Optional[float] = Field(
        None, description="Calculated daily energy in kWh"
    )
    calculated_annual_energy_mwh: Optional[float] = Field(
        None, description="Calculated annual energy in MWh"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Heat Tracing Load HTC-001",
                "project_id": 1,
                "electrical_node_id": 2,
                "load_type": "heat_tracing",
                "rated_power_kw": 2.5,
                "rated_voltage_v": 240.0,
                "rated_current_a": 10.4,
                "calculated_operating_power_kw": 2.0,
                "calculated_operating_current_a": 8.3,
                "calculated_daily_energy_kwh": 48.0,
                "calculated_annual_energy_mwh": 17.5,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class LoadCalculationSummarySchema(BaseModel):
    """Lightweight schema for load calculation listings."""

    id: int = Field(..., description="Load calculation ID")
    name: str = Field(..., description="Load calculation name")
    electrical_node_id: int = Field(..., description="Electrical node ID")
    load_type: str = Field(..., description="Load type")
    rated_power_kw: float = Field(..., description="Rated power in kW")
    calculated_operating_power_kw: Optional[float] = Field(
        None, description="Calculated operating power in kW"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# VOLTAGE DROP CALCULATION SCHEMAS
# ============================================================================


class VoltageDropCalculationBaseSchema(BaseModel):
    """Base schema for VoltageDropCalculation with common fields."""

    name: str = Field(
        ...,
        min_length=3,
        max_length=100,
        description="Voltage drop calculation name/identifier",
    )
    supply_voltage_v: float = Field(..., gt=0, description="Supply voltage in volts")
    load_current_a: float = Field(..., gt=0, description="Load current in amperes")
    cable_length_m: float = Field(..., gt=0, description="Cable length in meters")
    cable_resistance_ohm_per_m: float = Field(
        ..., ge=0, description="Cable resistance per meter in ohm/m"
    )
    cable_reactance_ohm_per_m: float = Field(
        0.0, ge=0, description="Cable reactance per meter in ohm/m"
    )
    power_factor: float = Field(1.0, gt=0, le=1, description="Power factor")
    ambient_temperature_c: float = Field(
        25.0, ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    ground_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ground temperature in Celsius"
    )
    derating_factor: float = Field(1.0, gt=0, le=1, description="Derating factor")
    max_allowed_voltage_drop_percent: float = Field(
        5.0, gt=0, le=50, description="Maximum allowed voltage drop percentage"
    )
    calculation_method: str = Field("IEC", description="Calculation method")
    calculation_standard: Optional[str] = Field(
        None, description="Calculation standard"
    )
    calculation_notes: Optional[str] = Field(None, description="Calculation notes")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate voltage drop calculation name."""
        if not v or not v.strip():
            raise ValueError("Voltage drop calculation name cannot be empty")
        return v.strip()


class VoltageDropCalculationCreateSchema(VoltageDropCalculationBaseSchema):
    """Schema for creating a new voltage drop calculation."""

    project_id: int = Field(..., description="Project ID")
    cable_route_id: int = Field(..., description="Cable route ID")
    load_calculation_id: Optional[int] = Field(None, description="Load calculation ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Voltage Drop Calc - Main Feed",
                "project_id": 1,
                "cable_route_id": 1,
                "load_calculation_id": 1,
                "supply_voltage_v": 240.0,
                "load_current_a": 10.4,
                "cable_length_m": 150.0,
                "cable_resistance_ohm_per_m": 0.0184,
                "cable_reactance_ohm_per_m": 0.0,
                "power_factor": 1.0,
                "ambient_temperature_c": 25.0,
                "derating_factor": 1.0,
                "max_allowed_voltage_drop_percent": 5.0,
                "calculation_method": "IEC",
                "calculation_standard": "IEC 60364",
            }
        }
    )


class VoltageDropCalculationUpdateSchema(BaseModel):
    """Schema for updating an existing voltage drop calculation."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="Voltage drop calculation name/identifier",
    )
    cable_route_id: Optional[int] = Field(None, description="Cable route ID")
    load_calculation_id: Optional[int] = Field(None, description="Load calculation ID")
    supply_voltage_v: Optional[float] = Field(
        None, gt=0, description="Supply voltage in volts"
    )
    load_current_a: Optional[float] = Field(
        None, gt=0, description="Load current in amperes"
    )
    cable_length_m: Optional[float] = Field(
        None, gt=0, description="Cable length in meters"
    )
    cable_resistance_ohm_per_m: Optional[float] = Field(
        None, ge=0, description="Cable resistance per meter in ohm/m"
    )
    cable_reactance_ohm_per_m: Optional[float] = Field(
        None, ge=0, description="Cable reactance per meter in ohm/m"
    )
    power_factor: Optional[float] = Field(None, gt=0, le=1, description="Power factor")
    ambient_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    ground_temperature_c: Optional[float] = Field(
        None, ge=-50, le=80, description="Ground temperature in Celsius"
    )
    derating_factor: Optional[float] = Field(
        None, gt=0, le=1, description="Derating factor"
    )
    max_allowed_voltage_drop_percent: Optional[float] = Field(
        None, gt=0, le=50, description="Maximum allowed voltage drop percentage"
    )
    calculation_method: Optional[str] = Field(None, description="Calculation method")
    calculation_standard: Optional[str] = Field(
        None, description="Calculation standard"
    )
    calculation_notes: Optional[str] = Field(None, description="Calculation notes")

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(
        VoltageDropCalculationBaseSchema.validate_name
    )


class VoltageDropCalculationReadSchema(
    VoltageDropCalculationBaseSchema, BaseSoftDeleteSchema
):
    """Schema for reading/displaying voltage drop calculation data."""

    project_id: int = Field(..., description="Project ID")
    cable_route_id: int = Field(..., description="Cable route ID")
    load_calculation_id: Optional[int] = Field(None, description="Load calculation ID")
    calculated_voltage_drop_v: Optional[float] = Field(
        None, description="Calculated voltage drop in volts"
    )
    calculated_voltage_drop_percent: Optional[float] = Field(
        None, description="Calculated voltage drop percentage"
    )
    calculated_power_loss_w: Optional[float] = Field(
        None, description="Calculated power loss in watts"
    )
    calculated_efficiency_percent: Optional[float] = Field(
        None, description="Calculated efficiency percentage"
    )
    is_compliant: Optional[bool] = Field(
        None, description="Compliance with voltage drop limits"
    )
    compliance_margin_percent: Optional[float] = Field(
        None, description="Compliance margin percentage"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Voltage Drop Calc - Main Feed",
                "project_id": 1,
                "cable_route_id": 1,
                "load_calculation_id": 1,
                "supply_voltage_v": 240.0,
                "load_current_a": 10.4,
                "cable_length_m": 150.0,
                "calculated_voltage_drop_v": 8.5,
                "calculated_voltage_drop_percent": 3.5,
                "calculated_power_loss_w": 88.4,
                "calculated_efficiency_percent": 96.5,
                "is_compliant": True,
                "compliance_margin_percent": 1.5,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class VoltageDropCalculationSummarySchema(BaseModel):
    """Lightweight schema for voltage drop calculation listings."""

    id: int = Field(..., description="Voltage drop calculation ID")
    name: str = Field(..., description="Voltage drop calculation name")
    cable_route_id: int = Field(..., description="Cable route ID")
    calculated_voltage_drop_percent: Optional[float] = Field(
        None, description="Calculated voltage drop percentage"
    )
    is_compliant: Optional[bool] = Field(
        None, description="Compliance with voltage drop limits"
    )
    max_allowed_voltage_drop_percent: float = Field(
        ..., description="Maximum allowed voltage drop percentage"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# DESIGN WORKFLOW SCHEMAS
# ============================================================================


class ElectricalDesignInputSchema(BaseModel):
    """Schema for electrical design workflow inputs."""

    project_id: int = Field(..., description="Project ID")
    electrical_node_ids: Optional[List[int]] = Field(
        None, description="List of electrical node IDs to design"
    )
    cable_route_ids: Optional[List[int]] = Field(
        None, description="List of cable route IDs to design"
    )
    load_calculation_ids: Optional[List[int]] = Field(
        None, description="List of load calculation IDs to include"
    )
    design_parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Design parameters"
    )
    standards_context: ElectricalStandardsValidationInputSchema = Field(
        ..., description="Standards validation context"
    )
    auto_cable_sizing: bool = Field(
        True, description="Automatically perform cable sizing"
    )
    auto_voltage_drop_calc: bool = Field(
        True, description="Automatically calculate voltage drop"
    )
    optimization_enabled: bool = Field(True, description="Enable route optimization")
    max_voltage_drop_percent: float = Field(
        5.0, gt=0, le=50, description="Maximum allowed voltage drop percentage"
    )

    @field_validator("electrical_node_ids")
    @classmethod
    def validate_node_or_route_ids(
        cls, v: Optional[List[int]], info
    ) -> Optional[List[int]]:
        """Validate that either node_ids or route_ids is provided."""
        route_ids = info.data.get("cable_route_ids")
        if (v is None or len(v) == 0) and (route_ids is None or len(route_ids) == 0):
            raise ValueError(
                "Either electrical_node_ids or cable_route_ids must be provided"
            )
        return v


class ElectricalDesignResultSchema(BaseModel):
    """Schema for electrical design workflow results."""

    project_id: int = Field(..., description="Project ID")
    designed_nodes: List[ElectricalNodeReadSchema] = Field(
        default_factory=list, description="Designed electrical nodes"
    )
    designed_routes: List[CableRouteReadSchema] = Field(
        default_factory=list, description="Designed cable routes"
    )
    created_segments: List[CableSegmentReadSchema] = Field(
        default_factory=list, description="Created cable segments"
    )
    load_calculations: List[LoadCalculationReadSchema] = Field(
        default_factory=list, description="Load calculations"
    )
    voltage_drop_calculations: List[VoltageDropCalculationReadSchema] = Field(
        default_factory=list, description="Voltage drop calculations"
    )
    cable_sizing_results: List[CableSizingCalculationResultSchema] = Field(
        default_factory=list, description="Cable sizing results"
    )
    standards_validation_results: List[ElectricalStandardsValidationResultSchema] = (
        Field(default_factory=list, description="Standards validation results")
    )
    design_summary: Dict[str, Any] = Field(
        default_factory=dict, description="Design summary statistics"
    )
    warnings: List[str] = Field(default_factory=list, description="Design warnings")
    errors: List[str] = Field(default_factory=list, description="Design errors")


# ============================================================================
# PAGINATED RESPONSE SCHEMAS
# ============================================================================


class ElectricalNodeListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated electrical node list responses."""

    electrical_nodes: List[ElectricalNodeSummarySchema] = Field(
        ..., description="List of electrical nodes"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "electrical_nodes": [
                    {
                        "id": 1,
                        "name": "Main Switchboard Incoming",
                        "project_id": 1,
                        "node_type": "SWITCHBOARD_INCOMING",
                        "voltage_v": 415.0,
                        "power_capacity_kva": 100.0,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class CableRouteListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated cable route list responses."""

    cable_routes: List[CableRouteSummarySchema] = Field(
        ..., description="List of cable routes"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "cable_routes": [
                    {
                        "id": 1,
                        "name": "Main Feed to HT Panel",
                        "project_id": 1,
                        "from_node_id": 1,
                        "to_node_id": 2,
                        "length_m": 150.0,
                        "calculated_voltage_drop_v": 8.5,
                        "installation_method": "CABLE_TRAY",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class CableSegmentListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated cable segment list responses."""

    cable_segments: List[CableSegmentSummarySchema] = Field(
        ..., description="List of cable segments"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "cable_segments": [
                    {
                        "id": 1,
                        "name": "Segment 1 - Underground",
                        "cable_route_id": 1,
                        "segment_order": 1,
                        "length_m": 50.0,
                        "installation_method": "DIRECT_BURIED",
                        "calculated_voltage_drop_v": 4.2,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class LoadCalculationListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated load calculation list responses."""

    load_calculations: List[LoadCalculationSummarySchema] = Field(
        ..., description="List of load calculations"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "load_calculations": [
                    {
                        "id": 1,
                        "name": "Heat Tracing Load HTC-001",
                        "electrical_node_id": 2,
                        "load_type": "heat_tracing",
                        "rated_power_kw": 2.5,
                        "calculated_operating_power_kw": 2.0,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class VoltageDropCalculationListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated voltage drop calculation list responses."""

    voltage_drop_calculations: List[VoltageDropCalculationSummarySchema] = Field(
        ..., description="List of voltage drop calculations"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "voltage_drop_calculations": [
                    {
                        "id": 1,
                        "name": "Voltage Drop Calc - Main Feed",
                        "cable_route_id": 1,
                        "calculated_voltage_drop_percent": 3.5,
                        "is_compliant": True,
                        "max_allowed_voltage_drop_percent": 5.0,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )
