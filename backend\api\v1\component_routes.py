# backend/api/v1/component_routes.py
"""
FastAPI routes for Component and ComponentCategory operations.

This module defines the REST API endpoints for component-related operations
including CRUD operations, search, and filtering.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Depends, Query, Path, status
from fastapi.responses import JSONResponse

from backend.core.schemas.component_schemas import (
    ComponentCreateSchema,
    ComponentUpdateSchema,
    ComponentReadSchema,
    ComponentListResponseSchema,
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategoryReadSchema,
    ComponentCategoryListResponseSchema
)
from backend.core.services.component_service import ComponentService, ComponentCategoryService
from backend.core.repositories.component_repository import ComponentRepository, ComponentCategoryRepository
from backend.core.errors.exceptions import (
    ComponentNotFoundError,
    DuplicateEntryError,
    DataValidationError,
    DatabaseError
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/components", tags=["Components"])
category_router = APIRouter(prefix="/component-categories", tags=["Component Categories"])


# Dependency injection (simplified for now - in production you'd use proper DI)
def get_component_service() -> ComponentService:
    """Get ComponentService instance."""
    # This is simplified - in production you'd use proper dependency injection
    from backend.core.database.session import get_db_session
    db_session = next(get_db_session())
    component_repo = ComponentRepository(db_session)
    category_repo = ComponentCategoryRepository(db_session)
    return ComponentService(component_repo, category_repo)


def get_category_service() -> ComponentCategoryService:
    """Get ComponentCategoryService instance."""
    # This is simplified - in production you'd use proper dependency injection
    from backend.core.database.session import get_db_session
    db_session = next(get_db_session())
    category_repo = ComponentCategoryRepository(db_session)
    return ComponentCategoryService(category_repo)


# Component Routes

@router.post(
    "/",
    response_model=ComponentReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new component",
    description="Create a new component with the provided data. Component name must be unique within the category."
)
async def create_component(
    component_data: ComponentCreateSchema,
    service: ComponentService = Depends(get_component_service)
) -> ComponentReadSchema:
    """Create a new component."""
    try:
        logger.info(f"API request to create component: {component_data.name}")
        result = service.create_component(component_data)
        logger.info(f"Component created successfully: {result.id}")
        return result
        
    except DuplicateEntryError as e:
        logger.warning(f"Duplicate component creation attempt: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.detail
        )
    except DataValidationError as e:
        logger.warning(f"Component validation error: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.detail
        )
    except DatabaseError as e:
        logger.error(f"Database error creating component: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while creating component"
        )
    except Exception as e:
        logger.error(f"Unexpected error creating component: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@router.get(
    "/{component_id}",
    response_model=ComponentReadSchema,
    summary="Get component details",
    description="Retrieve detailed information about a specific component by ID."
)
async def get_component(
    component_id: int = Path(..., description="Component ID", gt=0),
    service: ComponentService = Depends(get_component_service)
) -> ComponentReadSchema:
    """Get component details by ID."""
    try:
        logger.debug(f"API request to get component: {component_id}")
        result = service.get_component_details(component_id)
        return result
        
    except ComponentNotFoundError as e:
        logger.warning(f"Component not found: {component_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Component with ID {component_id} not found"
        )
    except DatabaseError as e:
        logger.error(f"Database error retrieving component {component_id}: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving component"
        )
    except Exception as e:
        logger.error(f"Unexpected error retrieving component {component_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@router.put(
    "/{component_id}",
    response_model=ComponentReadSchema,
    summary="Update component",
    description="Update an existing component with the provided data."
)
async def update_component(
    component_id: int = Path(..., description="Component ID", gt=0),
    component_data: ComponentUpdateSchema = ...,
    service: ComponentService = Depends(get_component_service)
) -> ComponentReadSchema:
    """Update an existing component."""
    try:
        logger.info(f"API request to update component: {component_id}")
        result = service.update_component(component_id, component_data)
        logger.info(f"Component updated successfully: {component_id}")
        return result
        
    except ComponentNotFoundError as e:
        logger.warning(f"Component not found for update: {component_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Component with ID {component_id} not found"
        )
    except DuplicateEntryError as e:
        logger.warning(f"Duplicate component update attempt: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.detail
        )
    except DataValidationError as e:
        logger.warning(f"Component update validation error: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.detail
        )
    except DatabaseError as e:
        logger.error(f"Database error updating component {component_id}: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while updating component"
        )
    except Exception as e:
        logger.error(f"Unexpected error updating component {component_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@router.delete(
    "/{component_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete component",
    description="Soft delete a component. The component will be marked as deleted but not physically removed."
)
async def delete_component(
    component_id: int = Path(..., description="Component ID", gt=0),
    service: ComponentService = Depends(get_component_service)
):
    """Delete a component (soft delete)."""
    try:
        logger.info(f"API request to delete component: {component_id}")
        service.delete_component(component_id)
        logger.info(f"Component deleted successfully: {component_id}")
        return JSONResponse(
            status_code=status.HTTP_204_NO_CONTENT,
            content=None
        )
        
    except ComponentNotFoundError as e:
        logger.warning(f"Component not found for deletion: {component_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Component with ID {component_id} not found"
        )
    except DatabaseError as e:
        logger.error(f"Database error deleting component {component_id}: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while deleting component"
        )
    except Exception as e:
        logger.error(f"Unexpected error deleting component {component_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@router.get(
    "/",
    response_model=ComponentListResponseSchema,
    summary="List components",
    description="Get a paginated list of components with optional filtering by category, search term, and manufacturer."
)
async def list_components(
    page: int = Query(1, description="Page number", ge=1),
    per_page: int = Query(10, description="Items per page", ge=1, le=100),
    category_id: Optional[int] = Query(None, description="Filter by category ID", gt=0),
    search: Optional[str] = Query(None, description="Search term for name, model, or manufacturer"),
    manufacturer: Optional[str] = Query(None, description="Filter by manufacturer"),
    include_deleted: bool = Query(False, description="Include soft-deleted components"),
    service: ComponentService = Depends(get_component_service)
) -> ComponentListResponseSchema:
    """Get paginated list of components with filtering."""
    try:
        logger.debug(f"API request to list components: page={page}, per_page={per_page}")
        result = service.get_components_list(
            page=page,
            per_page=per_page,
            category_id=category_id,
            search_term=search,
            manufacturer=manufacturer,
            include_deleted=include_deleted
        )
        logger.debug(f"Retrieved {len(result.components)} components")
        return result
        
    except DatabaseError as e:
        logger.error(f"Database error listing components: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving components"
        )
    except Exception as e:
        logger.error(f"Unexpected error listing components: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


# Component Category Routes

@category_router.post(
    "/",
    response_model=ComponentCategoryReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new component category",
    description="Create a new component category with the provided data."
)
async def create_category(
    category_data: ComponentCategoryCreateSchema,
    service: ComponentCategoryService = Depends(get_category_service)
) -> ComponentCategoryReadSchema:
    """Create a new component category."""
    try:
        logger.info(f"API request to create category: {category_data.name}")
        result = service.create_category(category_data)
        logger.info(f"Category created successfully: {result.id}")
        return result
        
    except DuplicateEntryError as e:
        logger.warning(f"Duplicate category creation attempt: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=e.detail
        )
    except DataValidationError as e:
        logger.warning(f"Category validation error: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=e.detail
        )
    except DatabaseError as e:
        logger.error(f"Database error creating category: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while creating category"
        )
    except Exception as e:
        logger.error(f"Unexpected error creating category: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@category_router.get(
    "/{category_id}",
    response_model=ComponentCategoryReadSchema,
    summary="Get category details",
    description="Retrieve detailed information about a specific component category by ID."
)
async def get_category(
    category_id: int = Path(..., description="Category ID", gt=0),
    service: ComponentCategoryService = Depends(get_category_service)
) -> ComponentCategoryReadSchema:
    """Get category details by ID."""
    try:
        logger.debug(f"API request to get category: {category_id}")
        result = service.get_category_details(category_id)
        return result
        
    except ComponentNotFoundError as e:
        logger.warning(f"Category not found: {category_id}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Category with ID {category_id} not found"
        )
    except DatabaseError as e:
        logger.error(f"Database error retrieving category {category_id}: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving category"
        )
    except Exception as e:
        logger.error(f"Unexpected error retrieving category {category_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )


@category_router.get(
    "/",
    response_model=ComponentCategoryListResponseSchema,
    summary="List component categories",
    description="Get a paginated list of component categories with optional parent filtering."
)
async def list_categories(
    page: int = Query(1, description="Page number", ge=1),
    per_page: int = Query(10, description="Items per page", ge=1, le=100),
    parent_id: Optional[int] = Query(None, description="Filter by parent category ID (None for root categories)", gt=0),
    include_deleted: bool = Query(False, description="Include soft-deleted categories"),
    service: ComponentCategoryService = Depends(get_category_service)
) -> ComponentCategoryListResponseSchema:
    """Get paginated list of component categories."""
    try:
        logger.debug(f"API request to list categories: page={page}, per_page={per_page}")
        result = service.get_categories_list(
            page=page,
            per_page=per_page,
            parent_id=parent_id,
            include_deleted=include_deleted
        )
        logger.debug(f"Retrieved {len(result.categories)} categories")
        return result
        
    except DatabaseError as e:
        logger.error(f"Database error listing categories: {e.detail}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error occurred while retrieving categories"
        )
    except Exception as e:
        logger.error(f"Unexpected error listing categories: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred"
        )
