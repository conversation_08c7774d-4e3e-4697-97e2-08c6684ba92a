# backend/core/standards/__init__.py
"""
Standards Layer - Engineering standards validation and compliance.

This module provides validation against various engineering standards
including TR 50410, IEC standards, and hazardous area compliance.

The standards layer is organized into specialized sub-packages:
- tr_50410: TR 50410 standard specific rules
- iec_60079_30_1: IEC 60079-30-1 hazardous area standards
- general_standards: Cross-standard rules and safety factors
- standards_manager: Central interface for standard selection and application

All standards validation follows strict error handling patterns and integrates
with the calculations layer for comprehensive design validation.
"""

from .standards_manager import StandardsManager

__all__ = [
    "StandardsManager",
]
