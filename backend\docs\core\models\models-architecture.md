### Model Layer Architecture Specification

**1. Purpose & Role**
The Model Layer serves as the **Object-Relational Mapping (ORM)** layer, defining the structure of your application's data as Python classes that map directly to database tables. Utilizing **SQLAlchemy**, its primary purposes are to:
* **Represent Database Schema:** Clearly define the database tables, columns, data types, and relationships in a Pythonic way.
* **Enable Data Persistence:** Provide the means to perform Create, Read, Update, and Delete (CRUD) operations on database records through Python objects, abstracting away raw SQL.
* **Enforce Database Integrity:** Leverage database-level constraints (e.g., unique constraints, foreign keys, nullability rules) directly within the ORM definitions.
* **Define Relationships:** Specify how different entities in the database are related to each other (e.g., one-to-many, many-to-many).

    See the [Database Layer Architecture](../database/architecture.md) for more details on database interaction.

**2. Location & Structure (`src/core/models/`)**
The Model Layer will be organized within `src/core/models/`, promoting modularity by logical domain or entity.

```
src/core/models/
├── __init__.py
├── base.py                   # Defines the declarative base and common base model columns/mixins
├── user.py                   # ORM models for users, roles, etc.
├── project.py                # ORM model for the Project entity
├── circuit.py                # ORM model for Heat Tracing Circuits
├── component.py              # ORM models for various components (e.g., Pipe, Insulation, Instrument)
├── cable.py                  # ORM model for heating cable types and instances
├── material.py               # ORM models for materials and their properties
└── settings.py               # ORM models for global or application-wide settings
```

**3. Core Responsibilities & Functionality**

*   **Table Mapping:** Each Python class in this layer directly maps to a database table. Columns are defined within these classes, specifying their type, nullability, default values, and other database constraints.
*   **Data Persistence:** When instances of these model classes are created, modified, or deleted within a SQLAlchemy session, the ORM handles the translation into corresponding SQL statements for the database.
*   **Relationship Definition:** Defines relationships between different model classes using SQLAlchemy's `relationship` and `foreign key` constructs. This allows for easy navigation between related objects (e.g., accessing all `Pipe` objects associated with a `Project`).
*   **Database Constraints:** Implements database-level integrity rules directly in the model definitions:
    *   **Primary Keys:** Uniquely identifies each record.
    *   **Foreign Keys:** Enforces referential integrity between tables.
    *   **Unique Constraints:** Ensures uniqueness of values in specific columns or combinations of columns.
    *   **Nullability:** Defines whether a column can store `NULL` values.
    *   **Indexes:** Defines database indexes for performance optimization.
*   **Basic Model-Level Logic (Limited):** While the [Service Layer](../services/services-architecture.md) holds core business logic, the Model Layer can contain very localized, self-contained methods or properties that are:
    *   **Derived Properties:** Simple calculations or aggregations based *only* on the model's own attributes (e.g., `user.full_name`, `pipe.is_active`). These are often defined using SQLAlchemy's `hybrid_property`.
    *   **Lifecycle Hooks:** Uses SQLAlchemy event listeners (e.g., `before_insert`, `before_update`) for actions like automatically setting `updated_at` timestamps or performing lightweight pre-persistence validation.

    For more details on validation at this layer, see the [Model-Level Validation Architecture](validation-architecture.md).

*   **Data Type Handling:** Specifies the appropriate SQLAlchemy column types (e.g., `String`, `Integer`, `Float`, `Boolean`, `DateTime`, `JSONB`) to correctly store and retrieve data.

**4. Key Components/Concepts**

*   **`DeclarativeBase`:** The base class that all ORM models inherit from, linking them to the SQLAlchemy engine and defining table metadata. (In modern SQLAlchemy, this is often imported from `sqlalchemy.orm`).
*   **`mapped_column()`:** Used for defining columns in a more type-hint-friendly way.
*   **`relationship()`:** Defines the relationships between different model classes.
*   **`session`:** While not part of the model definition itself, ORM operations are always performed within the context of a SQLAlchemy `Session`, which is managed by the [Database Layer](../database/architecture.md) and provided to the [Repository Layer](../repositories/repositories-architecture.md).

    For more details on session management, see the [Database Layer Architecture](../database/architecture.md).

**5. Example Model Structure**

```python
# src/core/models/base.py
from datetime import datetime
from sqlalchemy import func
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column

class Base(DeclarativeBase):
    __abstract__ = True # This tells SQLAlchemy not to create a table for Base

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    created_at: Mapped[datetime] = mapped_column(default=func.now())
    updated_at: Mapped[datetime] = mapped_column(default=func.now(), onupdate=func.now())

# src/core/models/project.py
from typing import List
from sqlalchemy import String, Text, Boolean
from sqlalchemy.orm import Mapped, relationship, mapped_column

from .base import Base
from .circuit import Circuit # Assuming Circuit model exists

class Project(Base):
    __tablename__ = "projects"

    name: Mapped[str] = mapped_column(String(100), unique=True, index=True)
    code: Mapped[str] = mapped_column(String(20), unique=True, index=True)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

    # Define one-to-many relationship with circuits
    circuits: Mapped[List["Circuit"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', code='{self.code}')>"
```

**6. Interaction with Other Layers**

*   **Database Layer (`src/core/database/`):** The `Base` class (or `DeclarativeBase`) is initialized and connected to the database engine defined in the [Database Layer](../database/architecture.md). The [Database Layer](../database/architecture.md) also provides the `Session` objects that the [Repository Layer](../repositories/repositories-architecture.md) uses to interact with these models.
*   **Repository Layer ([`src/core/repositories/`](../repositories/repositories-architecture.md) - Primary Consumer):** This layer is the sole direct consumer of the ORM models. It performs all CRUD operations (querying, adding, updating, deleting instances) on these models within a SQLAlchemy session. The [Repository Layer](../repositories/repositories-architecture.md) acts as a facade, exposing clean methods that operate on model instances, shielding higher layers from SQLAlchemy specifics.
*   **Service Layer ([`src/core/services/`](../services/services-architecture.md)):** Receives ORM model instances (or lists of instances) from the [Repository Layer](../repositories/repositories-architecture.md). It performs business logic on these model instances (e.g., modifying attributes, calling model methods, building relationships) and then passes them back to the Repository Layer for persistence. The [Service Layer](../services/services-architecture.md) typically does **not** directly query the database or instantiate ORM sessions.
*   **Schemas Layer ([`src/core/schemas/`](../schemas/schemas-architecture.md)):** [Pydantic schemas](../schemas/schemas-architecture.md) often mirror the structure of SQLAlchemy ORM models (`src/core/models/`). However, they serve distinct purposes: schemas are for validation/serialization/deserialization, while models are for database interaction and persistence. The `Config.from_attributes = True` (or `orm_mode = True`) setting in Pydantic facilitates easy conversion between the two.

    See the [Schemas Layer Architecture](../schemas/schemas-architecture.md) and [API Layer Architecture](../../../api/api-architecture.md#4-core-responsibilities--functionality) for more details.

*   **Model-Level Validation:** As discussed, `Base` or individual models can incorporate SQLAlchemy event listeners or `hybrid_property` definitions to enforce simple, self-contained validation rules directly on the model before data is committed to the database. More complex business validation, however, resides in the [Service Layer](../services/services-architecture.md).

    For a detailed explanation, see the [Model-Level Validation Architecture](validation-architecture.md).

**7. Key Principles**

*   **Persistence Focus:** The primary concern of this layer is how data is structured for storage and retrieval from the database.
*   **Separation of Concerns:** Models should be relatively "dumb" data structures with persistence capabilities. Complex business logic and cross-model operations reside in the [Service Layer](../services/services-architecture.md).
*   **Database Abstraction:** SQLAlchemy's ORM abstracts away the need to write raw SQL for most common operations.
*   **DRY (Don't Repeat Yourself):** Achieved through a common `Base` model for shared fields and the use of inheritance.
*   **Explicit Relationships:** Clearly defined relationships make traversing the object graph intuitive and efficient.

This completes the detailed specification for the Model Layer, providing a comprehensive overview of all backend components.
