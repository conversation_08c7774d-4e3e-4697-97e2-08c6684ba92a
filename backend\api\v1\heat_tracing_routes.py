# backend/api/v1/heat_tracing_routes.py
"""
Heat Tracing API Routes

This module defines the FastAPI routes for Heat Tracing entity operations including:
- CRUD operations for Pipes, Vessels, HTCircuits, and ControlCircuits
- Heat loss calculation endpoints
- Standards compliance validation
- Design workflow orchestration
- Project summary and readiness endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Response, status
from sqlalchemy.orm import Session

try:
    from backend.config.logging_config import get_logger

    # Dependency injection for database session
    from backend.core.database import get_db
    from backend.core.errors.error_factory import ErrorFactory
    from backend.core.errors.exceptions import (
        DatabaseError,
        DataValidationError,
        DuplicateEntryError,
        NotFoundError,
    )
    from backend.core.schemas.error import ErrorResponseSchema
    from backend.core.schemas.heat_tracing_schemas import (
        # HTCircuit schemas
        HeatLossCalculationInputSchema,
        HeatLossCalculationResultSchema,
        # Design workflow schemas
        HeatTracingDesignInputSchema,
        HeatTracingDesignResultSchema,
        # Pipe schemas
        PipeCreateSchema,
        PipeListResponseSchema,
        PipeReadSchema,
        PipeUpdateSchema,
        StandardsValidationInputSchema,
        StandardsValidationResultSchema,
        # Vessel schemas
        VesselCreateSchema,
        VesselListResponseSchema,
        VesselReadSchema,
    )

    # Import services
    from backend.core.services.heat_tracing_service import HeatTracingService
except ImportError:
    from config.logging_config import get_logger

    # Dependency injection for database session
    from core.database import get_db
    from core.errors.error_factory import ErrorFactory
    from core.errors.exceptions import (
        DatabaseError,
        DataValidationError,
        DuplicateEntryError,
        NotFoundError,
    )
    from core.schemas.error import ErrorResponseSchema
    from core.schemas.heat_tracing_schemas import (
        # HTCircuit schemas
        HeatLossCalculationInputSchema,
        HeatLossCalculationResultSchema,
        # Design workflow schemas
        HeatTracingDesignInputSchema,
        HeatTracingDesignResultSchema,
        # Pipe schemas
        PipeCreateSchema,
        PipeListResponseSchema,
        PipeReadSchema,
        PipeUpdateSchema,
        StandardsValidationInputSchema,
        StandardsValidationResultSchema,
        # Vessel schemas
        VesselCreateSchema,
        VesselListResponseSchema,
        VesselReadSchema,
    )

    # Import services
    from core.services.heat_tracing_service import HeatTracingService

# Initialize logger for this module
logger = get_logger(__name__)


# Dependency injection for HeatTracingService
def get_heat_tracing_service(db: Session = Depends(get_db)) -> HeatTracingService:
    """
    Dependency injection for HeatTracingService.

    Args:
        db: Database session

    Returns:
        HeatTracingService: Configured service instance
    """
    try:
        from backend.core.repositories.heat_tracing_repository import (
            HeatTracingRepository,
        )
    except ImportError:
        from core.repositories.heat_tracing_repository import HeatTracingRepository

    heat_tracing_repo = HeatTracingRepository(db)
    return HeatTracingService(heat_tracing_repo)


router = APIRouter()


# ============================================================================
# PIPE ENDPOINTS
# ============================================================================


@router.post(
    "/pipes",
    response_model=PipeReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new pipe",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_409_CONFLICT: {
            "model": ErrorResponseSchema,
            "description": "Pipe with this name already exists in project",
        },
    },
)
async def create_pipe(
    pipe_data: PipeCreateSchema,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Create a new pipe with validation.

    - **name**: Unique pipe name within the project
    - **project_id**: ID of the project this pipe belongs to
    - **pipe_material_id**: ID of the pipe material component
    - **insulation_material_id**: ID of the insulation material component
    - **length_m**: Pipe length in meters
    - **insulation_thickness_mm**: Insulation thickness in millimeters
    """
    logger.info(f"Creating new pipe: {pipe_data.name}")

    try:
        new_pipe = service.create_pipe(pipe_data)
        logger.info(f"Pipe created successfully: {new_pipe.name} (ID: {new_pipe.id})")
        return new_pipe
    except DataValidationError as e:
        logger.warning(f"Validation error creating pipe: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except DuplicateEntryError as e:
        logger.warning(f"Duplicate pipe error: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Unexpected error creating pipe: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/pipes/{pipe_id}",
    response_model=PipeReadSchema,
    summary="Retrieve pipe details by ID",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Pipe not found",
        },
    },
)
async def get_pipe(
    pipe_id: int,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Retrieve detailed information about a specific pipe.

    - **pipe_id**: Unique identifier of the pipe
    """
    logger.debug(f"Retrieving pipe details: {pipe_id}")

    try:
        pipe = service.get_pipe_details(pipe_id)
        return pipe
    except NotFoundError as e:
        logger.warning(f"Pipe not found: {pipe_id}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Error retrieving pipe {pipe_id}: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.put(
    "/pipes/{pipe_id}",
    response_model=PipeReadSchema,
    summary="Update pipe details",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Pipe not found",
        },
        status.HTTP_409_CONFLICT: {
            "model": ErrorResponseSchema,
            "description": "Update would violate unique constraint",
        },
    },
)
async def update_pipe(
    pipe_id: int,
    pipe_data: PipeUpdateSchema,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Update an existing pipe's details.

    - **pipe_id**: Unique identifier of the pipe to update
    - Only provided fields will be updated
    """
    logger.info(f"Updating pipe: {pipe_id}")

    try:
        updated_pipe = service.update_pipe(pipe_id, pipe_data)
        logger.info(
            f"Pipe updated successfully: {updated_pipe.name} (ID: {updated_pipe.id})"
        )
        return updated_pipe
    except (DataValidationError, NotFoundError, DuplicateEntryError) as e:
        logger.warning(f"Error updating pipe {pipe_id}: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Unexpected error updating pipe {pipe_id}: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.delete(
    "/pipes/{pipe_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete a pipe (soft delete)",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Pipe not found",
        }
    },
)
async def delete_pipe(
    pipe_id: int,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Soft delete a pipe.

    - **pipe_id**: Unique identifier of the pipe to delete
    - The pipe will be marked as deleted but not physically removed
    """
    logger.info(f"Deleting pipe: {pipe_id}")

    try:
        service.delete_pipe(pipe_id)
        logger.info(f"Pipe deleted successfully: {pipe_id}")
        return Response(status_code=status.HTTP_204_NO_CONTENT)
    except NotFoundError as e:
        logger.warning(f"Pipe not found for deletion: {pipe_id}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Error deleting pipe {pipe_id}: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/projects/{project_id}/pipes",
    response_model=PipeListResponseSchema,
    summary="List pipes for a project with pagination",
    responses={
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    },
)
async def list_pipes(
    project_id: int,
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    per_page: int = Query(10, ge=1, le=100, description="Number of pipes per page"),
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Retrieve a paginated list of pipes for a specific project.

    - **project_id**: ID of the project to list pipes for
    - **page**: Page number (1-based, default: 1)
    - **per_page**: Number of pipes per page (1-100, default: 10)
    """
    logger.debug(
        f"Listing pipes for project {project_id}: page={page}, per_page={per_page}"
    )

    try:
        pipes_list = service.get_pipes_list(
            project_id=project_id, page=page, per_page=per_page
        )
        logger.debug(
            f"Retrieved {len(pipes_list.pipes)} pipes for project {project_id}"
        )
        return pipes_list
    except DatabaseError as e:
        logger.error(f"Database error listing pipes: {e}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Unexpected error listing pipes: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


# ============================================================================
# VESSEL ENDPOINTS
# ============================================================================


@router.post(
    "/vessels",
    response_model=VesselReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new vessel",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_409_CONFLICT: {
            "model": ErrorResponseSchema,
            "description": "Vessel with this name already exists in project",
        },
    },
)
async def create_vessel(
    vessel_data: VesselCreateSchema,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Create a new vessel with validation.

    - **name**: Unique vessel name within the project
    - **project_id**: ID of the project this vessel belongs to
    - **material_id**: ID of the vessel material component
    - **insulation_material_id**: ID of the insulation material component
    - **dimensions_json**: Vessel dimensions as JSON string
    - **surface_area_m2**: Surface area in square meters
    """
    logger.info(f"Creating new vessel: {vessel_data.name}")

    try:
        new_vessel = service.create_vessel(vessel_data)
        logger.info(
            f"Vessel created successfully: {new_vessel.name} (ID: {new_vessel.id})"
        )
        return new_vessel
    except DataValidationError as e:
        logger.warning(f"Validation error creating vessel: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except DuplicateEntryError as e:
        logger.warning(f"Duplicate vessel error: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Unexpected error creating vessel: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/vessels/{vessel_id}",
    response_model=VesselReadSchema,
    summary="Retrieve vessel details by ID",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Vessel not found",
        },
    },
)
async def get_vessel(
    vessel_id: int,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Retrieve detailed information about a specific vessel.

    - **vessel_id**: Unique identifier of the vessel
    """
    logger.debug(f"Retrieving vessel details: {vessel_id}")

    try:
        vessel = service.get_vessel_details(vessel_id)
        return vessel
    except NotFoundError as e:
        logger.warning(f"Vessel not found: {vessel_id}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Error retrieving vessel {vessel_id}: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/projects/{project_id}/vessels",
    response_model=VesselListResponseSchema,
    summary="List vessels for a project with pagination",
    responses={
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    },
)
async def list_vessels(
    project_id: int,
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    per_page: int = Query(10, ge=1, le=100, description="Number of vessels per page"),
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Retrieve a paginated list of vessels for a specific project.

    - **project_id**: ID of the project to list vessels for
    - **page**: Page number (1-based, default: 1)
    - **per_page**: Number of vessels per page (1-100, default: 10)
    """
    logger.debug(
        f"Listing vessels for project {project_id}: page={page}, per_page={per_page}"
    )

    try:
        vessels_list = service.get_vessels_list(
            project_id=project_id, page=page, per_page=per_page
        )
        logger.debug(
            f"Retrieved {len(vessels_list.vessels)} vessels for project {project_id}"
        )
        return vessels_list
    except DatabaseError as e:
        logger.error(f"Database error listing vessels: {e}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Unexpected error listing vessels: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


# ============================================================================
# CALCULATION ENDPOINTS
# ============================================================================


@router.post(
    "/pipes/{pipe_id}/calculate-heat-loss",
    response_model=HeatLossCalculationResultSchema,
    summary="Calculate heat loss for a pipe",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid calculation inputs",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Pipe not found",
        },
        status.HTTP_503_SERVICE_UNAVAILABLE: {
            "model": ErrorResponseSchema,
            "description": "Calculation service unavailable",
        },
    },
)
async def calculate_pipe_heat_loss(
    pipe_id: int,
    calculation_input: HeatLossCalculationInputSchema,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Calculate heat loss for a specific pipe using engineering calculations.

    - **pipe_id**: Unique identifier of the pipe
    - **calculation_input**: Heat loss calculation parameters including:
        - Pipe diameter and length
        - Fluid and ambient temperatures
        - Insulation properties
        - Environmental conditions
    """
    logger.info(f"Calculating heat loss for pipe {pipe_id}")

    try:
        calculation_result = service.calculate_pipe_heat_loss(
            pipe_id, calculation_input
        )
        logger.info(
            f"Heat loss calculation completed for pipe {pipe_id}: {calculation_result.heat_loss_rate} W/m"
        )
        return calculation_result
    except NotFoundError as e:
        logger.warning(f"Pipe not found for heat loss calculation: {pipe_id}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except DataValidationError as e:
        logger.warning(f"Invalid calculation inputs for pipe {pipe_id}: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(
            f"Error calculating heat loss for pipe {pipe_id}: {e}", exc_info=True
        )
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.post(
    "/validate-standards-compliance",
    response_model=StandardsValidationResultSchema,
    summary="Validate standards compliance",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid validation inputs",
        },
        status.HTTP_503_SERVICE_UNAVAILABLE: {
            "model": ErrorResponseSchema,
            "description": "Standards manager unavailable",
        },
    },
)
async def validate_standards_compliance(
    validation_input: StandardsValidationInputSchema,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Validate heat tracing design against applicable standards.

    - **validation_input**: Standards validation parameters including:
        - Heat loss calculation results
        - Design parameters
        - Project standards (TR 50410, IEC 60079-30-1, etc.)
        - Hazardous area classification
    """
    logger.info("Validating standards compliance")

    try:
        validation_result = service.validate_standards_compliance(validation_input)
        logger.info(
            f"Standards validation completed: {'COMPLIANT' if validation_result.is_compliant else 'NON-COMPLIANT'}"
        )
        return validation_result
    except DataValidationError as e:
        logger.warning(f"Invalid validation inputs: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(f"Error validating standards compliance: {e}", exc_info=True)
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


# ============================================================================
# DESIGN WORKFLOW ENDPOINTS
# ============================================================================


@router.post(
    "/projects/{project_id}/design-workflow",
    response_model=HeatTracingDesignResultSchema,
    summary="Execute heat tracing design workflow",
    responses={
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid design inputs",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project or entities not found",
        },
    },
)
async def execute_design_workflow(
    project_id: int,
    design_input: HeatTracingDesignInputSchema,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Execute the complete heat tracing design workflow for a project.

    This endpoint orchestrates the entire design process including:
    - Heat loss calculations for pipes and vessels
    - Standards compliance validation
    - Circuit assignment and optimization
    - Power calculations and load balancing

    - **project_id**: ID of the project to design
    - **design_input**: Design workflow parameters including:
        - Lists of pipe and vessel IDs to design
        - Design parameters (temperatures, safety factors, etc.)
        - Standards validation context
        - Optimization settings
    """
    logger.info(f"Executing heat tracing design workflow for project {project_id}")

    try:
        # Ensure project_id matches the input
        design_input.project_id = project_id

        design_result = service.execute_heat_tracing_design(design_input)
        logger.info(
            f"Design workflow completed for project {project_id}: "
            f"{design_result.design_summary.get('total_pipes_designed', 0)} pipes, "
            f"{design_result.design_summary.get('total_vessels_designed', 0)} vessels"
        )
        return design_result
    except (DataValidationError, NotFoundError) as e:
        logger.warning(f"Error in design workflow for project {project_id}: {e.detail}")
        raise HTTPException(status_code=e.status_code, detail=e.detail)
    except Exception as e:
        logger.error(
            f"Unexpected error in design workflow for project {project_id}: {e}",
            exc_info=True,
        )
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


# ============================================================================
# PROJECT SUMMARY ENDPOINTS
# ============================================================================


@router.get(
    "/projects/{project_id}/summary",
    summary="Get heat tracing summary for a project",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        },
    },
)
async def get_project_summary(
    project_id: int,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Get heat tracing summary statistics for a project.

    - **project_id**: ID of the project to summarize

    Returns summary including:
    - Total number of pipes and vessels
    - Items without circuit assignments
    - Items with heat loss calculations
    - Design completion status
    """
    logger.debug(f"Generating heat tracing summary for project {project_id}")

    try:
        summary = service.get_project_heat_tracing_summary(project_id)
        return summary
    except Exception as e:
        logger.error(
            f"Error generating project summary for {project_id}: {e}", exc_info=True
        )
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )


@router.get(
    "/projects/{project_id}/design-readiness",
    summary="Check design readiness for a project",
    responses={
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": "Project not found",
        },
    },
)
async def get_design_readiness(
    project_id: int,
    service: HeatTracingService = Depends(get_heat_tracing_service),
):
    """
    Check design readiness status for a project.

    - **project_id**: ID of the project to check

    Returns readiness status including:
    - Whether all items have circuit assignments
    - Heat loss calculation completion
    - Overall completion percentage
    - List of missing items
    """
    logger.debug(f"Checking design readiness for project {project_id}")

    try:
        readiness = service.get_project_design_readiness(project_id)
        return readiness
    except Exception as e:
        logger.error(
            f"Error checking design readiness for project {project_id}: {e}",
            exc_info=True,
        )
        app_exception = ErrorFactory.create_exception("500_001", reason=str(e))
        raise HTTPException(
            status_code=app_exception.status_code, detail=app_exception.detail
        )
