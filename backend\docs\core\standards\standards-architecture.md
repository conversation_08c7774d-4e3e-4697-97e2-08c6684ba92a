### Standards Layer Architecture Specification

**1. Purpose & Role**
The Standards Layer is responsible for implementing and enforcing adherence to various engineering standards, codes, and best practices relevant to heat tracing design. Its primary purpose is to:
*   **Ensure Compliance:** Guarantee that all [calculations](../calculations/calculations-architecture.md), designs, and selected components comply with specified industry standards (e.g., EN/IEC standards, ATEX, specific company norms).
*   **Centralize Standard Rules:** Provide a single, authoritative source for applying and validating against standard-specific requirements, promoting consistency and reducing errors.
*   **Abstract Standard Complexity:** Decouple the core engineering [calculations](../calculations/calculations-architecture.md) from the intricate details of various standard requirements.
*   **Facilitate Updates:** Allow for easy updating or addition of new standards without modifying fundamental calculation logic.
*   **Enhance Safety & Reliability:** Incorporate safety factors and design limits mandated by standards to ensure robust and safe heat tracing systems.

**2. Location & Structure (`src/core/standards/`)**
The Standards Layer will be organized within `src/core/standards/`. Its structure will enable clear separation of concerns by standard or by type of standard rule.

```
src/core/standards/
├── __init__.py
├── tr_50410/                   # Sub-package for TR 50410 standard specific rules
│   ├── __init__.py
│   ├── heat_loss_factors.py    # TR 50410 specific heat loss factors or adjustments
│   ├── power_density_limits.py # TR 50410 allowed power densities
│   └── safety_factors.py       # TR 50410 specific safety factors for components
├── iec_60079_30_1/             # Sub-package for IEC 60079-30-1 (Hazardous Area) specific rules (Example)
│   ├── __init__.py
│   ├── temperature_class_limits.py # T-class limits for various heating elements
│   └── hazardous_area_compliance.py # Validation logic for ATEX/IECEx zones
├── general_standards/          # Sub-package for general or cross-standard rules
│   ├── __init__.py
│   ├── common_safety_factors.py # Universal safety factors (e.g., general electrical)
│   └── code_compliance.py      # General electrical code compliance checks
└── standards_manager.py        # Central interface for selecting and applying standards
```

**3. Core Responsibilities & Functionality**

*   **Standard-Specific Rule Application:**
    *   Each standard sub-module (e.g., `tr_50410.py`) will contain functions that apply specific rules, adjustments, or transformations based on that standard. This might involve modifying raw calculated values or selecting parameters based on standard lookups.
    *   **Example:** `apply_tr_50410_insulation_correction(heat_loss_data)` might adjust calculated heat loss based on standard-defined insulation properties or environmental conditions.
*   **Safety Factor Application:**
    *   The layer will include functions to apply various safety factors (e.g., thermal safety factors for heat loss, electrical safety factors for cable sizing) to calculated values.
    *   Safety factors will be retrieved based on the selected standard, component type, and application context.
    *   **Example:** `apply_electrical_safety_factor(power_draw, standard_id)`
*   **Standards-Based Validation:**
    *   This is a critical responsibility. Functions within this layer will take calculated parameters (e.g., required power, selected cable type, surface temperature) and explicitly check them against the limits, thresholds, and prescriptive requirements defined by the relevant standards.
    *   **Example:** `validate_hazardous_area_compliance(cable_temp_class, zone_type, gas_group)` will ensure the selected cable's temperature class is appropriate for a given hazardous area.
    *   **Example:** `validate_cable_power_density(cable_type, power_per_meter, standard_id)` checks if the power density complies with the standard's limits for that cable type.

    **Example (Standards-Based Validation Check):**

    ```python
    # Example in src/core/standards/iec_60079_30_1/hazardous_area_compliance.py
    from backend.core.errors.exceptions import StandardComplianceError # Assuming custom exception
    from backend.core.standards.iec_60079_30_1.temperature_class_limits import get_temperature_limit # Assuming this lookup exists

    def validate_hazardous_area_compliance(
        cable_temp_class: str,  # e.g., 'T4'
        zone_type: str,         # e.g., 'Zone 1'
        gas_group: str          # e.g., 'IIB'
    ):
        """Validates if the cable temperature class is safe for the given hazardous area."""
        required_temp_limit = get_temperature_limit(zone_type, gas_group)
        cable_temp_limit = get_temperature_limit_for_class(cable_temp_class) # Assuming this lookup exists

        if cable_temp_limit > required_temp_limit:
            raise StandardComplianceError(
                details=f"Cable temperature class {cable_temp_class} exceeds the maximum allowed temperature for {zone_type} with gas group {gas_group}."
            )
        # Additional checks for certification, etc. would follow
    ```

*   **Standard Selection & Management:**
    *   The `standards_manager.py` (or a similar facade) will provide mechanisms to:
        *   Activate specific standards for a given design context (e.g., based on project requirements or user selection).
        *   Retrieve standard-specific constants, lookup tables, or rule sets.
*   **Standard Data Management:** If certain standards involve complex data (e.g., extensive material compatibility matrices, environmental factors), this layer will define how that data is accessed (e.g., loaded from configuration, database, or static files within the `common_properties` module).

**4. Error Handling (Integration with [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy))

*   **Standard Compliance Violations:** When a calculated parameter or design decision fails to comply with a selected standard's requirements, the Standards Layer will raise specific exceptions.
    *   **`StandardComplianceError` (from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy)):** A general exception for standard violations.
    *   **More Specific Exceptions:** For critical violations, more granular exceptions might be defined:
        *   `HazardousAreaViolationError`: If a design violates hazardous area classifications (e.g., incorrect temperature class).
        *   `PowerLimitExceededError`: If calculated power exceeds standard limits.
*   **Detailed Error Messages:** Exceptions will include highly detailed messages indicating:
    *   Which standard was violated.
    *   Which specific rule or limit was exceeded.
    *   The actual value vs. the allowed limit.
    *   Suggestions for corrective action where possible.
*   **Propagation:** All such errors are captured and propagated up to the [Service Layer](../../services/services-architecture.md), which then handles logging, translation into user-friendly messages via the global [error handling framework](../../errors/errors-architecture.md).

**5. Interaction with Other Layers**

*   **Service Layer (Primary Consumer):**
    *   The `DesignService` or `CalculationService` ([`src/core/services/`](../../services/services-architecture.md)) will be the primary consumer of the Standards Layer.
    *   After performing initial raw [calculations](../calculations/calculations-architecture.md) (via the [Calculations Layer](../calculations/calculations-architecture.md)), the [Service Layer](../../services/services-architecture.md) will pass the results and relevant design parameters to the Standards Layer for validation and application of standard-mandated adjustments (e.g., applying safety factors or checking against maximum allowed temperatures).
    *   **Error Handling:** It will catch `StandardComplianceError` and its variants, logging them and converting them into appropriate [Service Layer](../../services/services-architecture.md) exceptions (e.g., `DesignComplianceFailureError`) before re-raising for the [API](../../../api/api-architecture.md).
*   **Calculations Layer:**
    *   The [Calculations Layer](../calculations/calculations-architecture.md) produces the raw engineering values. It typically does not directly call the Standards Layer for *validation*.
    *   However, the Standards Layer might provide standard-defined constants or properties (e.g., minimum insulation thickness for a given standard) that the [Calculations Layer](../calculations/calculations-architecture.md) can *consume* as inputs for its calculations.

    See the [Calculations Layer Architecture](../calculations/calculations-architecture.md) for more details.

*   **Schema Layer (`src/core/schemas/`):** Defines the input/output [schemas](../../schemas/schemas-architecture.md) for the functions within the Standards Layer, ensuring clear data contracts.

    See the [Schemas Layer Architecture](../../schemas/schemas-architecture.md) for more details.

*   **Configuration/Settings (`src/config/settings.py`):** May contain configuration for which standards are active by default or specific parameters for standard application (e.g., default safety factor values, standard version).

    See the [Configuration Layer Architecture](../../../config/config-architecture.md) for more details.

*   **UI/Frontend:** The frontend will receive detailed error messages from the [API](../../../api/api-architecture.md) when standards are violated, guiding the user to correct their design.

**6. Key Principles**

*   **Separation of Concerns:** Clearly separates core engineering logic from standard-specific rules and compliance checks.
*   **Modularity & Extensibility:** New standards can be added as new sub-modules without impacting existing logic. Updates to a standard are localized.
*   **Transparency & Auditability:** Provides clear mechanisms to identify which standards were applied and if any violations occurred.
*   **Safety by Design:** Embeds safety factors and compliance checks directly into the design process.
