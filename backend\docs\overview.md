# Backend Codebase Overview

This document provides a high-level overview of the backend codebase structure within the `src/` directory.

For detailed architectural specifications of each layer and module, please refer to the linked documents.
For practical guides on specific tasks, see the [How-To Guides](how-to/).

For a comprehensive overview of the backend architecture, including principles and implemented layers, see the [Backend Architecture](backend-architecture.md).

## Architectural Layers and Modules

Here is a list of the main architectural layers and modules with links to their detailed specifications:

*   [API Layer](api/api-architecture.md)
*   [Configuration Layer](config/config-architecture.md)
*   Core Layers:
    *   [Calculations Layer](core/calculations/calculations-architecture.md)
    *   [Database Layer](core/database/architecture.md)
        *   [Migrations Architecture](core/database/migrations-architecture.md)
        *   [Seeding Architecture](core/database/seeding-architecture.md)
        *   [Transaction Architecture](core/database/transaction-architecture.md)
    *   [Data Import Layer](core/data_import/data_import-architecture.md)
    *   [Error Handling Layer](core/errors/errors-architecture.md)
    *   [Model Layer](core/models/models-architecture.md)
        *   [Model Validation Architecture](core/models/validation-architecture.md)
    *   [Reports Layer](core/reports/reports-architecture.md)
    *   [Repositories Layer](core/repositories/repositories-architecture.md)
    *   [Schemas Layer](core/schemas/schemas-architecture.md)
    *   [Services Layer](core/services/services-architecture.md)
    *   [Standards Layer](core/standards/standards-architecture.md)
*   [Lifespan Events](lifespan-architecture.md)
*   [Middleware Layer](middleware/middleware-architecture.md)
*   [Secrets Management](secrets/secrets-architecture.md)
*   [Security Layer](security/security-architecture.md)
*   [Telemetry Layer](telemetry/telemetry-architecture.md)
*   [Utility Functions](utils/utils-architecture.md)

src/
├── app.py                      # Main FastAPI application instance, wires up layers, registers middleware
├── [config/](config/config-architecture.md)                     # Application configuration settings
│   └── settings.py
│
├── core/
│   ├── models/                 # SQLAlchemy ORM models (your domain entities)
│   │   ├── __init__.py
│   │   ├── base.py
│   │   ├── enums.py            # Global enums used across models
│   │   ├── project.py
│   │   ├── components.py       # e.g., Pipe, Insulation
│   │   ├── heat_tracing.py     # e.g., Circuit, HeatLossSegment
│   │   ├── electrical.py       # e.g., Cable, Load
│   │   ├── switchboard.py      # e.g., DistributionBoard, Feeder
│   │   ├── users.py
│   │   ├── audit.py            # Audit trail related models
│   │   └── documents.py        # Models related to generated reports/documents
│   │
│   ├── repositories/           # Abstraction over data persistence (SQLAlchemy operations)
│   │   ├── __init__.py
│   │   ├── base_repository.py  # Generic CRUD operations
│   │   ├── project_repository.py
│   │   ├── component_repository.py
│   │   ├── heat_tracing_repository.py
│   │   ├── electrical_repository.py
│   │   ├── switchboard_repository.py
│   │   ├── user_repository.py
│   │   └── document_repository.py
│   │
│   ├── services/               # Business logic, orchestrates repositories and other services
│   │   ├── __init__.py
│   │   ├── project_service.py
│   │   ├── calculation_service.py
│   │   ├── circuit_design_service.py
│   │   ├── report_service.py
│   │   ├── user_service.py
│   │   └── import_export_service.py
│   │
│   ├── schemas/                # Pydantic models for request/response validation and serialization
│   │   ├── __init__.py
│   │   ├── error.py            # Pydantic schema for standardized error responses
│   │   ├── base.py             # Base schemas for common fields (ID, timestamps)
│   │   ├── project_schemas.py
│   │   ├── component_schemas.py
│   │   ├── heat_tracing_schemas.py
│   │   ├── electrical_schemas.py
│   │   ├── user_schemas.py
│   │   ├── report_schemas.py
│   │   └── import_schemas.py   # Schemas specific to data import validation
│   │
│   ├── database/               # Database connection, engine, and session management
│   │   ├── __init__.py
│   │   ├── session.py          # Provides session factory and dependency for FastAPI
│   │   └── engine.py           # Database engine creation and metadata setup
│   │
│   ├── calculations/           # Core engineering calculation logic and algorithms
│   │   ├── __init__.py
│   │   ├── heat_loss_calcs.py
│   │   ├── cable_sizing_calcs.py
│   │   ├── circuit_analysis_calcs.py
│   │   └── utils.py            # Internal calculation utilities (e.g., specific math functions)
│   │
│   ├── standards/              # Implementation of engineering standards and compliance rules
│   │   ├── __init__.py
│   │   ├── compliance_rules.py
│   │   ├── tr_50410.py         # Example: TR 50410 specific rules
│   │   └── hazardous_area.py   # Example: Hazardous area classification logic
│   │
│   ├── reports/                # Report generation module for native document templates (XLSX, DOCX to PDF)
│   │   ├── __init__.py
│   │   ├── templates/          # Native report templates (e.g., .docx, .xlsx)
│   │   │   └── ...
│   │   ├── data_preparators/   # Logic to prepare data for specific reports
│   │   │   └── ...
│   │   ├── document_populator/ # Logic to fill data into native templates
│   │   │   └── ...
│   │   ├── pdf_converter/      # Utility for converting populated documents to PDF
│   │   │   └── ...
│   │   └── circuit_reports/    # Specific report types (e.g., heat_loss_report.py)
│   │       └── ...
│   │
│   ├── data_import/            # Module for importing data from external sources (.xlsx, .json, .csv)
│   │   ├── __init__.py
│   │   ├── global_importer.py
│   │   ├── project_importer.py
│   │   ├── parsers/            # File format-specific parsing logic
│   │   │   └── ...
│   │   ├── validators/         # Import-specific validation (beyond schemas)
│   │   │   └── ...
│   │   └── mappers/            # Mapping parsed data to ORM models
│   │       └── ...
│   │
│   └── errors/                 # Custom exceptions, error registry, error factory, handlers
│       ├── __init__.py
│       ├── error_factory.py
│       ├── error_registry.py
│       ├── error_templates.py
│       └── exceptions.py
│
├── middleware/                 # HTTP Request/Response interceptors
│   ├── __init__.py
│   ├── base.py                 # Base middleware class/interface
│   ├── caching.py
│   ├── context.py              # Request-scoped context (user, request_id, etc.)
│   ├── data.py                 # Compression, content negotiation
│   ├── error_handling.py       # Global exception catcher & HTTP error formatter
│   ├── logging_middleware.py   # Request/response logging
│   ├── monitoring.py           # Performance metrics collection
│   ├── rate_limiting.py
│   ├── request.py              # Request ID injection, API versioning
│   └── security.py             # Authentication (JWT), Authorization, Security Headers
│
├── api/                        # Web Framework specific routing, controllers (views)
│   ├── __init__.py
│   ├── v1/                     # API Version 1
│   │   ├── __init__.py
│   │   ├── project_routes.py   # e.g., /v1/projects endpoint definitions
│   │   ├── component_routes.py
│   │   ├── heat_tracing_routes.py
│   │   ├── electrical_routes.py
│   │   ├── user_routes.py
│   │   ├── auth_routes.py      # Login/logout, token issuance
│   │   └── report_routes.py    # Endpoints for report generation
│   │
│   └── main_router.py          # Aggregates all versioned routes
│
└── utils/                      # General utility functions, helpers
    ├── __init__.py
    ├── file_operations.py      # Generic file handling (e.g., temporary file management)
    └── common_helpers.py       # Miscellaneous, truly generic helper functions (e.g., UUID generation, date formatting)