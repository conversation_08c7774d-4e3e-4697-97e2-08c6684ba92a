# backend/core/calculations/calculation_service.py
"""
Calculation Service - Orchestrates engineering calculations.

This service acts as the main facade for all engineering calculations,
coordinating between different calculation modules and ensuring proper
error handling and validation.
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, Optional

from backend.core.errors.exceptions import (
    CalculationError,
    InvalidInputError,
    NotFoundError,
)

logger = logging.getLogger(__name__)


@dataclass
class CalculationInput:
    """Base class for calculation inputs."""

    pass


@dataclass
class CalculationResult:
    """Base class for calculation results."""

    calculation_type: str
    inputs: Dict[str, Any]
    outputs: Dict[str, Any]
    warnings: list[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []
        if self.metadata is None:
            self.metadata = {}


@dataclass
class HeatLossInput(CalculationInput):
    """Input parameters for heat loss calculations."""

    pipe_diameter: float  # meters
    pipe_length: float  # meters
    fluid_temperature: float  # Celsius
    ambient_temperature: float  # Celsius
    insulation_thickness: float  # meters
    insulation_type: str
    wind_speed: Optional[float] = 0.0  # m/s
    pipe_material: str = "carbon_steel"


@dataclass
class HeatLossResult(CalculationResult):
    """Results from heat loss calculations."""

    heat_loss_rate: float = 0.0  # Watts per meter
    total_heat_loss: float = 0.0  # Watts
    surface_temperature: float = 0.0  # Celsius
    required_power: float = 0.0  # Watts


@dataclass
class CableSizingInput(CalculationInput):
    """Input parameters for cable sizing calculations."""

    required_power: float  # Watts
    cable_length: float  # meters
    supply_voltage: float  # Volts
    ambient_temperature: float  # Celsius
    installation_method: str
    cable_type: Optional[str] = None


@dataclass
class CableSizingResult(CalculationResult):
    """Results from cable sizing calculations."""

    recommended_cable_type: str = ""
    cable_power_per_meter: float = 0.0  # Watts per meter
    total_cable_length: float = 0.0  # meters
    current_draw: float = 0.0  # Amperes
    voltage_drop: float = 0.0  # Volts
    power_density: float = 0.0  # Watts per square meter


class CalculationService:
    """
    Main service for orchestrating engineering calculations.

    This service provides a unified interface for all calculation operations,
    handling error management and coordination between different calculation modules.
    """

    def __init__(self):
        """Initialize the calculation service."""
        logger.debug("CalculationService initialized")

    def calculate_heat_loss(self, inputs: HeatLossInput) -> HeatLossResult:
        """
        Calculate heat loss for pipes and vessels.

        Args:
            inputs: Heat loss calculation parameters

        Returns:
            HeatLossResult: Calculated heat loss values

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(
            f"Starting heat loss calculation for pipe diameter: {inputs.pipe_diameter}m"
        )

        try:
            # Validate inputs
            self._validate_heat_loss_inputs(inputs)

            # Import calculation modules (lazy loading)
            from .heat_loss.insulation_properties import get_insulation_properties
            from .heat_loss.pipe_heat_loss import calculate_pipe_heat_loss

            # Get insulation properties
            try:
                insulation_props = get_insulation_properties(inputs.insulation_type)
                assert insulation_props is not None, (
                    "Insulation properties should not be None"
                )
            except NotFoundError as e:
                raise CalculationError(
                    f"Insulation type '{inputs.insulation_type}' not found: {e.detail}"
                )

            # Perform heat loss calculation
            heat_loss_rate = calculate_pipe_heat_loss(
                diameter=inputs.pipe_diameter,
                fluid_temp=inputs.fluid_temperature,
                ambient_temp=inputs.ambient_temperature,
                insulation_thickness=inputs.insulation_thickness,
                insulation_conductivity=insulation_props["thermal_conductivity"],
                wind_speed=inputs.wind_speed or 0.0,
            )

            # Calculate total heat loss
            total_heat_loss = heat_loss_rate * inputs.pipe_length

            # Calculate surface temperature (simplified)
            surface_temp = self._calculate_surface_temperature(
                inputs.fluid_temperature,
                inputs.ambient_temperature,
                inputs.insulation_thickness,
                insulation_props["thermal_conductivity"],
            )

            # Calculate required heating power (with safety factor)
            required_power = total_heat_loss * 1.2  # 20% safety factor

            result = HeatLossResult(
                calculation_type="heat_loss",
                inputs=inputs.__dict__,
                outputs={
                    "heat_loss_rate": heat_loss_rate,
                    "total_heat_loss": total_heat_loss,
                    "surface_temperature": surface_temp,
                    "required_power": required_power,
                },
                heat_loss_rate=heat_loss_rate,
                total_heat_loss=total_heat_loss,
                surface_temperature=surface_temp,
                required_power=required_power,
                metadata={
                    "insulation_properties": insulation_props,
                    "safety_factor": 1.2,
                },
            )

            logger.info(f"Heat loss calculation completed: {heat_loss_rate:.2f} W/m")
            return result

        except (InvalidInputError, NotFoundError):
            raise
        except Exception as e:
            logger.error(f"Heat loss calculation failed: {e}", exc_info=True)
            raise CalculationError(details=f"Heat loss calculation failed: {str(e)}")

    def calculate_cable_sizing(self, inputs: CableSizingInput) -> CableSizingResult:
        """
        Calculate cable sizing for heating circuits.

        Args:
            inputs: Cable sizing calculation parameters

        Returns:
            CableSizingResult: Calculated cable sizing values

        Raises:
            InvalidInputError: If input parameters are invalid
            CalculationError: If calculation fails
        """
        logger.info(f"Starting cable sizing calculation for {inputs.required_power}W")

        try:
            # Validate inputs
            self._validate_cable_sizing_inputs(inputs)

            # Import calculation modules
            from .electrical_sizing.cable_sizing import (
                calculate_cable_parameters,
                select_optimal_cable,
            )
            from .electrical_sizing.voltage_drop import calculate_voltage_drop

            # Select optimal cable type
            cable_selection = select_optimal_cable(
                required_power=inputs.required_power,
                cable_length=inputs.cable_length,
                ambient_temp=inputs.ambient_temperature,
                installation_method=inputs.installation_method,
                preferred_type=inputs.cable_type,
            )

            if not cable_selection:
                raise CalculationError(
                    details="No suitable cable found for the given requirements"
                )

            # Calculate cable parameters
            cable_params = calculate_cable_parameters(
                cable_type=cable_selection["type"],
                required_power=inputs.required_power,
                cable_length=inputs.cable_length,
                supply_voltage=inputs.supply_voltage,
            )

            # Calculate voltage drop
            voltage_drop = calculate_voltage_drop(
                current=cable_params["current_draw"],
                length=inputs.cable_length,
                cable_resistance=cable_selection["resistance_per_meter"],
            )

            result = CableSizingResult(
                calculation_type="cable_sizing",
                inputs=inputs.__dict__,
                outputs={
                    "recommended_cable_type": cable_selection["type"],
                    "cable_power_per_meter": cable_params["power_per_meter"],
                    "total_cable_length": cable_params["total_length"],
                    "current_draw": cable_params["current_draw"],
                    "voltage_drop": voltage_drop,
                    "power_density": cable_params["power_density"],
                },
                recommended_cable_type=cable_selection["type"],
                cable_power_per_meter=cable_params["power_per_meter"],
                total_cable_length=cable_params["total_length"],
                current_draw=cable_params["current_draw"],
                voltage_drop=voltage_drop,
                power_density=cable_params["power_density"],
                metadata={
                    "cable_selection": cable_selection,
                    "cable_parameters": cable_params,
                },
            )

            logger.info(
                f"Cable sizing calculation completed: {cable_selection['type']}"
            )
            return result

        except (InvalidInputError, NotFoundError):
            raise
        except Exception as e:
            logger.error(f"Cable sizing calculation failed: {e}", exc_info=True)
            raise CalculationError(details=f"Cable sizing calculation failed: {str(e)}")

    def _validate_heat_loss_inputs(self, inputs: HeatLossInput) -> None:
        """Validate heat loss calculation inputs."""
        if inputs.pipe_diameter <= 0:
            raise InvalidInputError("Pipe diameter must be positive")

        if inputs.pipe_length <= 0:
            raise InvalidInputError("Pipe length must be positive")

        if inputs.fluid_temperature <= inputs.ambient_temperature:
            raise InvalidInputError(
                "Fluid temperature must be higher than ambient temperature"
            )

        if inputs.insulation_thickness < 0:
            raise InvalidInputError("Insulation thickness cannot be negative")

        if inputs.wind_speed and inputs.wind_speed < 0:
            raise InvalidInputError("Wind speed cannot be negative")

    def _validate_cable_sizing_inputs(self, inputs: CableSizingInput) -> None:
        """Validate cable sizing calculation inputs."""
        if inputs.required_power <= 0:
            raise InvalidInputError("Required power must be positive")

        if inputs.cable_length <= 0:
            raise InvalidInputError("Cable length must be positive")

        if inputs.supply_voltage <= 0:
            raise InvalidInputError("Supply voltage must be positive")

        if not inputs.installation_method:
            raise InvalidInputError("Installation method is required")

    def _calculate_surface_temperature(
        self,
        fluid_temp: float,
        ambient_temp: float,
        insulation_thickness: float,
        thermal_conductivity: float,
    ) -> float:
        """Calculate approximate surface temperature (simplified model)."""
        # Simplified calculation - in reality this would be more complex
        temp_drop = (fluid_temp - ambient_temp) * (
            insulation_thickness * thermal_conductivity
        )
        return fluid_temp - temp_drop
