# backend/core/schemas/user_schemas.py
"""
Pydantic schemas for User entities validation, serialization, and deserialization.

This module defines the data contracts for User-related operations including:
- User schemas: For user account management and authentication
- UserPreference schemas: For user preferences and application settings
- Authentication schemas: For login, logout, and session management
- Security schemas: For password management and access control
- Role-based access control schemas: For permission management
"""

from typing import Any, Dict, List, Optional

from pydantic import (
    BaseModel,
    ConfigDict,
    EmailStr,
    Field,
    field_validator,
    model_validator,
)

from .base import BaseSoftDeleteSchema, PaginatedResponseSchema

# ============================================================================
# USER SCHEMAS
# ============================================================================


class UserBaseSchema(BaseModel):
    """Base schema for User with common fields."""

    name: str = Field(..., min_length=2, max_length=100, description="User full name")
    email: Optional[EmailStr] = Field(None, description="User email address")
    is_active: bool = Field(True, description="User active status")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate user name."""
        if not v or not v.strip():
            raise ValueError("User name cannot be empty")
        return v.strip()


class UserCreateSchema(UserBaseSchema):
    """Schema for creating a new user."""

    password: str = Field(
        ..., min_length=8, max_length=128, description="User password"
    )

    @field_validator("password")
    @classmethod
    def validate_password(cls, v: str) -> str:
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        # Check for at least one uppercase, one lowercase, and one digit
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )

        return v

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "John Smith",
                "email": "<EMAIL>",
                "password": "SecurePass123",
                "is_active": True,
            }
        }
    )


class UserUpdateSchema(BaseModel):
    """Schema for updating an existing user."""

    name: Optional[str] = Field(
        None, min_length=2, max_length=100, description="User full name"
    )
    email: Optional[EmailStr] = Field(None, description="User email address")
    is_active: Optional[bool] = Field(None, description="User active status")

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(UserBaseSchema.validate_name)


class UserReadSchema(UserBaseSchema):
    """Schema for reading/displaying user data."""

    id: int = Field(..., description="User ID")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "John Smith",
                "email": "<EMAIL>",
                "is_active": True,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
            }
        },
    )


class UserSummarySchema(BaseModel):
    """Lightweight schema for user listings."""

    id: int = Field(..., description="User ID")
    name: str = Field(..., description="User name")
    email: Optional[EmailStr] = Field(None, description="User email")
    is_active: bool = Field(..., description="User active status")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# USER PREFERENCE SCHEMAS
# ============================================================================


class UserPreferenceBaseSchema(BaseModel):
    """Base schema for UserPreference with common fields."""

    ui_theme: str = Field("light", description="UI theme preference")
    default_min_ambient_temp_c: Optional[float] = Field(
        None,
        ge=-50,
        le=80,
        description="Default minimum ambient temperature in Celsius",
    )
    default_max_ambient_temp_c: Optional[float] = Field(
        None,
        ge=-50,
        le=80,
        description="Default maximum ambient temperature in Celsius",
    )
    default_desired_maintenance_temp_c: Optional[float] = Field(
        None,
        ge=-50,
        le=200,
        description="Default desired maintenance temperature in Celsius",
    )
    default_safety_margin_percent: float = Field(
        0.0, ge=0, le=100, description="Default safety margin percentage"
    )
    preferred_cable_manufacturers_json: Optional[str] = Field(
        None, description="Preferred cable manufacturers (JSON)"
    )
    preferred_control_device_manufacturers_json: Optional[str] = Field(
        None, description="Preferred control device manufacturers (JSON)"
    )

    @field_validator("ui_theme")
    @classmethod
    def validate_ui_theme(cls, v: str) -> str:
        """Validate UI theme."""
        allowed_themes = ["light", "dark", "auto"]
        if v not in allowed_themes:
            raise ValueError(f"UI theme must be one of: {', '.join(allowed_themes)}")
        return v

    @model_validator(mode="after")
    def validate_temperature_range(self):
        """Validate temperature range."""
        if (
            self.default_max_ambient_temp_c is not None
            and self.default_min_ambient_temp_c is not None
            and self.default_max_ambient_temp_c <= self.default_min_ambient_temp_c
        ):
            raise ValueError(
                "Maximum ambient temperature must be higher than minimum ambient temperature"
            )
        return self


class UserPreferenceCreateSchema(UserPreferenceBaseSchema):
    """Schema for creating user preferences."""

    user_id: int = Field(..., description="User ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_id": 1,
                "ui_theme": "dark",
                "default_min_ambient_temp_c": -10.0,
                "default_max_ambient_temp_c": 40.0,
                "default_desired_maintenance_temp_c": 65.0,
                "default_safety_margin_percent": 20.0,
            }
        }
    )


class UserPreferenceUpdateSchema(BaseModel):
    """Schema for updating user preferences."""

    ui_theme: Optional[str] = Field(None, description="UI theme preference")
    default_min_ambient_temp_c: Optional[float] = Field(
        None,
        ge=-50,
        le=80,
        description="Default minimum ambient temperature in Celsius",
    )
    default_max_ambient_temp_c: Optional[float] = Field(
        None,
        ge=-50,
        le=80,
        description="Default maximum ambient temperature in Celsius",
    )
    default_desired_maintenance_temp_c: Optional[float] = Field(
        None,
        ge=-50,
        le=200,
        description="Default desired maintenance temperature in Celsius",
    )
    default_safety_margin_percent: Optional[float] = Field(
        None, ge=0, le=100, description="Default safety margin percentage"
    )
    preferred_cable_manufacturers_json: Optional[str] = Field(
        None, description="Preferred cable manufacturers (JSON)"
    )
    preferred_control_device_manufacturers_json: Optional[str] = Field(
        None, description="Preferred control device manufacturers (JSON)"
    )

    # Apply the same validators as create schema
    _validate_ui_theme = field_validator("ui_theme")(
        UserPreferenceBaseSchema.validate_ui_theme
    )


class UserPreferenceReadSchema(UserPreferenceBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying user preference data."""

    user_id: int = Field(..., description="User ID")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "user_id": 1,
                "ui_theme": "dark",
                "default_min_ambient_temp_c": -10.0,
                "default_max_ambient_temp_c": 40.0,
                "default_desired_maintenance_temp_c": 65.0,
                "default_safety_margin_percent": 20.0,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


# ============================================================================
# AUTHENTICATION SCHEMAS
# ============================================================================


class LoginRequestSchema(BaseModel):
    """Schema for user login request."""

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=1, description="User password")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "password": "SecurePass123",
            }
        }
    )


class LoginResponseSchema(BaseModel):
    """Schema for user login response."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserReadSchema = Field(..., description="User information")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 3600,
                "user": {
                    "id": 1,
                    "name": "John Smith",
                    "email": "<EMAIL>",
                    "is_active": True,
                },
            }
        }
    )


class LogoutResponseSchema(BaseModel):
    """Schema for user logout response."""

    message: str = Field(..., description="Logout confirmation message")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "Successfully logged out",
            }
        }
    )


class PasswordChangeRequestSchema(BaseModel):
    """Schema for password change request."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(
        ..., min_length=8, max_length=128, description="New password"
    )

    @field_validator("new_password")
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        # Check for at least one uppercase, one lowercase, and one digit
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )

        return v

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "current_password": "OldPass123",
                "new_password": "NewSecurePass456",
            }
        }
    )


class PasswordResetRequestSchema(BaseModel):
    """Schema for password reset request."""

    email: EmailStr = Field(..., description="User email address")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
            }
        }
    )


class PasswordResetConfirmSchema(BaseModel):
    """Schema for password reset confirmation."""

    token: str = Field(..., description="Password reset token")
    new_password: str = Field(
        ..., min_length=8, max_length=128, description="New password"
    )

    @field_validator("new_password")
    @classmethod
    def validate_new_password(cls, v: str) -> str:
        """Validate new password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")

        # Check for at least one uppercase, one lowercase, and one digit
        has_upper = any(c.isupper() for c in v)
        has_lower = any(c.islower() for c in v)
        has_digit = any(c.isdigit() for c in v)

        if not (has_upper and has_lower and has_digit):
            raise ValueError(
                "Password must contain at least one uppercase letter, "
                "one lowercase letter, and one digit"
            )

        return v

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "token": "reset_token_here",
                "new_password": "NewSecurePass456",
            }
        }
    )


# ============================================================================
# SESSION MANAGEMENT SCHEMAS
# ============================================================================


class UserSessionSchema(BaseModel):
    """Schema for user session information."""

    user_id: int = Field(..., description="User ID")
    session_token: str = Field(..., description="Session token")
    expires_at: str = Field(..., description="Session expiration timestamp")
    is_active: bool = Field(..., description="Session active status")
    created_at: str = Field(..., description="Session creation timestamp")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "user_id": 1,
                "session_token": "session_token_here",
                "expires_at": "2024-01-16T10:30:00Z",
                "is_active": True,
                "created_at": "2024-01-15T10:30:00Z",
            }
        },
    )


# ============================================================================
# PAGINATED RESPONSE SCHEMAS
# ============================================================================


class UserPaginatedResponseSchema(PaginatedResponseSchema):
    """Schema for paginated user list responses."""

    users: List[UserSummarySchema] = Field(..., description="List of users")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "users": [
                    {
                        "id": 1,
                        "name": "John Smith",
                        "email": "<EMAIL>",
                        "is_active": True,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )
