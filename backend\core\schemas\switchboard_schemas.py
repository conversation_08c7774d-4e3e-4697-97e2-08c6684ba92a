# backend/core/schemas/switchboard_schemas.py
"""
Pydantic schemas for Switchboard entities validation, serialization, and deserialization.

This module defines the data contracts for Switchboard-related operations including:
- Switchboard schemas: For electrical distribution panels and switchboards
- SwitchboardComponent schemas: For components within switchboards
- Feeder schemas: For electrical feeders and distribution circuits
- FeederComponent schemas: For components within feeders
- Electrical integration schemas: For load distribution and capacity management
- Validation schemas: For electrical distribution constraints and business logic
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from ..models.enums import SwitchboardType
from .base import BaseSoftDeleteSchema, PaginatedResponseSchema

# ============================================================================
# SWITCHBOARD SCHEMAS
# ============================================================================


class SwitchboardBaseSchema(BaseModel):
    """Base schema for Switchboard with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Switchboard name/identifier"
    )
    location: Optional[str] = Field(
        None, max_length=200, description="Switchboard location description"
    )
    voltage_level_v: int = Field(
        ..., gt=0, le=50000, description="Voltage level in volts"
    )
    number_of_phases: int = Field(
        ..., ge=1, le=3, description="Number of electrical phases (1 or 3)"
    )
    type: Optional[SwitchboardType] = Field(
        None, description="Switchboard type classification"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate switchboard name."""
        if not v or not v.strip():
            raise ValueError("Switchboard name cannot be empty")
        return v.strip()

    @field_validator("number_of_phases")
    @classmethod
    def validate_phases(cls, v: int) -> int:
        """Validate number of phases."""
        if v not in [1, 3]:
            raise ValueError("Number of phases must be 1 or 3")
        return v


class SwitchboardCreateSchema(SwitchboardBaseSchema):
    """Schema for creating a new switchboard."""

    project_id: int = Field(..., description="Project ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Main Distribution Board",
                "project_id": 1,
                "location": "Main electrical room",
                "voltage_level_v": 415,
                "number_of_phases": 3,
                "type": "MAIN",
            }
        }
    )


class SwitchboardUpdateSchema(BaseModel):
    """Schema for updating an existing switchboard."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Switchboard name/identifier"
    )
    location: Optional[str] = Field(
        None, max_length=200, description="Switchboard location description"
    )
    voltage_level_v: Optional[int] = Field(
        None, gt=0, le=50000, description="Voltage level in volts"
    )
    number_of_phases: Optional[int] = Field(
        None, ge=1, le=3, description="Number of electrical phases (1 or 3)"
    )
    type: Optional[SwitchboardType] = Field(
        None, description="Switchboard type classification"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(SwitchboardBaseSchema.validate_name)
    _validate_phases = field_validator("number_of_phases")(
        SwitchboardBaseSchema.validate_phases
    )


class SwitchboardReadSchema(SwitchboardBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying switchboard data."""

    project_id: int = Field(..., description="Project ID")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Main Distribution Board",
                "project_id": 1,
                "location": "Main electrical room",
                "voltage_level_v": 415,
                "number_of_phases": 3,
                "type": "MAIN",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class SwitchboardSummarySchema(BaseModel):
    """Lightweight schema for switchboard listings."""

    id: int = Field(..., description="Switchboard ID")
    name: str = Field(..., description="Switchboard name")
    project_id: int = Field(..., description="Project ID")
    location: Optional[str] = Field(None, description="Switchboard location")
    voltage_level_v: int = Field(..., description="Voltage level in volts")
    number_of_phases: int = Field(..., description="Number of electrical phases")
    type: Optional[SwitchboardType] = Field(None, description="Switchboard type")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# FEEDER SCHEMAS
# ============================================================================


class FeederBaseSchema(BaseModel):
    """Base schema for Feeder with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Feeder name/identifier"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate feeder name."""
        if not v or not v.strip():
            raise ValueError("Feeder name cannot be empty")
        return v.strip()


class FeederCreateSchema(FeederBaseSchema):
    """Schema for creating a new feeder."""

    switchboard_id: int = Field(..., description="Parent switchboard ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Feeder 01 - Heat Tracing",
                "switchboard_id": 1,
            }
        }
    )


class FeederUpdateSchema(BaseModel):
    """Schema for updating an existing feeder."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Feeder name/identifier"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(FeederBaseSchema.validate_name)


class FeederReadSchema(FeederBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying feeder data."""

    switchboard_id: int = Field(..., description="Parent switchboard ID")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Feeder 01 - Heat Tracing",
                "switchboard_id": 1,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class FeederSummarySchema(BaseModel):
    """Lightweight schema for feeder listings."""

    id: int = Field(..., description="Feeder ID")
    name: str = Field(..., description="Feeder name")
    switchboard_id: int = Field(..., description="Parent switchboard ID")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# SWITCHBOARD COMPONENT SCHEMAS
# ============================================================================


class SwitchboardComponentBaseSchema(BaseModel):
    """Base schema for SwitchboardComponent with common fields."""

    quantity: int = Field(1, ge=1, le=100, description="Component quantity")
    position: Optional[str] = Field(
        None, max_length=50, description="Component position in switchboard"
    )

    @field_validator("position")
    @classmethod
    def validate_position(cls, v: Optional[str]) -> Optional[str]:
        """Validate component position."""
        if v is not None and v.strip():
            return v.strip()
        return v


class SwitchboardComponentCreateSchema(SwitchboardComponentBaseSchema):
    """Schema for creating a new switchboard component."""

    switchboard_id: int = Field(..., description="Switchboard ID")
    component_id: int = Field(..., description="Component ID from catalog")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "switchboard_id": 1,
                "component_id": 15,
                "quantity": 2,
                "position": "Row 1, Position 3-4",
            }
        }
    )


class SwitchboardComponentUpdateSchema(BaseModel):
    """Schema for updating an existing switchboard component."""

    quantity: Optional[int] = Field(
        None, ge=1, le=100, description="Component quantity"
    )
    position: Optional[str] = Field(
        None, max_length=50, description="Component position in switchboard"
    )

    # Apply the same validators as create schema
    _validate_position = field_validator("position")(
        SwitchboardComponentBaseSchema.validate_position
    )


class SwitchboardComponentReadSchema(SwitchboardComponentBaseSchema):
    """Schema for reading/displaying switchboard component data."""

    id: int = Field(..., description="Switchboard component ID")
    switchboard_id: int = Field(..., description="Switchboard ID")
    component_id: int = Field(..., description="Component ID from catalog")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "switchboard_id": 1,
                "component_id": 15,
                "quantity": 2,
                "position": "Row 1, Position 3-4",
            }
        },
    )


class SwitchboardComponentSummarySchema(BaseModel):
    """Lightweight schema for switchboard component listings."""

    id: int = Field(..., description="Switchboard component ID")
    switchboard_id: int = Field(..., description="Switchboard ID")
    component_id: int = Field(..., description="Component ID from catalog")
    quantity: int = Field(..., description="Component quantity")
    position: Optional[str] = Field(None, description="Component position")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# FEEDER COMPONENT SCHEMAS
# ============================================================================


class FeederComponentBaseSchema(BaseModel):
    """Base schema for FeederComponent with common fields."""

    quantity: int = Field(1, ge=1, le=100, description="Component quantity")
    position: Optional[str] = Field(
        None, max_length=50, description="Component position in feeder"
    )

    @field_validator("position")
    @classmethod
    def validate_position(cls, v: Optional[str]) -> Optional[str]:
        """Validate component position."""
        if v is not None and v.strip():
            return v.strip()
        return v


class FeederComponentCreateSchema(FeederComponentBaseSchema):
    """Schema for creating a new feeder component."""

    feeder_id: int = Field(..., description="Feeder ID")
    component_id: int = Field(..., description="Component ID from catalog")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "feeder_id": 1,
                "component_id": 25,
                "quantity": 1,
                "position": "Output Terminal",
            }
        }
    )


class FeederComponentUpdateSchema(BaseModel):
    """Schema for updating an existing feeder component."""

    quantity: Optional[int] = Field(
        None, ge=1, le=100, description="Component quantity"
    )
    position: Optional[str] = Field(
        None, max_length=50, description="Component position in feeder"
    )

    # Apply the same validators as create schema
    _validate_position = field_validator("position")(
        FeederComponentBaseSchema.validate_position
    )


class FeederComponentReadSchema(FeederComponentBaseSchema):
    """Schema for reading/displaying feeder component data."""

    id: int = Field(..., description="Feeder component ID")
    feeder_id: int = Field(..., description="Feeder ID")
    component_id: int = Field(..., description="Component ID from catalog")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "feeder_id": 1,
                "component_id": 25,
                "quantity": 1,
                "position": "Output Terminal",
            }
        },
    )


class FeederComponentSummarySchema(BaseModel):
    """Lightweight schema for feeder component listings."""

    id: int = Field(..., description="Feeder component ID")
    feeder_id: int = Field(..., description="Feeder ID")
    component_id: int = Field(..., description="Component ID from catalog")
    quantity: int = Field(..., description="Component quantity")
    position: Optional[str] = Field(None, description="Component position")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# ELECTRICAL INTEGRATION SCHEMAS
# ============================================================================


class SwitchboardLoadSummarySchema(BaseModel):
    """Schema for switchboard load distribution summary."""

    switchboard_id: int = Field(..., description="Switchboard ID")
    total_connected_load_kw: float = Field(
        ..., description="Total connected load in kW"
    )
    total_demand_load_kw: float = Field(..., description="Total demand load in kW")
    load_factor: float = Field(..., description="Load factor")
    diversity_factor: float = Field(..., description="Diversity factor")
    available_capacity_kw: float = Field(..., description="Available capacity in kW")
    utilization_percent: float = Field(..., description="Utilization percentage")
    feeder_count: int = Field(..., description="Number of feeders")

    model_config = ConfigDict(from_attributes=True)


class SwitchboardCapacityAnalysisSchema(BaseModel):
    """Schema for switchboard capacity analysis."""

    switchboard_id: int = Field(..., description="Switchboard ID")
    rated_capacity_kva: float = Field(..., description="Rated capacity in kVA")
    current_load_kva: float = Field(..., description="Current load in kVA")
    available_capacity_kva: float = Field(..., description="Available capacity in kVA")
    capacity_utilization_percent: float = Field(
        ..., description="Capacity utilization percentage"
    )
    is_overloaded: bool = Field(..., description="Overload status")
    recommendations: List[str] = Field(
        default_factory=list, description="Capacity recommendations"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# PAGINATED RESPONSE SCHEMAS
# ============================================================================


class SwitchboardPaginatedResponseSchema(PaginatedResponseSchema):
    """Schema for paginated switchboard list responses."""

    switchboards: List[SwitchboardSummarySchema] = Field(
        ..., description="List of switchboards"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "switchboards": [
                    {
                        "id": 1,
                        "name": "Main Distribution Board",
                        "project_id": 1,
                        "location": "Main electrical room",
                        "voltage_level_v": 415,
                        "number_of_phases": 3,
                        "type": "MAIN",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class FeederPaginatedResponseSchema(PaginatedResponseSchema):
    """Schema for paginated feeder list responses."""

    feeders: List[FeederSummarySchema] = Field(..., description="List of feeders")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "feeders": [
                    {
                        "id": 1,
                        "name": "Feeder 01 - Heat Tracing",
                        "switchboard_id": 1,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class SwitchboardComponentPaginatedResponseSchema(PaginatedResponseSchema):
    """Schema for paginated switchboard component list responses."""

    switchboard_components: List[SwitchboardComponentSummarySchema] = Field(
        ..., description="List of switchboard components"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "switchboard_components": [
                    {
                        "id": 1,
                        "switchboard_id": 1,
                        "component_id": 15,
                        "quantity": 2,
                        "position": "Row 1, Position 3-4",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class FeederComponentPaginatedResponseSchema(PaginatedResponseSchema):
    """Schema for paginated feeder component list responses."""

    feeder_components: List[FeederComponentSummarySchema] = Field(
        ..., description="List of feeder components"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "feeder_components": [
                    {
                        "id": 1,
                        "feeder_id": 1,
                        "component_id": 25,
                        "quantity": 1,
                        "position": "Output Terminal",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )
