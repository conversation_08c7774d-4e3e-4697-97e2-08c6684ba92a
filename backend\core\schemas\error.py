# src/schemas/error.py
from pydantic import BaseModel, Field
from typing import Optional

class ErrorResponseSchema(BaseModel):
    code: str = Field(..., description="Unique application-specific error code.")
    detail: str = Field(..., description="A human-readable explanation of the error.")
    category: str = Field(..., description="Category of the error (e.g., ClientError, ServerError, Validation).")
    status_code: int = Field(..., description="HTTP status code equivalent (for UI/API compatibility).")
    metadata: Optional[dict] = Field(None, description="Additional context or debugging information.")

    class Config:
        json_schema_extra = {
            "example": {
                "code": "404_001",
                "detail": "Project with ID 'XYZ-123' not found.",
                "category": "ClientError",
                "status_code": 404,
                "metadata": {"project_id": "XYZ-123", "requested_by": "<EMAIL>"}
            }
        }

# If you have specific validation error details from Pydantic
class ValidationErrorDetail(BaseModel):
    loc: list[str | int] = Field(..., description="Location of the validation error in the input data.")
    msg: str = Field(..., description="Validation error message.")
    type: str = Field(..., description="Type of validation error.")

class ValidationErrorsResponseSchema(ErrorResponseSchema):
    validation_details: list[ValidationErrorDetail] = Field(..., description="Detailed list of validation errors.")

    class Config:
        json_schema_extra = {
            "example": {
                "code": "400_001",
                "detail": "Input validation failed.",
                "category": "Validation",
                "status_code": 400,
                "validation_details": [
                    {"loc": ["pipe_length"], "msg": "value is not a valid float", "type": "type_error.float"}
                ]
            }
        }