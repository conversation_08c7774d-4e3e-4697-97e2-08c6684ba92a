# backend/core/standards/iec_60079_30_1/hazardous_area_compliance.py
"""
IEC 60079-30-1 Hazardous Area Compliance.

This module implements hazardous area compliance validation
according to IEC 60079-30-1.
"""

import logging
from typing import Dict, Any, List

from backend.core.errors.exceptions import StandardComplianceError

logger = logging.getLogger(__name__)

# Gas groups and their properties
GAS_GROUPS = {
    "IIA": {
        "description": "Propane, butane, acetone, ammonia",
        "ignition_energy": "high",
        "flame_speed": "low"
    },
    "IIB": {
        "description": "Ethylene, dimethyl ether",
        "ignition_energy": "medium", 
        "flame_speed": "medium"
    },
    "IIC": {
        "description": "Hydrogen, acetylene",
        "ignition_energy": "low",
        "flame_speed": "high"
    }
}

# Zone types and requirements
ZONE_REQUIREMENTS = {
    "Zone 0": {
        "description": "Explosive atmosphere present continuously",
        "equipment_category": "1G",
        "protection_level": "very_high",
        "allowed_equipment": ["ia", "ma"]
    },
    "Zone 1": {
        "description": "Explosive atmosphere likely to occur in normal operation",
        "equipment_category": "2G", 
        "protection_level": "high",
        "allowed_equipment": ["ia", "ib", "ma", "mb", "d", "e", "p"]
    },
    "Zone 2": {
        "description": "Explosive atmosphere unlikely to occur",
        "equipment_category": "3G",
        "protection_level": "enhanced",
        "allowed_equipment": ["ia", "ib", "ic", "ma", "mb", "mc", "d", "e", "p", "n"]
    }
}


def validate_hazardous_area_compliance(
    cable_temp_class: str,
    zone_type: str,
    gas_group: str,
    protection_type: str = "heating_cable"
) -> Dict[str, Any]:
    """
    Validate hazardous area compliance for heating cables.
    
    Args:
        cable_temp_class: Temperature class of cable (T1-T6)
        zone_type: Hazardous area zone (Zone 0, Zone 1, Zone 2)
        gas_group: Gas group (IIA, IIB, IIC)
        protection_type: Type of protection
        
    Returns:
        Dict with compliance validation results
        
    Raises:
        StandardComplianceError: If compliance requirements are not met
    """
    logger.debug(f"Validating hazardous area compliance: {zone_type}, {gas_group}, {cable_temp_class}")
    
    violations = []
    warnings = []
    
    # Validate inputs
    if zone_type not in ZONE_REQUIREMENTS:
        raise StandardComplianceError(f"Invalid zone type: {zone_type}")
    
    if gas_group not in GAS_GROUPS:
        raise StandardComplianceError(f"Invalid gas group: {gas_group}")
    
    zone_req = ZONE_REQUIREMENTS[zone_type]
    gas_props = GAS_GROUPS[gas_group]
    
    # Check temperature class compatibility
    if not _validate_temperature_class_for_gas(cable_temp_class, gas_group):
        violations.append(
            f"Temperature class {cable_temp_class} may not be suitable for gas group {gas_group}"
        )
    
    # Check zone compatibility
    if zone_type == "Zone 0":
        violations.append(
            "Zone 0 applications require special intrinsically safe heating cables - "
            "standard heating cables are not permitted"
        )
    elif zone_type == "Zone 1":
        warnings.append(
            "Zone 1 applications require certified heating cables with appropriate protection type"
        )
    
    # Check gas group specific requirements
    if gas_group == "IIC":
        warnings.append(
            "Gas group IIC (hydrogen/acetylene) requires enhanced safety measures and "
            "lower surface temperatures"
        )
    
    # Additional safety requirements
    safety_requirements = _get_safety_requirements(zone_type, gas_group)
    
    is_compliant = len(violations) == 0
    
    result = {
        "is_compliant": is_compliant,
        "zone_type": zone_type,
        "gas_group": gas_group,
        "temperature_class": cable_temp_class,
        "violations": violations,
        "warnings": warnings,
        "safety_requirements": safety_requirements,
        "zone_requirements": zone_req,
        "gas_properties": gas_props
    }
    
    if not is_compliant:
        raise StandardComplianceError(
            f"Hazardous area compliance failed: {'; '.join(violations)}"
        )
    
    logger.debug("Hazardous area compliance validation passed")
    return result


def _validate_temperature_class_for_gas(temp_class: str, gas_group: str) -> bool:
    """Validate temperature class is appropriate for gas group."""
    
    # More restrictive requirements for higher risk gas groups
    if gas_group == "IIC":
        # Hydrogen/acetylene - require lower temperatures
        return temp_class in ["T4", "T5", "T6"]
    elif gas_group == "IIB":
        # Ethylene - moderate restrictions
        return temp_class in ["T3", "T4", "T5", "T6"]
    else:  # IIA
        # Propane/butane - less restrictive
        return temp_class in ["T2", "T3", "T4", "T5", "T6"]


def _get_safety_requirements(zone_type: str, gas_group: str) -> List[str]:
    """Get safety requirements for zone and gas group combination."""
    
    requirements = []
    
    # Base requirements for all zones
    requirements.extend([
        "Use certified heating cables suitable for hazardous areas",
        "Ensure proper earthing and bonding",
        "Install appropriate monitoring and control systems"
    ])
    
    # Zone-specific requirements
    if zone_type == "Zone 0":
        requirements.extend([
            "Use intrinsically safe heating cables only",
            "Install certified safety barriers",
            "Implement redundant safety systems"
        ])
    elif zone_type == "Zone 1":
        requirements.extend([
            "Use explosion-proof or intrinsically safe equipment",
            "Install certified control panels",
            "Implement temperature monitoring"
        ])
    elif zone_type == "Zone 2":
        requirements.extend([
            "Use equipment suitable for Zone 2 or better",
            "Install basic temperature monitoring",
            "Follow good electrical practices"
        ])
    
    # Gas group specific requirements
    if gas_group == "IIC":
        requirements.extend([
            "Use enhanced safety measures for hydrogen/acetylene",
            "Implement lower surface temperature limits",
            "Consider additional ventilation requirements"
        ])
    elif gas_group == "IIB":
        requirements.extend([
            "Use appropriate protection for ethylene environments",
            "Monitor for gas accumulation"
        ])
    
    return requirements


def get_recommended_protection_types(zone_type: str) -> List[str]:
    """Get recommended protection types for hazardous area zone."""
    
    if zone_type not in ZONE_REQUIREMENTS:
        raise StandardComplianceError(f"Invalid zone type: {zone_type}")
    
    return ZONE_REQUIREMENTS[zone_type]["allowed_equipment"]


def validate_cable_certification(
    cable_type: str,
    zone_type: str,
    gas_group: str,
    temp_class: str
) -> Dict[str, Any]:
    """
    Validate cable certification requirements.
    
    Args:
        cable_type: Type of heating cable
        zone_type: Hazardous area zone
        gas_group: Gas group
        temp_class: Temperature class
        
    Returns:
        Dict with certification validation results
    """
    logger.debug(f"Validating cable certification for {cable_type}")
    
    required_certifications = []
    
    # Base certification requirements
    required_certifications.append("IECEx certification")
    required_certifications.append("ATEX certification")
    
    # Zone-specific certifications
    if zone_type == "Zone 0":
        required_certifications.append("Category 1G certification")
    elif zone_type == "Zone 1":
        required_certifications.append("Category 2G certification")
    elif zone_type == "Zone 2":
        required_certifications.append("Category 3G certification")
    
    # Temperature class certification
    required_certifications.append(f"Temperature class {temp_class} certification")
    
    # Gas group certification
    required_certifications.append(f"Gas group {gas_group} certification")
    
    return {
        "cable_type": cable_type,
        "required_certifications": required_certifications,
        "zone_type": zone_type,
        "gas_group": gas_group,
        "temperature_class": temp_class,
        "certification_standard": "IEC 60079-30-1"
    }
