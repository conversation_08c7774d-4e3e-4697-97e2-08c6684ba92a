# backend/tests/test_repositories/test_electrical_repository.py
"""
Tests for Electrical repositories data access operations.

This module tests the repository layer for Electrical entities including:
- ElectricalNodeRepository
- CableRouteRepository
- CableSegmentRepository
- LoadCalculationRepository
- VoltageDropCalculationRepository
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import SQLAlchemyError

from backend.core.repositories.electrical_repository import (
    ElectricalNodeRepository,
    CableRouteRepository,
    CableSegmentRepository,
    LoadCalculationRepository,
    VoltageDropCalculationRepository,
)
from backend.core.models.electrical import (
    ElectricalNode,
    CableRoute,
    CableSegment,
    LoadCalculation,
    VoltageDropCalculation,
)
from backend.core.models.enums import ElectricalNodeType, CableInstallationMethod


class TestElectricalNodeRepository:
    """Test ElectricalNodeRepository operations."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock()

    @pytest.fixture
    def electrical_node_repo(self, mock_db_session):
        """Create an ElectricalNodeRepository instance."""
        return ElectricalNodeRepository(mock_db_session)

    @pytest.fixture
    def sample_electrical_node(self):
        """Create a sample electrical node for testing."""
        node = Mock(spec=ElectricalNode)
        node.id = 1
        node.name = "Main Switchboard"
        node.project_id = 1
        node.node_type = ElectricalNodeType.SWITCHBOARD_INCOMING
        node.voltage_v = 415.0
        node.power_capacity_kva = 100.0
        node.is_deleted = False
        return node

    def test_get_by_project_id_success(self, electrical_node_repo, mock_db_session, sample_electrical_node):
        """Test successful retrieval of electrical nodes by project ID."""
        # Arrange
        project_id = 1
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_electrical_node]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = electrical_node_repo.get_by_project_id(project_id)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_electrical_node
        mock_db_session.scalars.assert_called_once()

    def test_get_by_project_id_database_error(self, electrical_node_repo, mock_db_session):
        """Test database error handling in get_by_project_id."""
        # Arrange
        project_id = 1
        mock_db_session.scalars.side_effect = SQLAlchemyError("Database error")

        # Mock the _handle_db_exception method to raise an exception
        with patch.object(electrical_node_repo, '_handle_db_exception', side_effect=Exception("Database error")):
            # Act & Assert
            with pytest.raises(Exception, match="Database error"):
                electrical_node_repo.get_by_project_id(project_id)

    def test_get_by_node_type_success(self, electrical_node_repo, mock_db_session, sample_electrical_node):
        """Test successful retrieval of electrical nodes by type."""
        # Arrange
        project_id = 1
        node_type = "SWITCHBOARD_INCOMING"
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_electrical_node]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = electrical_node_repo.get_by_node_type(project_id, node_type)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_electrical_node
        mock_db_session.scalars.assert_called_once()

    def test_get_nodes_with_capacity_success(self, electrical_node_repo, mock_db_session, sample_electrical_node):
        """Test successful retrieval of electrical nodes with capacity."""
        # Arrange
        project_id = 1
        min_capacity = 50.0
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_electrical_node]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = electrical_node_repo.get_nodes_with_capacity(project_id, min_capacity)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_electrical_node
        mock_db_session.scalars.assert_called_once()

    def test_count_by_project_success(self, electrical_node_repo, mock_db_session):
        """Test successful counting of electrical nodes by project."""
        # Arrange
        project_id = 1
        expected_count = 5
        mock_db_session.scalar.return_value = expected_count

        # Act
        result = electrical_node_repo.count_by_project(project_id)

        # Assert
        assert result == expected_count
        mock_db_session.scalar.assert_called_once()

    def test_count_by_project_none_result(self, electrical_node_repo, mock_db_session):
        """Test counting electrical nodes when result is None."""
        # Arrange
        project_id = 1
        mock_db_session.scalar.return_value = None

        # Act
        result = electrical_node_repo.count_by_project(project_id)

        # Assert
        assert result == 0
        mock_db_session.scalar.assert_called_once()


class TestCableRouteRepository:
    """Test CableRouteRepository operations."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock()

    @pytest.fixture
    def cable_route_repo(self, mock_db_session):
        """Create a CableRouteRepository instance."""
        return CableRouteRepository(mock_db_session)

    @pytest.fixture
    def sample_cable_route(self):
        """Create a sample cable route for testing."""
        route = Mock(spec=CableRoute)
        route.id = 1
        route.name = "Main Feed"
        route.project_id = 1
        route.from_node_id = 1
        route.to_node_id = 2
        route.length_m = 150.0
        route.installation_method = CableInstallationMethod.CABLE_TRAY
        route.calculated_voltage_drop_v = 8.5
        route.is_deleted = False
        return route

    def test_get_routes_by_node_both_directions(self, cable_route_repo, mock_db_session, sample_cable_route):
        """Test retrieval of cable routes by node in both directions."""
        # Arrange
        node_id = 1
        direction = "both"
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_cable_route]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = cable_route_repo.get_routes_by_node(node_id, direction)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_cable_route
        mock_db_session.scalars.assert_called_once()

    def test_get_routes_by_node_invalid_direction(self, cable_route_repo):
        """Test invalid direction parameter in get_routes_by_node."""
        # Arrange
        node_id = 1
        invalid_direction = "invalid"

        # Act & Assert
        with pytest.raises(ValueError, match="Direction must be"):
            cable_route_repo.get_routes_by_node(node_id, invalid_direction)

    def test_get_routes_by_installation_method_success(self, cable_route_repo, mock_db_session, sample_cable_route):
        """Test successful retrieval of cable routes by installation method."""
        # Arrange
        project_id = 1
        installation_method = "CABLE_TRAY"
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_cable_route]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = cable_route_repo.get_routes_by_installation_method(project_id, installation_method)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_cable_route
        mock_db_session.scalars.assert_called_once()

    def test_get_routes_with_high_voltage_drop_success(self, cable_route_repo, mock_db_session, sample_cable_route):
        """Test successful retrieval of cable routes with high voltage drop."""
        # Arrange
        project_id = 1
        max_voltage_drop_percent = 3.0
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_cable_route]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = cable_route_repo.get_routes_with_high_voltage_drop(project_id, max_voltage_drop_percent)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_cable_route
        mock_db_session.scalars.assert_called_once()


class TestLoadCalculationRepository:
    """Test LoadCalculationRepository operations."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock()

    @pytest.fixture
    def load_calc_repo(self, mock_db_session):
        """Create a LoadCalculationRepository instance."""
        return LoadCalculationRepository(mock_db_session)

    @pytest.fixture
    def sample_load_calculation(self):
        """Create a sample load calculation for testing."""
        load_calc = Mock(spec=LoadCalculation)
        load_calc.id = 1
        load_calc.name = "Heat Tracing Load"
        load_calc.project_id = 1
        load_calc.electrical_node_id = 2
        load_calc.load_type = "heat_tracing"
        load_calc.rated_power_kw = 2.5
        load_calc.calculated_operating_power_kw = 2.0
        load_calc.is_deleted = False
        return load_calc

    def test_get_by_electrical_node_id_success(self, load_calc_repo, mock_db_session, sample_load_calculation):
        """Test successful retrieval of load calculations by electrical node ID."""
        # Arrange
        electrical_node_id = 2
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_load_calculation]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = load_calc_repo.get_by_electrical_node_id(electrical_node_id)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_load_calculation
        mock_db_session.scalars.assert_called_once()

    def test_get_by_load_type_success(self, load_calc_repo, mock_db_session, sample_load_calculation):
        """Test successful retrieval of load calculations by load type."""
        # Arrange
        project_id = 1
        load_type = "heat_tracing"
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_load_calculation]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = load_calc_repo.get_by_load_type(project_id, load_type)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_load_calculation
        mock_db_session.scalars.assert_called_once()

    def test_get_total_power_by_node_success(self, load_calc_repo, mock_db_session):
        """Test successful calculation of total power by node."""
        # Arrange
        electrical_node_id = 2
        expected_total_power = 5.5
        mock_db_session.scalar.return_value = expected_total_power

        # Act
        result = load_calc_repo.get_total_power_by_node(electrical_node_id)

        # Assert
        assert result == expected_total_power
        mock_db_session.scalar.assert_called_once()

    def test_get_total_power_by_node_none_result(self, load_calc_repo, mock_db_session):
        """Test total power calculation when result is None."""
        # Arrange
        electrical_node_id = 2
        mock_db_session.scalar.return_value = None

        # Act
        result = load_calc_repo.get_total_power_by_node(electrical_node_id)

        # Assert
        assert result == 0.0
        mock_db_session.scalar.assert_called_once()


class TestVoltageDropCalculationRepository:
    """Test VoltageDropCalculationRepository operations."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock()

    @pytest.fixture
    def voltage_drop_repo(self, mock_db_session):
        """Create a VoltageDropCalculationRepository instance."""
        return VoltageDropCalculationRepository(mock_db_session)

    @pytest.fixture
    def sample_voltage_drop_calculation(self):
        """Create a sample voltage drop calculation for testing."""
        vd_calc = Mock(spec=VoltageDropCalculation)
        vd_calc.id = 1
        vd_calc.name = "Voltage Drop Calc"
        vd_calc.project_id = 1
        vd_calc.cable_route_id = 1
        vd_calc.calculated_voltage_drop_percent = 3.5
        vd_calc.is_compliant = True
        vd_calc.calculation_method = "IEC"
        vd_calc.is_deleted = False
        return vd_calc

    def test_get_by_cable_route_id_success(self, voltage_drop_repo, mock_db_session, sample_voltage_drop_calculation):
        """Test successful retrieval of voltage drop calculations by cable route ID."""
        # Arrange
        cable_route_id = 1
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_voltage_drop_calculation]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = voltage_drop_repo.get_by_cable_route_id(cable_route_id)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_voltage_drop_calculation
        mock_db_session.scalars.assert_called_once()

    def test_get_non_compliant_calculations_success(self, voltage_drop_repo, mock_db_session, sample_voltage_drop_calculation):
        """Test successful retrieval of non-compliant voltage drop calculations."""
        # Arrange
        project_id = 1
        sample_voltage_drop_calculation.is_compliant = False
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_voltage_drop_calculation]
        mock_db_session.scalars.return_value = mock_scalars

        # Act
        result = voltage_drop_repo.get_non_compliant_calculations(project_id)

        # Assert
        assert len(result) == 1
        assert result[0] == sample_voltage_drop_calculation
        assert result[0].is_compliant is False
        mock_db_session.scalars.assert_called_once()

    def test_get_average_voltage_drop_by_project_success(self, voltage_drop_repo, mock_db_session):
        """Test successful calculation of average voltage drop by project."""
        # Arrange
        project_id = 1
        expected_avg = 3.2
        mock_db_session.scalar.return_value = expected_avg

        # Act
        result = voltage_drop_repo.get_average_voltage_drop_by_project(project_id)

        # Assert
        assert result == expected_avg
        mock_db_session.scalar.assert_called_once()

    def test_count_by_compliance_status_success(self, voltage_drop_repo, mock_db_session):
        """Test successful counting by compliance status."""
        # Arrange
        project_id = 1
        is_compliant = True
        expected_count = 8
        mock_db_session.scalar.return_value = expected_count

        # Act
        result = voltage_drop_repo.count_by_compliance_status(project_id, is_compliant)

        # Assert
        assert result == expected_count
        mock_db_session.scalar.assert_called_once()
