from sqlalchemy import <PERSON><PERSON>ey, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, EnumType, SoftDeleteColumns
from .components import Component
# from .electrical import ElectricalNode
from .enums import SwitchboardType
# from .heat_tracing import HTCircuit
# from .project import Project


class Switchboard(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Switchboard"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    location: Mapped[str | None] = mapped_column(nullable=True)
    voltage_level_v: Mapped[int] = mapped_column(nullable=False)
    number_of_phases: Mapped[int] = mapped_column(nullable=False)
    type: Mapped[SwitchboardType | None] = mapped_column(
        EnumType(SwitchboardType), nullable=True
    )

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="switchboards")
    feeders: Mapped[list["Feeder"]] = relationship(
        back_populates="switchboard", cascade="all, delete-orphan"
    )
    switchboard_components: Mapped[list["SwitchboardComponent"]] = relationship(
        back_populates="switchboard", cascade="all, delete-orphan"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="related_switchboard", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("project_id", "name", name="uq_switchboard_project_name"),
    )

    def __repr__(self):
        return f"<Switchboard(id={self.id}, name='{self.name}', project_id={self.project_id})>"


class Feeder(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Feeder"

    switchboard_id: Mapped[int] = mapped_column(
        ForeignKey("Switchboard.id"), nullable=False
    )

    # Relationships
    switchboard: Mapped["Switchboard"] = relationship(back_populates="feeders")
    htcircuits: Mapped[list["HTCircuit"]] = relationship(
        back_populates="feeder", cascade="all, delete-orphan"
    )
    feeder_components: Mapped[list["FeederComponent"]] = relationship(
        back_populates="feeder", cascade="all, delete-orphan"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="related_feeder", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("switchboard_id", "name", name="uq_feeder_switchboard_name"),
    )

    def __repr__(self):
        return f"<Feeder(id={self.id}, name='{self.name}', switchboard_id={self.switchboard_id})>"


# Junction tables for components specific to switchboards and feeders
class SwitchboardComponent(Base):
    __tablename__ = "SwitchboardComponent"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    switchboard_id: Mapped[int] = mapped_column(
        ForeignKey("Switchboard.id"), nullable=False
    )
    component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    quantity: Mapped[int] = mapped_column(default=1)
    position: Mapped[str | None] = mapped_column(nullable=True)

    switchboard: Mapped["Switchboard"] = relationship(
        back_populates="switchboard_components"
    )
    component: Mapped["Component"] = relationship()

    __table_args__ = (
        UniqueConstraint(
            "switchboard_id", "component_id", "position", name="uq_swbd_comp"
        ),
    )

    def __repr__(self):
        return f"<SwitchboardComponent(swbd_id={self.switchboard_id}, comp_id={self.component_id}, qty={self.quantity})>"


class FeederComponent(Base):
    __tablename__ = "FeederComponent"
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    feeder_id: Mapped[int] = mapped_column(ForeignKey("Feeder.id"), nullable=False)
    component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )
    quantity: Mapped[int] = mapped_column(default=1)
    position: Mapped[str | None] = mapped_column(nullable=True)

    feeder: Mapped["Feeder"] = relationship(back_populates="feeder_components")
    component: Mapped["Component"] = relationship()

    __table_args__ = (
        UniqueConstraint(
            "feeder_id", "component_id", "position", name="uq_feeder_comp"
        ),
    )

    def __repr__(self):
        return f"<FeederComponent(feeder_id={self.feeder_id}, comp_id={self.component_id}, qty={self.quantity})>"
