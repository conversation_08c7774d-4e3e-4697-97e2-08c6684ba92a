### Calculations Layer Architecture Specification (Updated with Specifics)

**1. Purpose & Role**
The Calculations Layer is dedicated to encapsulating and executing all complex engineering formulas, algorithms, and domain-specific logic required for heat tracing design. Its primary purpose is to:
* **Centralize Engineering Expertise:** All calculation routines reside in a single, well-defined location.
* **Ensure Accuracy & Reliability:** Provide a controlled environment for precise implementation and rigorous testing of critical calculations.
* **Optimize Performance:** Allow for specific optimizations for computationally intensive tasks.
* **Promote Reusability:** Make individual calculation components reusable across different features.
* **Decouple Logic:** Separate complex calculation algorithms from data access, API handling, and general business orchestration.

**2. Location & Structure (`src/core/calculations/`)**
The Calculations Layer is organized within `src/core/calculations/`. It adopts a highly modular structure to prevent large, unwieldy files, breaking down calculations into logical sub-modules based on their engineering domain or function.

```
src/core/calculations/
├── __init__.py
├── heat_loss/                      # Sub-package for heat loss calculations
│   ├── __init__.py
│   ├── pipe_heat_loss.py           # Functions for pipe heat loss
│   ├── vessel_heat_loss.py         # Functions for vessel heat loss
│   └── insulation_properties.py    # Lookup/calculation of insulation properties
├── electrical_sizing/              # Sub-package for electrical sizing calculations
│   ├── __init__.py
│   ├── cable_sizing.py             # Functions for cable sizing (amps, length, material)
│   └── voltage_drop.py             # Functions for voltage drop calculations
├── circuit_design/                 # Sub-package for circuit design logic
│   ├── __init__.py
│   ├── circuit_breaker_sizing.py   # Breaker sizing calculations
│   └── control_circuit_logic.py    # Logic for control circuit design
├── common_properties/              # Sub-package for common material properties/lookups
│   ├── __init__.py
│   ├── material_data.py            # Static or loaded material properties
│   └── fluid_properties.py         # Fluid properties calculation/lookup
├── utils/                          # Sub-package for calculation-specific utilities
│   ├── __init__.py
│   ├── input_parser.py             # Utility for parsing raw input data into calc-ready formats
│   ├── validation_rules.py         # Reusable validation rules for calculation inputs/outputs
│   ├── math_helpers.py             # Common mathematical functions (e.g., interpolation, curve fitting)
│   └── units_conversion.py         # Specific unit conversions only relevant to calculations (if not global)
└── calculation_service.py          # Orchestrates calls to various calculation modules
```

**3. Core Responsibilities & Functionality**

*   **Input Data Transformation:** Transforms structured input ([Pydantic schemas](../../schemas/schemas-architecture.md) from the [Service Layer](../../services/services-architecture.md)) into formats suitable for calculation algorithms.
*   **Calculation-Specific Validation:** Performs detailed validation checks intrinsic to the engineering calculation itself, *raising exceptions from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy) when validation fails.*
    *   **Range Checks:** Ensures numerical inputs (e.g., `temperature`, `pressure`) fall within physically meaningful and algorithmically supported ranges. If invalid, raises `InvalidInputError` with specific details.
    *   **Compatibility Checks:** Validates combinations of inputs (e.g., `insulation_type` with `pipe_material`). If incompatible, raises `InvalidInputError` or a more specific custom error if warranted.
    *   **Physical Constraints:** Checks if inputs would lead to physically impossible scenarios before computation, pre-emptively raising `CalculationError`.
*   **Algorithm Execution:** Implements the core engineering formulas and algorithms.
    *   **Specific Calculation Functions:** The layer will include functions like:
        *   `calculate_required_power`: Determines the heating power needed for a given pipe or vessel based on heat loss, desired temperature, and environmental factors.
        *   `select_cable_type`: Recommends suitable heating cable types from a defined catalog based on criteria such as required power, operating temperature, and hazardous area certifications.
        *   `calculate_cable_parameters`: Computes necessary cable parameters like total length, required spacing, number of heating circuits, and associated power draw for a given design.
        *   `calculate_ssr_power_options`: Calculates various power percentage options for Solid State Relays (SSR) to achieve desired temperature control, accounting for heater characteristics and supply voltage.
*   **Output Formatting:** Structures calculation results into Pydantic schemas.
*   **Precision & Units Management:** Ensures numerical precision and handles unit consistency.
*   **Robust Error Handling (Crucial Integration):**
    *   **Algorithmic Failures:** Catches and handles internal computational issues (e.g., division by zero, non-converging iterative solutions, missing lookup data). These are converted into and raised as **`CalculationError`** from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy). The `CalculationError` should include contextual information (e.g., input values that led to the error, the specific internal calculation step that failed) to aid debugging at higher layers.
    *   **Missing or Invalid Lookup Data:** If calculations depend on internal data (e.g., material properties from `common_properties/material_data.py`) and a required property is not found or is invalid, a **`NotFoundError`** or `InvalidDataError` (from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy)) should be raised, indicating the specific missing/invalid data point.
    *   **Early Exit:** When an unrecoverable calculation error occurs, the function should immediately raise the appropriate exception, preventing further erroneous computation.

    For a detailed explanation of error handling, see the [Error and Exception Handling Architecture](../../errors/errors-architecture.md).

**4. Calculation Utilities (`src/core/calculations/utils/`)**

This dedicated `utils` sub-package within the `calculations` layer ensures that helper functions specific to the mathematical and engineering domain are co-located:

*   **`input_parser.py`:** Includes logic to parse and sanitize various raw input data formats into a structure suitable for direct algorithmic consumption. **Should raise `InvalidInputError` if parsing fails due to malformed data.**
*   **`validation_rules.py`:** Houses reusable validation functions. **These functions will return boolean or raise `InvalidInputError` directly.**
    *   **Dedicated Validation Functions:** Includes specific functions like `validate_cable_selection` which performs comprehensive checks on a selected cable against criteria such as:
        *   Temperature limits (e.g., maximum exposure temperature, maximum operating temperature).
        *   Hazardous area certifications (e.g., ATEX, IECEx compliance for the selected cable type in the specified zone).
        *   Maximum permissible cable length for the given power output and voltage.
        *   Power output limits (e.g., ensuring cable power is within expected ranges).
*   **`math_helpers.py`:** Generic mathematical functions, interpolation routines, curve-fitting algorithms, or statistical helpers commonly used across multiple engineering calculations. **Should handle potential mathematical errors (e.g., `ZeroDivisionError`) and raise `CalculationError` with contextual info.**
*   **`units_conversion.py`:** (Optional) Specific unit conversion helpers that are *only* relevant to the nuances of engineering calculations within this layer, distinct from general application-wide unit conversions. **Should raise `InvalidInputError` or `CalculationError` if conversion is impossible or outside valid ranges.**

**5. Use of Constants**
*   Physical properties, standard material specifications, and fixed cable characteristics (e.g., Ohm's law constants, thermal conductivity of standard insulation types, maximum power per meter for specific cable series) are defined as **constants** within the relevant calculation sub-modules (e.g., `insulation_properties.py`, `cable_sizing.py`, `material_data.py`). This approach improves readability, maintainability, and ensures consistency in engineering data used across calculations.

**6. Interaction with Other Layers (Emphasizing Error Flow)**

*   **Service Layer (Primary Consumer):** The `CalculationService` ([`src/core/services/calculation_service.py`](../../services/services-architecture.md)) acts as the facade. It receives high-level calculation requests and calls functions within `src/core/calculations/`.
    *   **Error Handling:** The [Service Layer](../../services/services-architecture.md) is responsible for **catching `CalculationError`, `InvalidInputError`, `NotFoundError` (or other application errors) raised by the Calculations Layer.** It then decides whether to:
        *   Log the detailed error.
        *   Translate the `CalculationError` into a more user-friendly Service Layer exception (e.g., `ProjectCalculationFailedError`) before re-raising it for the API layer.
        *   Potentially attempt alternative calculation paths or provide default values if the error is non-critical and recoverable.
*   **API Layer:** Calls the `CalculationService`.

    See the [API Layer Architecture](../../../api/api-architecture.md) for more details.

    *   **Error Handling:** Relies on global exception handlers (defined in [`src/core/errors/handlers.py`](../../errors/errors-architecture.md#3-error-handling-middleware-srccoreerrorserror_handlingpy)) to catch the Service Layer's exceptions (which might originate from the Calculations Layer) and map them to appropriate HTTP status codes and standardized JSON error responses.
*   **Schema Layer (`src/core/schemas/`):** Defines calculation input/output [Pydantic schemas](../../schemas/schemas-architecture.md). Pydantic's built-in validation will catch initial data format issues *before* it even reaches the Calculations Layer.
*   **Logging:** Detailed internal calculation errors, warnings, and their contexts are logged extensively within the Calculations Layer.

    See the [How-To: Configure Logging](../../../how-to/how-to_logging.md) for more details.

*   **Telemetry:** Traces and metrics capture the execution paths and performance of calculations, and also record when `CalculationError` or `InvalidInputError` are raised, providing observability into calculation failures.

    See the [Telemetry Architecture](../../../telemetry/telemetry-architecture.md) for more details.
