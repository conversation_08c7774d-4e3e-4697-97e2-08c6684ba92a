# src/core/errors/error_registry.py
from typing import Literal

# Define error categories (e.g., for logging or UI display)
ErrorCategory = Literal["ClientError", "ServerError", "DatabaseError", "Validation", "Calculation"]

ERROR_REGISTRY = {
    "404_001": {"category": "ClientError", "message_key": "PROJECT_NOT_FOUND", "http_status": 404},
    "409_001": {"category": "ClientError", "message_key": "COMPONENT_ALREADY_EXISTS", "http_status": 409},
    "400_001": {"category": "Validation", "message_key": "VALIDATION_ERROR", "http_status": 400},
    "500_001": {"category": "DatabaseError", "message_key": "DB_OPERATION_FAILED", "http_status": 500},
    "422_001": {"category": "Calculation", "message_key": "CALCULATION_INPUT_INVALID", "http_status": 422},
    # ... more error codes
}