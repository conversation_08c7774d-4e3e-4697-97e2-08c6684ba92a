# backend/core/calculations/heat_loss/pipe_heat_loss.py
"""
Pipe Heat Loss Calculations.

This module implements heat loss calculations for pipes according to
engineering standards and best practices.
"""

import math
import logging
from typing import Dict, Any

from backend.core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)

# Physical constants
STEFAN_BOLTZMANN_CONSTANT = 5.67e-8  # W/(m²·K⁴)
AIR_THERMAL_CONDUCTIVITY = 0.026  # W/(m·K) at 20°C
AIR_KINEMATIC_VISCOSITY = 15.1e-6  # m²/s at 20°C
AIR_PRANDTL_NUMBER = 0.71  # dimensionless


def calculate_pipe_heat_loss(
    diameter: float,
    fluid_temp: float,
    ambient_temp: float,
    insulation_thickness: float,
    insulation_conductivity: float,
    wind_speed: float = 0.0,
    pipe_material: str = "carbon_steel",
    surface_emissivity: float = 0.8
) -> float:
    """
    Calculate heat loss from an insulated pipe.
    
    This function implements a comprehensive heat loss calculation considering
    conduction through insulation, convection to ambient air, and radiation.
    
    Args:
        diameter: Pipe outer diameter (m)
        fluid_temp: Fluid temperature (°C)
        ambient_temp: Ambient air temperature (°C)
        insulation_thickness: Insulation thickness (m)
        insulation_conductivity: Thermal conductivity of insulation (W/m·K)
        wind_speed: Wind speed (m/s)
        pipe_material: Pipe material type
        surface_emissivity: Surface emissivity for radiation (0-1)
        
    Returns:
        float: Heat loss rate (W/m)
        
    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating pipe heat loss: D={diameter}m, T_fluid={fluid_temp}°C")
    
    try:
        # Validate inputs
        _validate_pipe_inputs(diameter, fluid_temp, ambient_temp, insulation_thickness)
        
        # Convert temperatures to Kelvin
        T_fluid_K = fluid_temp + 273.15
        T_ambient_K = ambient_temp + 273.15
        
        # Calculate pipe geometry
        pipe_radius = diameter / 2
        insulated_radius = pipe_radius + insulation_thickness
        insulated_diameter = 2 * insulated_radius
        
        # Calculate thermal resistance through insulation (per unit length)
        if insulation_thickness > 0:
            R_insulation = math.log(insulated_radius / pipe_radius) / (2 * math.pi * insulation_conductivity)
        else:
            R_insulation = 0.0
        
        # Calculate surface temperature (iterative approach)
        T_surface_K = _calculate_surface_temperature_iterative(
            T_fluid_K,
            T_ambient_K,
            R_insulation,
            insulated_diameter,
            wind_speed,
            surface_emissivity
        )
        
        # Calculate total heat loss
        if R_insulation > 0:
            # Heat loss through insulation
            heat_loss = (T_fluid_K - T_surface_K) / R_insulation
        else:
            # Direct heat loss from pipe surface
            h_conv = _calculate_convection_coefficient(insulated_diameter, T_surface_K, T_ambient_K, wind_speed)
            h_rad = _calculate_radiation_coefficient(T_surface_K, T_ambient_K, surface_emissivity)
            h_total = h_conv + h_rad
            
            heat_loss = h_total * math.pi * insulated_diameter * (T_surface_K - T_ambient_K)
        
        logger.debug(f"Calculated heat loss: {heat_loss:.2f} W/m")
        return heat_loss
        
    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Pipe heat loss calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Pipe heat loss calculation failed: {str(e)}")


def _validate_pipe_inputs(diameter: float, fluid_temp: float, ambient_temp: float, insulation_thickness: float) -> None:
    """Validate pipe heat loss calculation inputs."""
    if diameter <= 0:
        raise InvalidInputError("Pipe diameter must be positive")
    
    if diameter > 10.0:  # 10m diameter seems unreasonable
        raise InvalidInputError("Pipe diameter exceeds reasonable limits (>10m)")
    
    if fluid_temp <= ambient_temp:
        raise InvalidInputError("Fluid temperature must be higher than ambient temperature")
    
    if fluid_temp > 1000:  # °C
        raise InvalidInputError("Fluid temperature exceeds reasonable limits (>1000°C)")
    
    if ambient_temp < -100 or ambient_temp > 100:  # °C
        raise InvalidInputError("Ambient temperature outside reasonable range (-100 to 100°C)")
    
    if insulation_thickness < 0:
        raise InvalidInputError("Insulation thickness cannot be negative")
    
    if insulation_thickness > 1.0:  # 1m insulation seems excessive
        raise InvalidInputError("Insulation thickness exceeds reasonable limits (>1m)")


def _calculate_surface_temperature_iterative(
    T_fluid_K: float,
    T_ambient_K: float,
    R_insulation: float,
    diameter: float,
    wind_speed: float,
    emissivity: float,
    max_iterations: int = 20,
    tolerance: float = 0.1
) -> float:
    """
    Calculate surface temperature using iterative method.
    
    The surface temperature depends on convection and radiation coefficients,
    which in turn depend on the surface temperature. This requires iteration.
    """
    # Initial guess: average of fluid and ambient temperatures
    T_surface_K = (T_fluid_K + T_ambient_K) / 2
    
    for i in range(max_iterations):
        # Calculate heat transfer coefficients at current surface temperature
        h_conv = _calculate_convection_coefficient(diameter, T_surface_K, T_ambient_K, wind_speed)
        h_rad = _calculate_radiation_coefficient(T_surface_K, T_ambient_K, emissivity)
        h_total = h_conv + h_rad
        
        # Calculate external thermal resistance
        R_external = 1 / (h_total * math.pi * diameter)
        
        # Calculate new surface temperature
        if R_insulation > 0:
            T_surface_new = T_ambient_K + (T_fluid_K - T_ambient_K) * R_external / (R_insulation + R_external)
        else:
            T_surface_new = T_ambient_K + (T_fluid_K - T_ambient_K) * R_external / R_external
        
        # Check convergence
        if abs(T_surface_new - T_surface_K) < tolerance:
            logger.debug(f"Surface temperature converged after {i+1} iterations: {T_surface_new-273.15:.1f}°C")
            return T_surface_new
        
        T_surface_K = T_surface_new
    
    logger.warning(f"Surface temperature calculation did not converge after {max_iterations} iterations")
    return T_surface_K


def _calculate_convection_coefficient(diameter: float, T_surface_K: float, T_ambient_K: float, wind_speed: float) -> float:
    """
    Calculate convection heat transfer coefficient.
    
    Uses correlations for natural and forced convection around cylinders.
    """
    # Film temperature for property evaluation
    T_film = (T_surface_K + T_ambient_K) / 2
    
    # Grashof number for natural convection
    g = 9.81  # m/s²
    beta = 1 / T_film  # thermal expansion coefficient for ideal gas
    delta_T = abs(T_surface_K - T_ambient_K)
    
    Gr = g * beta * delta_T * (diameter ** 3) / (AIR_KINEMATIC_VISCOSITY ** 2)
    
    # Rayleigh number
    Ra = Gr * AIR_PRANDTL_NUMBER
    
    # Natural convection Nusselt number (Churchill-Chu correlation)
    if Ra < 1e12:
        Nu_nat = (0.6 + (0.387 * (Ra ** (1/6))) / ((1 + (0.559 / AIR_PRANDTL_NUMBER) ** (9/16)) ** (8/27))) ** 2
    else:
        Nu_nat = 0.1 * (Ra ** (1/3))
    
    # Forced convection (if wind speed > 0)
    if wind_speed > 0.1:  # m/s
        Re = wind_speed * diameter / AIR_KINEMATIC_VISCOSITY
        
        # Hilpert correlation for flow over cylinder
        if Re < 4:
            Nu_forced = 0.989 * (Re ** 0.330) * (AIR_PRANDTL_NUMBER ** (1/3))
        elif Re < 40:
            Nu_forced = 0.911 * (Re ** 0.385) * (AIR_PRANDTL_NUMBER ** (1/3))
        elif Re < 4000:
            Nu_forced = 0.683 * (Re ** 0.466) * (AIR_PRANDTL_NUMBER ** (1/3))
        elif Re < 40000:
            Nu_forced = 0.193 * (Re ** 0.618) * (AIR_PRANDTL_NUMBER ** (1/3))
        else:
            Nu_forced = 0.027 * (Re ** 0.805) * (AIR_PRANDTL_NUMBER ** (1/3))
        
        # Combined natural and forced convection
        Nu = (Nu_nat ** 3 + Nu_forced ** 3) ** (1/3)
    else:
        Nu = Nu_nat
    
    # Convection coefficient
    h_conv = Nu * AIR_THERMAL_CONDUCTIVITY / diameter
    
    return h_conv


def _calculate_radiation_coefficient(T_surface_K: float, T_ambient_K: float, emissivity: float) -> float:
    """Calculate radiation heat transfer coefficient."""
    # Linearized radiation coefficient
    h_rad = emissivity * STEFAN_BOLTZMANN_CONSTANT * (T_surface_K + T_ambient_K) * (T_surface_K ** 2 + T_ambient_K ** 2)
    
    return h_rad


def calculate_pipe_heat_loss_simplified(
    diameter: float,
    fluid_temp: float,
    ambient_temp: float,
    overall_heat_transfer_coefficient: float = 10.0
) -> float:
    """
    Simplified pipe heat loss calculation using overall heat transfer coefficient.
    
    Args:
        diameter: Pipe diameter (m)
        fluid_temp: Fluid temperature (°C)
        ambient_temp: Ambient temperature (°C)
        overall_heat_transfer_coefficient: Overall U-value (W/m²·K)
        
    Returns:
        float: Heat loss rate (W/m)
    """
    delta_T = fluid_temp - ambient_temp
    surface_area_per_length = math.pi * diameter  # m²/m
    
    heat_loss = overall_heat_transfer_coefficient * surface_area_per_length * delta_T
    
    return heat_loss
