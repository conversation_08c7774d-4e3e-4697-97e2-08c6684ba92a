### Schemas Layer Architecture Specification

**1. Purpose & Role**
The Schemas Layer serves as the central definition point for all data structures that flow through the application. Utilizing **Pydantic**, its primary purposes are to:
*   **Data Validation:** Rigorously validate incoming data (e.g., from [API requests](../../../api/api-architecture.md), [data imports](../../data_import/data_import-architecture.md)) against defined types, formats, and constraints. This ensures data integrity at the application's boundary.
*   **Data Serialization:** Convert complex Python objects (e notably [SQLAlchemy ORM models](../models/models-architecture.md), but also results from [calculations](../calculations/calculations-architecture.md) or [services](../services/services-architecture.md)) into JSON-serializable dictionaries for consistent [API responses](../../../api/api-architecture.md) or internal data exchange.
*   **Data Deserialization:** Convert incoming JSON (or other formats) into validated Python objects that the application's business logic can work with reliably.
*   **Define Data Contracts:** Establish clear and explicit contracts for data exchange between different layers of the application (e.g., [API](../../../api/api-architecture.md) ↔ [Service](../services/services-architecture.md), [Service](../services/services-architecture.md) ↔ [Calculations](../calculations/calculations-architecture.md), [Data Import](../data_import/data_import-architecture.md) ↔ [Service](../services/services-architecture.md)).
*   **API Documentation:** Automatically generate OpenAPI (Swagger UI / ReDoc) documentation, providing a live and accurate representation of the API's input and output data structures.
*   **Input Transformation:** Can perform minor data transformations (e.g., stripping whitespace, default value assignment) during the validation process.

**2. Location & Structure (`src/core/schemas/`)**
The Schemas Layer will be organized within `src/core/schemas/`, promoting modularity by logical domain or entity.

```
src/core/schemas/
├── __init__.py
├── base.py                   # Base schemas with common fields (e.g., ID, timestamps)
├── user.py                   # Schemas related to user authentication and profiles
├── project.py                # Schemas for project creation, details, updates
├── component.py              # Schemas for various heat tracing components (e.g., pipes, insulation)
├── calculation.py            # Schemas for inputs to and outputs from calculation functions
├── report.py                 # Schemas for report request parameters and metadata
├── data_import.py            # Schemas for validating incoming data during import (e.g., PipeImportSchema)
├── error.py                  # Schemas for standardized error responses and validation details
└── security.py               # Schemas for authentication tokens, user credentials
```

**3. Core Responsibilities & Functionality**

*   **Pydantic Model Definition:** Defines `BaseModel` classes with type hints and Pydantic field validators (`Field`, `validator`, `model_validator`) to specify data types, constraints (min/max length, value ranges), and complex validation logic.
*   **Request Schemas:**
    *   Define the exact structure and validation rules for data received from clients via [API requests](../../../api/api-architecture.md) (e.g., `ProjectCreate` for creating a new project, `ComponentUpdate` for modifying an existing component).
    *   Often include fields that are mandatory for creation but optional for updates.
*   **Response Schemas:**
    *   Define the structure of data sent back to clients as [API responses](../../../api/api-architecture.md) (e.g., `ProjectResponse` for a full project object, `CableSizingResult`).
    *   Typically include read-only fields like `id`, `created_at`, `updated_at`, which are populated by the backend.
    *   Leverage `Config.from_attributes = True` (formerly `Config.orm_mode = True`) to facilitate easy conversion from [SQLAlchemy ORM models](../models/models-architecture.md).
*   **Validation Schemas (Internal):**
    *   Used internally for validating data at various stages of the application lifecycle, even if not directly exposed via the API.
    *   Examples: `PipeImportSchema` for validating data from `.xlsx` imports, or internal `CalculationInputSchema` for rigorous checking before expensive computations.
*   **Base Schemas for DRY:**
    *   Define base Pydantic models (e.g., in `base.py`) that include common fields like `id`, `created_at`, `updated_at`, `is_active`, `name`, etc.
    *   Other schemas then inherit from these base schemas, reducing code duplication.
*   **Automatic Documentation:** By integrating with [FastAPI](../../../api/api-architecture.md), these schemas automatically generate detailed OpenAPI specifications, providing live and accurate API documentation.
*   **Error Detail Structuring:** The `ErrorDetail` and `ErrorResponseSchema` (from [`src/core/schemas/error.py`](#4-error-response-schemas-srccoreschemaserrorpy)) are crucial parts of this layer, defining how validation and [application errors](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorserror_exceptionspy) are communicated consistently.

    See the [Error and Exception Handling Architecture](../../errors/errors-architecture.md) for more details.

**4. Types of Schemas (Examples)**

*   **`base.py`:**
    ```python
    from pydantic import BaseModel, Field
    from datetime import datetime
    from typing import Optional

    class BaseSchema(BaseModel):
        id: Optional[int] = Field(None, description="Unique identifier for the resource.")
        created_at: Optional[datetime] = Field(None, description="Timestamp of creation.")
        updated_at: Optional[datetime] = Field(None, description="Timestamp of last update.")

        class Config:
            from_attributes = True # Enables ORM mode
    ```

*   **`project.py`:**
    ```python
    from .base import BaseSchema

    class ProjectCreate(BaseModel):
        name: str = Field(..., min_length=3, max_length=100)
        code: str = Field(..., min_length=3, max_length=20, pattern=r"^[A-Z0-9_-]+$")
        description: Optional[str] = None

    class ProjectUpdate(BaseModel):
        name: Optional[str] = Field(None, min_length=3, max_length=100)
        description: Optional[str] = None
        is_active: Optional[bool] = None

    class ProjectResponse(BaseSchema):
        name: str
        code: str
        description: Optional[str]
        is_active: bool
        # You might include counts of related components here too
    ```

**5. Interaction with Other Layers**

*   **API Layer ([`src/api/`](../../../api/api-architecture.md) - Primary Consumer):**
    *   **Request Body Validation:** FastAPI automatically validates incoming JSON request bodies against the specified Pydantic schemas. If validation fails, it raises a `ValidationError`, which is then caught by the global error handler ([`src/core/errors/handlers.py`](../../errors/errors-architecture.md#3-error-handling-middleware-srccoreerrorserror_handlingpy)) and transformed into a standardized `ErrorResponseSchema`.
    *   **Response Modeling:** [API endpoints](../../../api/api-architecture.md) specify response schemas (`response_model` parameter in FastAPI decorators). This ensures that the data returned to the client always conforms to the defined schema, performing automatic serialization.
    *   **Query/Path Parameter Validation:** Schemas can also be used for complex query or path parameter validation.
*   **Service Layer ([`src/core/services/`](../services/services-architecture.md)):**
    *   Receives validated Pydantic models from the [API Layer](../../../api/api-architecture.md), ensuring that business logic operates on clean, validated data.
    *   Can return Pydantic models to the [API Layer](../../../api/api-architecture.md) for automatic serialization.
    *   May use schemas internally for data consistency or to define the expected structure of data passed between service methods.

    See the [Service Layer Architecture](../services/services-architecture.md) for more details.

*   **Calculations Layer ([`src/core/calculations/`](../calculations/calculations-architecture.md)):**
    *   May define its own specific Pydantic schemas for inputs and outputs (e.g., `HeatLossInput`, `CableSizingResult`) to ensure the correct data types and ranges for algorithms, leveraging the Schema Layer for its internal data contracts.

    See the [Calculations Layer Architecture](../calculations/calculations-architecture.md) for more details.

*   **Data Import Layer ([`src/core/data_import/`](../data_import/data_import-architecture.md)):**
    *   Heavily relies on schemas (e.g., `PipeImportSchema`, `MaterialCatalogEntrySchema`) to validate incoming data from XLSX, JSON, or CSV files against the application's expected structure before mapping to [ORM models](../models/models-architecture.md).

    See the [Data Import Layer Architecture](../data_import/data_import-architecture.md) for more details.

*   **Report Generation Layer ([`src/core/reports/`](../reports/reports-architecture.md)):**
    *   Uses schemas to define the structure of the data that needs to be prepared and passed to report templates (e.g., `CircuitReportData`, `ComplianceSummaryData`).

    See the [Report Generation Architecture](../reports/reports-architecture.md) for more details.

*   **Error Handling Layer ([`src/core/errors/`](../../errors/errors-architecture.md)):**
    *   The `src/core/schemas/error.py` module within the Schemas Layer specifically defines the `ErrorResponseSchema` and `ValidationErrorDetail` that the [Error Handling Layer](../../errors/errors-architecture.md) uses to provide consistent error messages to clients.

    See the [Error and Exception Handling Architecture](../../errors/errors-architecture.md) for more details.

*   **Model Layer ([`src/core/models/`](../models/models-architecture.md)):** [Schemas](schemas-architecture.md) often closely mirror the structure of [SQLAlchemy ORM models](../models/models-architecture.md) (`src/core/models/`). However, they serve distinct purposes: schemas are for validation/serialization/deserialization, while models are for database interaction and persistence. The `Config.from_attributes = True` (or `orm_mode = True`) setting in Pydantic facilitates easy conversion between the two.

    See the [Model Layer Architecture](../models/models-architecture.md) for more details.

**6. Key Principles**

*   **Separation of Concerns:** Clearly distinguishes data representation/validation from business logic and database models.
*   **Single Source of Truth for Data Contracts:** The schemas are the definitive source for how data should look and behave throughout the application.
*   **DRY (Don't Repeat Yourself):** Achieved through Pydantic's inheritance and composition features.
*   **Automatic Documentation:** A significant benefit, as [API documentation](../../../api/api-architecture.md) is generated directly from code and is always up-to-date.
*   **Strictness & Type Safety:** Pydantic enforces type checking and validation, reducing runtime errors.
*   **Readability & Clarity:** Provides clear and concise definitions of complex data structures.
