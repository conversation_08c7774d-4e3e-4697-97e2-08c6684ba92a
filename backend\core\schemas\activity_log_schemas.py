# backend/core/schemas/activity_log_schemas.py
"""
Pydantic schemas for Activity Log entities validation, serialization, and deserialization.

This module defines the data contracts for Activity Log operations including:
- Activity Log schemas: For audit trail management and event tracking
- Event Type schemas: For standardized event categorization
- Filter schemas: For advanced audit trail querying and reporting
- Report schemas: For compliance reporting and audit analysis
- Security schemas: For security event monitoring and alerting
"""

import json
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import (
    BaseModel,
    ConfigDict,
    Field,
    field_validator,
    model_validator,
)

from .base import BaseSchema, PaginatedResponseSchema

# ============================================================================
# EVENT TYPE ENUMS
# ============================================================================


class EventTypeEnum(str, Enum):
    """Enumeration of event types for activity logging."""

    # CRUD Operations
    CREATE = "CREATE"
    READ = "READ"
    UPDATE = "UPDATE"
    DELETE = "DELETE"

    # Authentication Events
    LOGIN = "LOGIN"
    LOGOUT = "LOGOUT"
    LOGIN_FAILED = "LOGIN_FAILED"
    PASSWORD_CHANGE = "PASSWORD_CHANGE"
    PASSWORD_RESET = "PASSWORD_RESET"

    # System Events
    SYSTEM_START = "SYSTEM_START"
    SYSTEM_SHUTDOWN = "SYSTEM_SHUTDOWN"
    SYSTEM_ERROR = "SYSTEM_ERROR"

    # Security Events
    UNAUTHORIZED_ACCESS = "UNAUTHORIZED_ACCESS"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    SECURITY_VIOLATION = "SECURITY_VIOLATION"

    # Business Events
    CALCULATION_PERFORMED = "CALCULATION_PERFORMED"
    REPORT_GENERATED = "REPORT_GENERATED"
    DATA_IMPORTED = "DATA_IMPORTED"
    DATA_EXPORTED = "DATA_EXPORTED"

    # File Operations
    FILE_UPLOADED = "FILE_UPLOADED"
    FILE_DOWNLOADED = "FILE_DOWNLOADED"
    FILE_DELETED = "FILE_DELETED"


class EntityTypeEnum(str, Enum):
    """Enumeration of entity types for activity logging."""

    PROJECT = "Project"
    COMPONENT = "Component"
    HEAT_TRACING = "HeatTracing"
    ELECTRICAL = "Electrical"
    SWITCHBOARD = "Switchboard"
    USER = "User"
    USER_PREFERENCE = "UserPreference"
    DOCUMENT = "Document"
    IMPORTED_DATA_REVISION = "ImportedDataRevision"
    EXPORTED_DOCUMENT = "ExportedDocument"
    CALCULATION_STANDARD = "CalculationStandard"
    ACTIVITY_LOG = "ActivityLog"


class EventCategoryEnum(str, Enum):
    """Enumeration of event categories for grouping and filtering."""

    AUTHENTICATION = "AUTHENTICATION"
    AUTHORIZATION = "AUTHORIZATION"
    DATA_MANAGEMENT = "DATA_MANAGEMENT"
    CALCULATION = "CALCULATION"
    REPORTING = "REPORTING"
    SYSTEM = "SYSTEM"
    SECURITY = "SECURITY"
    FILE_MANAGEMENT = "FILE_MANAGEMENT"


# ============================================================================
# ACTIVITY LOG SCHEMAS
# ============================================================================


class ActivityLogBaseSchema(BaseModel):
    """Base schema for Activity Log with common fields."""

    event_type: EventTypeEnum = Field(..., description="Type of event that occurred")
    entity_type: Optional[EntityTypeEnum] = Field(
        None, description="Type of entity affected"
    )
    entity_id: Optional[int] = Field(None, description="ID of the entity affected")
    details: Optional[str] = Field(
        None, max_length=5000, description="Additional event details"
    )

    @field_validator("details")
    @classmethod
    def validate_details(cls, v: Optional[str]) -> Optional[str]:
        """Validate and sanitize event details."""
        if v is None:
            return v

        # Ensure details is valid JSON if it looks like JSON
        if v.strip().startswith(("{", "[")):
            try:
                json.loads(v)
            except json.JSONDecodeError:
                raise ValueError(
                    "Details field must contain valid JSON if JSON format is used"
                )

        return v.strip()

    @model_validator(mode="after")
    def validate_entity_consistency(self):
        """Validate entity type and ID consistency."""
        if self.entity_type is not None and self.entity_id is None:
            # Allow entity_type without entity_id for system-wide events
            pass
        elif self.entity_type is None and self.entity_id is not None:
            raise ValueError("entity_type must be provided when entity_id is specified")

        return self


class ActivityLogCreateSchema(ActivityLogBaseSchema):
    """Schema for creating a new activity log entry."""

    user_id: Optional[int] = Field(
        None, description="ID of the user who performed the action"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_id": 1,
                "event_type": "CREATE",
                "entity_type": "Project",
                "entity_id": 123,
                "details": '{"project_name": "New Heat Tracing Project", "project_number": "HT-2024-001"}',
            }
        }
    )


class ActivityLogUpdateSchema(BaseModel):
    """Schema for updating an existing activity log entry."""

    details: Optional[str] = Field(
        None, max_length=5000, description="Updated event details"
    )

    # Apply the same validator as create schema
    _validate_details = field_validator("details")(
        ActivityLogBaseSchema.validate_details
    )


class ActivityLogReadSchema(ActivityLogBaseSchema, BaseSchema):
    """Schema for reading/displaying activity log data."""

    id: int = Field(..., description="Activity log ID")
    timestamp: datetime = Field(..., description="Timestamp when the event occurred")
    user_id: Optional[int] = Field(
        None, description="ID of the user who performed the action"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "timestamp": "2024-01-15T10:30:00Z",
                "user_id": 1,
                "event_type": "CREATE",
                "entity_type": "Project",
                "entity_id": 123,
                "details": '{"project_name": "New Heat Tracing Project"}',
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
            }
        },
    )


class ActivityLogSummarySchema(BaseModel):
    """Lightweight schema for activity log listings."""

    id: int = Field(..., description="Activity log ID")
    timestamp: datetime = Field(..., description="Event timestamp")
    user_id: Optional[int] = Field(None, description="User ID")
    event_type: EventTypeEnum = Field(..., description="Event type")
    entity_type: Optional[EntityTypeEnum] = Field(None, description="Entity type")
    entity_id: Optional[int] = Field(None, description="Entity ID")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# FILTER AND QUERY SCHEMAS
# ============================================================================


class ActivityLogFilterSchema(BaseModel):
    """Schema for filtering activity logs with advanced query options."""

    user_id: Optional[int] = Field(None, description="Filter by user ID")
    event_type: Optional[EventTypeEnum] = Field(
        None, description="Filter by event type"
    )
    entity_type: Optional[EntityTypeEnum] = Field(
        None, description="Filter by entity type"
    )
    entity_id: Optional[int] = Field(None, description="Filter by entity ID")

    # Time-based filtering
    start_date: Optional[datetime] = Field(
        None, description="Filter events after this date"
    )
    end_date: Optional[datetime] = Field(
        None, description="Filter events before this date"
    )

    # Category filtering
    event_category: Optional[EventCategoryEnum] = Field(
        None, description="Filter by event category"
    )

    # Text search
    search_details: Optional[str] = Field(
        None, max_length=100, description="Search in event details"
    )

    @model_validator(mode="after")
    def validate_date_range(self):
        """Validate date range consistency."""
        if self.start_date and self.end_date and self.start_date >= self.end_date:
            raise ValueError("start_date must be before end_date")
        return self

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "user_id": 1,
                "event_type": "CREATE",
                "entity_type": "Project",
                "start_date": "2024-01-01T00:00:00Z",
                "end_date": "2024-01-31T23:59:59Z",
                "event_category": "DATA_MANAGEMENT",
            }
        }
    )


# ============================================================================
# AUDIT REPORT SCHEMAS
# ============================================================================


class AuditReportRequestSchema(BaseModel):
    """Schema for requesting audit reports."""

    report_type: str = Field(..., description="Type of audit report to generate")
    start_date: datetime = Field(..., description="Report start date")
    end_date: datetime = Field(..., description="Report end date")

    # Optional filters
    user_ids: Optional[List[int]] = Field(
        None, description="Filter by specific user IDs"
    )
    entity_types: Optional[List[EntityTypeEnum]] = Field(
        None, description="Filter by entity types"
    )
    event_types: Optional[List[EventTypeEnum]] = Field(
        None, description="Filter by event types"
    )
    event_categories: Optional[List[EventCategoryEnum]] = Field(
        None, description="Filter by event categories"
    )

    # Report options
    include_details: bool = Field(
        True, description="Include detailed event information"
    )
    group_by: Optional[str] = Field(
        None, description="Group results by field (user, entity_type, event_type)"
    )

    @field_validator("report_type")
    @classmethod
    def validate_report_type(cls, v: str) -> str:
        """Validate report type."""
        allowed_types = [
            "user_activity",
            "entity_changes",
            "security_events",
            "system_events",
            "compliance",
        ]
        if v not in allowed_types:
            raise ValueError(f"Report type must be one of: {', '.join(allowed_types)}")
        return v

    @model_validator(mode="after")
    def validate_date_range(self):
        """Validate date range."""
        if self.start_date >= self.end_date:
            raise ValueError("start_date must be before end_date")

        # Limit report range to prevent performance issues
        from datetime import timedelta

        max_range = timedelta(days=365)  # 1 year maximum
        if self.end_date - self.start_date > max_range:
            raise ValueError("Report date range cannot exceed 365 days")

        return self

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "report_type": "user_activity",
                "start_date": "2024-01-01T00:00:00Z",
                "end_date": "2024-01-31T23:59:59Z",
                "user_ids": [1, 2, 3],
                "event_types": ["CREATE", "UPDATE", "DELETE"],
                "include_details": True,
                "group_by": "user",
            }
        }
    )


class AuditReportSummarySchema(BaseModel):
    """Schema for audit report summary information."""

    total_events: int = Field(..., description="Total number of events in the report")
    unique_users: int = Field(..., description="Number of unique users in the report")
    unique_entities: int = Field(..., description="Number of unique entities affected")
    event_type_counts: Dict[str, int] = Field(
        ..., description="Count of events by type"
    )
    entity_type_counts: Dict[str, int] = Field(
        ..., description="Count of events by entity type"
    )
    daily_activity_counts: Dict[str, int] = Field(
        ..., description="Daily activity counts"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "total_events": 1250,
                "unique_users": 15,
                "unique_entities": 89,
                "event_type_counts": {"CREATE": 450, "UPDATE": 600, "DELETE": 200},
                "entity_type_counts": {"Project": 300, "Component": 500, "User": 450},
                "daily_activity_counts": {"2024-01-15": 45, "2024-01-16": 52},
            }
        }
    )


class AuditReportResponseSchema(BaseModel):
    """Schema for audit report response."""

    report_id: str = Field(..., description="Unique report identifier")
    report_type: str = Field(..., description="Type of audit report")
    generated_at: datetime = Field(..., description="Report generation timestamp")
    generated_by_user_id: Optional[int] = Field(
        None, description="User who generated the report"
    )

    # Report metadata
    start_date: datetime = Field(..., description="Report start date")
    end_date: datetime = Field(..., description="Report end date")
    summary: AuditReportSummarySchema = Field(..., description="Report summary")

    # Report data
    events: List[ActivityLogSummarySchema] = Field(
        ..., description="Activity log events"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# SECURITY EVENT SCHEMAS
# ============================================================================


class SecurityEventSchema(BaseModel):
    """Schema for security-specific event information."""

    event_id: int = Field(..., description="Activity log event ID")
    severity: str = Field(..., description="Security event severity")
    threat_level: str = Field(..., description="Assessed threat level")
    source_ip: Optional[str] = Field(None, description="Source IP address if available")
    user_agent: Optional[str] = Field(None, description="User agent if available")
    additional_context: Optional[Dict[str, Any]] = Field(
        None, description="Additional security context"
    )

    @field_validator("severity")
    @classmethod
    def validate_severity(cls, v: str) -> str:
        """Validate security event severity."""
        allowed_severities = ["LOW", "MEDIUM", "HIGH", "CRITICAL"]
        if v not in allowed_severities:
            raise ValueError(
                f"Severity must be one of: {', '.join(allowed_severities)}"
            )
        return v

    @field_validator("threat_level")
    @classmethod
    def validate_threat_level(cls, v: str) -> str:
        """Validate threat level."""
        allowed_levels = ["NONE", "LOW", "MEDIUM", "HIGH", "CRITICAL"]
        if v not in allowed_levels:
            raise ValueError(
                f"Threat level must be one of: {', '.join(allowed_levels)}"
            )
        return v

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "event_id": 1,
                "severity": "HIGH",
                "threat_level": "MEDIUM",
                "source_ip": "*************",
                "user_agent": "Mozilla/5.0...",
                "additional_context": {"failed_attempts": 3, "account_locked": False},
            }
        }
    )


# ============================================================================
# PAGINATED RESPONSE SCHEMAS
# ============================================================================


class ActivityLogPaginatedResponseSchema(PaginatedResponseSchema):
    """Schema for paginated activity log list responses."""

    activity_logs: List[ActivityLogSummarySchema] = Field(
        ..., description="List of activity logs"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "activity_logs": [
                    {
                        "id": 1,
                        "timestamp": "2024-01-15T10:30:00Z",
                        "user_id": 1,
                        "event_type": "CREATE",
                        "entity_type": "Project",
                        "entity_id": 123,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


# ============================================================================
# UTILITY SCHEMAS
# ============================================================================


class EventCategoryMappingSchema(BaseModel):
    """Schema for mapping event types to categories."""

    @classmethod
    def get_event_category(cls, event_type: EventTypeEnum) -> EventCategoryEnum:
        """Map event type to category."""
        mapping = {
            # Authentication events
            EventTypeEnum.LOGIN: EventCategoryEnum.AUTHENTICATION,
            EventTypeEnum.LOGOUT: EventCategoryEnum.AUTHENTICATION,
            EventTypeEnum.LOGIN_FAILED: EventCategoryEnum.AUTHENTICATION,
            EventTypeEnum.PASSWORD_CHANGE: EventCategoryEnum.AUTHENTICATION,
            EventTypeEnum.PASSWORD_RESET: EventCategoryEnum.AUTHENTICATION,
            # Authorization events
            EventTypeEnum.UNAUTHORIZED_ACCESS: EventCategoryEnum.AUTHORIZATION,
            EventTypeEnum.PERMISSION_DENIED: EventCategoryEnum.AUTHORIZATION,
            # Data management events
            EventTypeEnum.CREATE: EventCategoryEnum.DATA_MANAGEMENT,
            EventTypeEnum.READ: EventCategoryEnum.DATA_MANAGEMENT,
            EventTypeEnum.UPDATE: EventCategoryEnum.DATA_MANAGEMENT,
            EventTypeEnum.DELETE: EventCategoryEnum.DATA_MANAGEMENT,
            EventTypeEnum.DATA_IMPORTED: EventCategoryEnum.DATA_MANAGEMENT,
            EventTypeEnum.DATA_EXPORTED: EventCategoryEnum.DATA_MANAGEMENT,
            # Calculation events
            EventTypeEnum.CALCULATION_PERFORMED: EventCategoryEnum.CALCULATION,
            # Reporting events
            EventTypeEnum.REPORT_GENERATED: EventCategoryEnum.REPORTING,
            # System events
            EventTypeEnum.SYSTEM_START: EventCategoryEnum.SYSTEM,
            EventTypeEnum.SYSTEM_SHUTDOWN: EventCategoryEnum.SYSTEM,
            EventTypeEnum.SYSTEM_ERROR: EventCategoryEnum.SYSTEM,
            # Security events
            EventTypeEnum.SECURITY_VIOLATION: EventCategoryEnum.SECURITY,
            # File management events
            EventTypeEnum.FILE_UPLOADED: EventCategoryEnum.FILE_MANAGEMENT,
            EventTypeEnum.FILE_DOWNLOADED: EventCategoryEnum.FILE_MANAGEMENT,
            EventTypeEnum.FILE_DELETED: EventCategoryEnum.FILE_MANAGEMENT,
        }

        return mapping.get(event_type, EventCategoryEnum.SYSTEM)
