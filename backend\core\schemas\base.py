# backend/core/schemas/base.py
"""
Base Pydantic schemas with common fields and patterns.

This module provides base schemas that can be inherited by entity-specific schemas
to ensure consistency across the application and reduce code duplication.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict


class BaseSchema(BaseModel):
    """Base schema with common fields for all entities."""
    
    id: Optional[int] = Field(None, description="Unique identifier for the resource")
    created_at: Optional[datetime] = Field(None, description="Timestamp of creation")
    updated_at: Optional[datetime] = Field(None, description="Timestamp of last update")

    model_config = ConfigDict(from_attributes=True)  # Enable ORM mode


class BaseReadSchema(BaseSchema):
    """Base schema for read operations with required common fields."""
    
    id: int = Field(..., description="Unique identifier for the resource")
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class BaseSoftDeleteSchema(BaseReadSchema):
    """Base schema for entities with soft delete functionality."""
    
    is_deleted: bool = Field(..., description="Soft delete flag")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by_user_id: Optional[int] = Field(None, description="ID of user who deleted the resource")


class BaseNamedSchema(BaseModel):
    """Base schema for entities with name and notes fields."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Resource name")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")


class PaginationSchema(BaseModel):
    """Schema for pagination parameters."""
    
    page: int = Field(1, ge=1, description="Page number (1-based)")
    per_page: int = Field(10, ge=1, le=100, description="Number of items per page")


class PaginatedResponseSchema(BaseModel):
    """Base schema for paginated responses."""
    
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")

    @classmethod
    def create_response(cls, items, total: int, page: int, per_page: int):
        """Helper method to create paginated response."""
        import math
        total_pages = math.ceil(total / per_page) if total > 0 else 1
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": total_pages
        }


class TimestampMixin(BaseModel):
    """Mixin for timestamp fields."""
    
    created_at: datetime = Field(..., description="Timestamp of creation")
    updated_at: datetime = Field(..., description="Timestamp of last update")


class SoftDeleteMixin(BaseModel):
    """Mixin for soft delete fields."""
    
    is_deleted: bool = Field(False, description="Soft delete flag")
    deleted_at: Optional[datetime] = Field(None, description="Deletion timestamp")
    deleted_by_user_id: Optional[int] = Field(None, description="ID of user who deleted the resource")


class NamedEntityMixin(BaseModel):
    """Mixin for named entities."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Entity name")
    notes: Optional[str] = Field(None, max_length=1000, description="Additional notes")
