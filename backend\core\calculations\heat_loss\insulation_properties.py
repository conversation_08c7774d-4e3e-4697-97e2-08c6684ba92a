# backend/core/calculations/heat_loss/insulation_properties.py
"""
Insulation Properties Database.

This module provides thermal properties for various insulation materials
used in heat tracing applications.
"""

import logging
from typing import Dict, Any, Optional

from backend.core.errors.exceptions import NotFoundError

logger = logging.getLogger(__name__)

# Insulation materials database
# Thermal conductivity values in W/(m·K) at typical operating temperatures
INSULATION_PROPERTIES = {
    "mineral_wool": {
        "thermal_conductivity": 0.040,  # W/(m·K)
        "max_temperature": 700,  # °C
        "density": 100,  # kg/m³
        "description": "Mineral wool insulation",
        "applications": ["general", "high_temperature"],
        "cost_factor": 1.0
    },
    "glass_wool": {
        "thermal_conductivity": 0.035,
        "max_temperature": 450,
        "density": 50,
        "description": "Glass wool insulation",
        "applications": ["general", "low_temperature"],
        "cost_factor": 0.8
    },
    "polyurethane_foam": {
        "thermal_conductivity": 0.025,
        "max_temperature": 110,
        "density": 40,
        "description": "Polyurethane foam insulation",
        "applications": ["low_temperature", "cryogenic"],
        "cost_factor": 1.2
    },
    "perlite": {
        "thermal_conductivity": 0.050,
        "max_temperature": 900,
        "density": 200,
        "description": "Expanded perlite insulation",
        "applications": ["high_temperature", "cryogenic"],
        "cost_factor": 1.5
    },
    "calcium_silicate": {
        "thermal_conductivity": 0.055,
        "max_temperature": 650,
        "density": 250,
        "description": "Calcium silicate insulation",
        "applications": ["high_temperature", "industrial"],
        "cost_factor": 1.8
    },
    "aerogel": {
        "thermal_conductivity": 0.015,
        "max_temperature": 200,
        "density": 150,
        "description": "Aerogel insulation (high performance)",
        "applications": ["space_constrained", "high_performance"],
        "cost_factor": 5.0
    },
    "phenolic_foam": {
        "thermal_conductivity": 0.020,
        "max_temperature": 120,
        "density": 45,
        "description": "Phenolic foam insulation",
        "applications": ["fire_resistant", "low_temperature"],
        "cost_factor": 1.4
    },
    "cellular_glass": {
        "thermal_conductivity": 0.045,
        "max_temperature": 450,
        "density": 120,
        "description": "Cellular glass insulation",
        "applications": ["moisture_resistant", "chemical_resistant"],
        "cost_factor": 2.0
    }
}

# Temperature-dependent thermal conductivity corrections
# Format: {material: {temp_range: correction_factor}}
TEMPERATURE_CORRECTIONS = {
    "mineral_wool": {
        (0, 100): 1.0,
        (100, 300): 1.1,
        (300, 500): 1.2,
        (500, 700): 1.3
    },
    "glass_wool": {
        (0, 100): 1.0,
        (100, 200): 1.05,
        (200, 350): 1.1,
        (350, 450): 1.15
    },
    "polyurethane_foam": {
        (-50, 0): 0.9,
        (0, 50): 1.0,
        (50, 110): 1.1
    }
}


def get_insulation_properties(insulation_type: str) -> Optional[Dict[str, Any]]:
    """
    Get thermal properties for a specific insulation material.
    
    Args:
        insulation_type: Type of insulation material
        
    Returns:
        Dict containing insulation properties or None if not found
        
    Raises:
        NotFoundError: If insulation type is not found
    """
    logger.debug(f"Looking up properties for insulation type: {insulation_type}")
    
    # Normalize input
    insulation_type = insulation_type.lower().strip()
    
    if insulation_type not in INSULATION_PROPERTIES:
        available_types = list(INSULATION_PROPERTIES.keys())
        logger.warning(f"Insulation type '{insulation_type}' not found. Available: {available_types}")
        raise NotFoundError(f"Insulation type '{insulation_type}' not found in database")
    
    properties = INSULATION_PROPERTIES[insulation_type].copy()
    logger.debug(f"Found properties for {insulation_type}: k={properties['thermal_conductivity']} W/(m·K)")
    
    return properties


def get_temperature_corrected_conductivity(
    insulation_type: str, 
    temperature: float
) -> float:
    """
    Get temperature-corrected thermal conductivity.
    
    Args:
        insulation_type: Type of insulation material
        temperature: Operating temperature (°C)
        
    Returns:
        float: Temperature-corrected thermal conductivity (W/m·K)
        
    Raises:
        NotFoundError: If insulation type is not found
    """
    properties = get_insulation_properties(insulation_type)
    base_conductivity = properties['thermal_conductivity']
    
    # Check if temperature corrections are available
    if insulation_type not in TEMPERATURE_CORRECTIONS:
        logger.debug(f"No temperature corrections available for {insulation_type}")
        return base_conductivity
    
    # Find appropriate temperature range
    corrections = TEMPERATURE_CORRECTIONS[insulation_type]
    correction_factor = 1.0
    
    for (temp_min, temp_max), factor in corrections.items():
        if temp_min <= temperature < temp_max:
            correction_factor = factor
            break
    
    corrected_conductivity = base_conductivity * correction_factor
    
    logger.debug(f"Temperature correction for {insulation_type} at {temperature}°C: "
                f"{base_conductivity} -> {corrected_conductivity} W/(m·K)")
    
    return corrected_conductivity


def validate_insulation_temperature(insulation_type: str, temperature: float) -> bool:
    """
    Validate if insulation can operate at specified temperature.
    
    Args:
        insulation_type: Type of insulation material
        temperature: Operating temperature (°C)
        
    Returns:
        bool: True if temperature is within limits
        
    Raises:
        NotFoundError: If insulation type is not found
    """
    properties = get_insulation_properties(insulation_type)
    max_temp = properties['max_temperature']
    
    is_valid = temperature <= max_temp
    
    if not is_valid:
        logger.warning(f"Temperature {temperature}°C exceeds maximum for {insulation_type} ({max_temp}°C)")
    
    return is_valid


def get_recommended_insulation(
    max_temperature: float,
    application: str = "general",
    cost_priority: bool = False
) -> list[Dict[str, Any]]:
    """
    Get recommended insulation materials for given criteria.
    
    Args:
        max_temperature: Maximum operating temperature (°C)
        application: Application type (general, high_temperature, etc.)
        cost_priority: Whether to prioritize cost over performance
        
    Returns:
        List of recommended insulation materials with properties
    """
    logger.debug(f"Finding insulation for T_max={max_temperature}°C, application={application}")
    
    suitable_materials = []
    
    for material_name, properties in INSULATION_PROPERTIES.items():
        # Check temperature compatibility
        if properties['max_temperature'] < max_temperature:
            continue
        
        # Check application compatibility
        if application != "general" and application not in properties['applications']:
            continue
        
        # Add to suitable list
        material_info = properties.copy()
        material_info['name'] = material_name
        suitable_materials.append(material_info)
    
    # Sort by criteria
    if cost_priority:
        # Sort by cost factor (lower is better)
        suitable_materials.sort(key=lambda x: x['cost_factor'])
    else:
        # Sort by thermal performance (lower conductivity is better)
        suitable_materials.sort(key=lambda x: x['thermal_conductivity'])
    
    logger.debug(f"Found {len(suitable_materials)} suitable insulation materials")
    
    return suitable_materials


def calculate_insulation_thickness(
    pipe_diameter: float,
    heat_loss_target: float,
    fluid_temp: float,
    ambient_temp: float,
    insulation_type: str,
    surface_heat_transfer_coeff: float = 10.0
) -> float:
    """
    Calculate required insulation thickness to achieve target heat loss.
    
    Args:
        pipe_diameter: Pipe outer diameter (m)
        heat_loss_target: Target heat loss rate (W/m)
        fluid_temp: Fluid temperature (°C)
        ambient_temp: Ambient temperature (°C)
        insulation_type: Type of insulation material
        surface_heat_transfer_coeff: Surface heat transfer coefficient (W/m²·K)
        
    Returns:
        float: Required insulation thickness (m)
        
    Raises:
        NotFoundError: If insulation type is not found
    """
    import math
    
    properties = get_insulation_properties(insulation_type)
    k_insulation = get_temperature_corrected_conductivity(insulation_type, (fluid_temp + ambient_temp) / 2)
    
    delta_T = fluid_temp - ambient_temp
    r_pipe = pipe_diameter / 2
    
    # Solve for insulation thickness using heat transfer equation
    # Q = 2π * ΔT / (ln(r_out/r_in)/k + 1/(h*r_out))
    
    # This requires iterative solution or approximation
    # For simplicity, using approximation for thin insulation
    
    # Target thermal resistance
    R_target = delta_T / heat_loss_target * 2 * math.pi
    
    # Surface resistance
    R_surface_approx = 1 / (surface_heat_transfer_coeff * pipe_diameter * math.pi)
    
    # Required insulation resistance
    R_insulation_required = R_target - R_surface_approx
    
    if R_insulation_required <= 0:
        logger.warning("Target heat loss too high - no insulation needed")
        return 0.0
    
    # Approximate insulation thickness (valid for thin insulation)
    thickness_approx = R_insulation_required * k_insulation * 2 * math.pi / math.log(1 + 0.1)  # rough approximation
    
    logger.debug(f"Calculated insulation thickness: {thickness_approx:.3f} m")
    
    return max(0.0, thickness_approx)


def get_all_insulation_types() -> list[str]:
    """Get list of all available insulation types."""
    return list(INSULATION_PROPERTIES.keys())


def get_insulation_summary() -> Dict[str, Dict[str, Any]]:
    """Get summary of all insulation materials."""
    summary = {}
    
    for material, props in INSULATION_PROPERTIES.items():
        summary[material] = {
            "thermal_conductivity": props["thermal_conductivity"],
            "max_temperature": props["max_temperature"],
            "description": props["description"],
            "cost_factor": props["cost_factor"]
        }
    
    return summary
