# Backend Architecture

This document outlines the core architectural principles, specifically the layered architecture, that guide the development of the backend for the Heat Tracing Design Application. It also provides a summary of the implemented architectural layers and key components.

## Architectural Principles

Absolutely, for an application of this complexity and with its stated goals of "modularity, clean coding practices, robust data management," implementing a layered architecture is highly recommended. It promotes separation of concerns, testability, maintainability, and scalability.

Here's a breakdown of the layers and how they would benefit the Heat Tracing Design Application:

### Core Layers from Principles Document:

*   [Repository Layer](core/repositories/repositories-architecture.md)
    *   **Purpose:** To abstract the data persistence logic from the rest of the application. It provides a clean, database-agnostic interface for performing CRUD (Create, Read, Update, Delete) operations on your domain models. The [repository layer](core/repositories/repositories-architecture.md) knows how to interact with the underlying database (SQLite via SQLAlchemy in your case) but encapsulates those details.
    *   **How it supports the models:** Instead of directly calling SQLAlchemy methods (`session.query()`, `session.add()`, etc.) throughout your application, you'd define methods like `project_repository.get_by_id(project_id)`, `circuit_repository.save(circuit)`, or `cable_repository.find_all()`.

    For a detailed specification, see the [Repository Layer Architecture](core/repositories/repositories-architecture.md).

    *   **Benefits for Heat Tracing App:**
        *   **Decoupling:** Your business logic doesn't need to know if you're using SQLite, PostgreSQL, or a different ORM. If you ever switch databases or ORMs, changes are mostly confined to this layer.
        *   **Testability:** You can easily mock the [repository layer](core/repositories/repositories-architecture.md) during unit testing of your [service layer](core/services/services-architecture.md), allowing you to test business logic without needing a live database connection.
        *   **Centralized Data Access:** All data access patterns are defined in one place, making them consistent and easier to maintain.
    *   **Recommendation:** **Highly Recommended.** This is a foundational layer for clean architecture.

*   [Service Layer](core/services/services-architecture.md) (Business Logic Layer)
    *   **Purpose:** To encapsulate the application's core business logic and orchestrate interactions between [repositories](core/repositories/repositories-architecture.md) and other components. This is where the "what" of your application happens, in terms of business rules and use cases.
    *   **How it supports the models:** The [service layer](core/services/services-architecture.md) receives requests (e.g., "create a new project," "calculate heat loss for a pipe," "generate BOM report"). It then uses the [repository layer](core/repositories/repositories-architecture.md) to fetch/save data, performs [calculations](core/calculations/calculations-architecture.md) (heat loss, cable selection), applies [business rules](core/standards/standards-architecture.md) (e.g., ATEX compliance checks, circuit design constraints), and manages [transactions](core/database/transaction-architecture.md).

    For a detailed specification, see the [Service Layer Architecture](core/services/services-architecture.md).

    *   **Benefits for Heat Tracing App:**
        *   **Centralized Business Rules:** All complex [calculations](core/calculations/calculations-architecture.md) (heat loss, cable sizing), validation (circuit assignment rules), and data manipulation logic are consolidated here.
        *   **Reusability:** Business logic can be reused across different parts of the application (e.g., UI, import functions).
        *   **Transaction Management:** [Services](core/services/services-architecture.md) often handle [database transactions](core/database/transaction-architecture.md), ensuring atomicity of operations.
        *   **Clear Use Cases:** Each [service](core/services/services-architecture.md) method typically represents a specific use case or business operation.
    *   **Recommendation:** **Highly Recommended.** This is where the intelligence of your heat tracing design application resides.

*   [Schema Layer](core/schemas/schemas-architecture.md) (Pydantic Models)
    *   **Purpose:** To define data structures for input validation, serialization (converting Python objects to data formats like JSON, dicts), and deserialization (converting data formats to Python objects). [Pydantic](core/schemas/schemas-architecture.md) is excellent for ensuring data integrity at the application's boundaries.
    *   **How it supports the models:**
        *   **Input Validation:** When a user enters data in a UI form (e.g., pipe dimensions, material properties), you can use [Pydantic schemas](core/schemas/schemas-architecture.md) to validate that input before passing it to the [service layer](core/services/services-architecture.md).
        *   **Data Import/Export:** When importing data from Excel/CSV or exporting to PDF/Excel reports, [Pydantic](core/schemas/schemas-architecture.md) can define the expected structure and validate the data.

    See the [Data Import Layer Architecture](core/data_import/data_import-architecture.md) and [Report Generation Architecture](core/reports/reports-architecture.md) for related documentation.

    *   **Data Transfer Objects (DTOs):** You can use [Pydantic models](core/schemas/schemas-architecture.md) as DTOs to transfer data between layers, ensuring consistent data contracts. For example, a `ProjectCreateSchema` for input and a `ProjectReadSchema` for output.

    For a detailed specification, see the [Schema Layer Architecture](core/schemas/schemas-architecture.md).

    *   **Benefits for Heat Tracing App:**
        *   **Robust Input Handling:** Prevents invalid data from reaching your business logic or database.
        *   **Clear Data Contracts:** Provides a clear specification of what data is expected for inputs and outputs.
        *   **Automatic Type Coercion:** [Pydantic](core/schemas/schemas-architecture.md) can automatically convert data types (e.g., string to float).
        *   **Serialization/Deserialization:** Simplifies converting [SQLAlchemy ORM objects](core/models/models-architecture.md) into dictionary/JSON-like structures for the UI or reports.
    *   **Recommendation:** **Highly Recommended.** Especially given the need for robust data import/export and complex data input from the user.

### Other Important Layers/Concepts from Principles Document:

*   Presentation Layer (UI Layer):
    *   **Purpose:** This is your user interface (e.g., built with PyQt, Tkinter, Kivy). It's responsible for displaying information to the user and capturing user input.
    *   **Interaction:** It interacts with the [Service Layer](core/services/services-architecture.md) to trigger business operations and retrieve data for display.
    *   **Recommendation:** Essential for a desktop application.
*   Domain Models (ORM Models):
    *   **Purpose:** These are your [SQLAlchemy ORM models](core/models/models-architecture.md) (e.g., `Project`, `Pipe`, `Cable`, `Circuit`). They represent the core entities and their relationships within your application's domain.
    *   **Role:** They are the "data structures" that flow between the [Repository](core/repositories/repositories-architecture.md) and [Service layers](core/services/services-architecture.md), and their data can be validated/transformed by the [Schema layer](core/schemas/schemas-architecture.md).
    *   **Recommendation:** Already in place as your `models` package.

    For a detailed specification, see the [Model Layer Architecture](core/models/models-architecture.md).

*   [Configuration Layer](config/config-architecture.md):
    *   **Purpose:** Centralized management of application settings (e.g., database connection strings, report template paths, default units).
    *   **Recommendation:** Recommended to separate settings from code.

    For a detailed specification, see the [Configuration Layer Architecture](config/config-architecture.md).

*   Logging Layer:
    *   **Purpose:** Provides a consistent mechanism for recording application events, errors, and debugging information.
    *   **Recommendation:** Essential for debugging and monitoring, especially for an offline desktop app where direct access to logs might be needed by the user.

    See the [How-To: Configure Logging](how-to/how-to_logging.md) for more details.

## Implemented Backend Layers Summary:

Here's a summary of the implemented backend layers and key components, outlining their primary responsibilities and interactions:

1.  Overall Application Structure (`src/`)
    *   **Purpose:** Defines the top-level directory structure for organizing the entire backend codebase, promoting modularity and separation of concerns.
    *   **Location:** Project root `src/` directory.
    *   **Interactions:** Serves as the container for all other layers.

2.  [Configuration Layer](config/config-architecture.md) (`src/config/`)
    *   **Purpose:** Manages application settings, environment variables, and external service configurations (e.g., database connection strings, API keys).
    *   **Location:** `src/config/settings.py`.
    *   **Interactions:** Directly imported by almost all other layers to access necessary configuration.

3.  [Database Layer](core/database/architecture.md) (`src/core/database/`)
    *   **Purpose:** Handles all aspects of database connection management, session handling, and ORM configuration.
    *   **Location:** `src/core/database/`.
    *   **Interactions:** Provides `Session` objects to the [Repository Layer](core/repositories/repositories-architecture.md) via [Dependency Injection](../dependency-management-architecture.md). Configured by the [Configuration Layer](config/config-architecture.md).

4.  [Model Layer](core/models/models-architecture.md) (`src/core/models/`)
    *   **Purpose:** Defines the SQLAlchemy [ORM models](core/models/models-architecture.md), representing the structure of data stored in the database. Also includes [**Model-Level Validation**](core/models/validation-architecture.md) for data integrity before persistence (e.g., using SQLAlchemy event listeners).
    *   **Location:** `src/core/models/`.

    See the [Model-Level Validation Architecture](core/models/validation-architecture.md) for more details.

*   **Interactions:** Used by the [Repository Layer](core/repositories/repositories-architecture.md) for database interactions; validated by custom methods within the models themselves.

5.  [Schema Layer](core/schemas/schemas-architecture.md) (`src/core/schemas/`)

*   **Purpose:** Defines [Pydantic schemas](core/schemas/schemas-architecture.md) for data validation, serialization, and deserialization. Used for [API request/response bodies](api/api-architecture.md), internal data contracts, and [data import](core/data_import/data_import-architecture.md)/[report structuring](core/reports/reports-architecture.md).
    *   **Location:** `src/core/schemas/`.
    *   **Interactions:** Heavily used by the [API Layer](api/api-architecture.md) (for DTOs), [Service Layer](core/services/services-architecture.md) (for data consistency), and [Data Import](core/data_import/data_import-architecture.md)/[Report Generation Layers](core/reports/reports-architecture.md) (for data contracts).

6.  [Repository Layer](core/repositories/repositories-architecture.md) (`src/core/repositories/`)

*   **Purpose:** Abstracts direct database interactions (CRUD operations) for specific [ORM models](core/models/models-architecture.md), providing a clean interface for the [Service Layer](core/services/services-architecture.md). Translates database exceptions into generic [application exceptions](core/errors/errors-architecture.md).
    *   **Location:** `src/core/repositories/`.
    *   **Interactions:** Consumes `Session` from the [Database Layer](core/database/architecture.md) (via [DI](../dependency-management-architecture.md)). Consumed by the [Service Layer](core/services/services-architecture.md). Operates on [Model Layer objects](core/models/models-architecture.md).

7.  [Service Layer](core/services/services-architecture.md) (`src/core/services/`)

*   **Purpose:** Contains the core business logic and orchestrates operations involving multiple [repositories](core/repositories/repositories-architecture.md) or complex workflows. Acts as the primary interface between the [API](api/api-architecture.md) and the data/[calculation layers](core/calculations/calculations-architecture.md).
    *   **Location:** `src/core/services/`.
    *   **Interactions:** Consumed by the [API Layer](api/api-architecture.md). Consumes [Repository](core/repositories/repositories-architecture.md), [Calculations](core/calculations/calculations-architecture.md), [Standards](core/standards/standards-architecture.md), and [Report Generation Layers](core/reports/reports-architecture.md). Raises [custom application-specific exceptions](core/errors/errors-architecture.md).

8.  [API Layer](api/api-architecture.md) (`src/api/`)

*   **Purpose:** Exposes HTTP endpoints for client-side interaction. Handles routing, input [data validation](core/schemas/schemas-architecture.md) (using [Pydantic schemas](core/schemas/schemas-architecture.md)), authentication, and authorization.
    *   **Location:** `src/api/`.
    *   **Interactions:** Consumes the [Service Layer](core/services/services-architecture.md). Uses [Schemas](core/schemas/schemas-architecture.md) for request/response. Integrates with [Dependency Management](dependency-management-architecture.md) and [Error Handling](core/errors/errors-architecture.md).

9.  [Security Layer](security/security-architecture.md) (`src/core/security/` and `src/middleware/security.py`)

*   **Purpose:** Handles authentication (e.g., JWT) and authorization (e.g., role-based access control).
    *   **Location:** `src/core/security/` for core logic, `src/middleware/security.py` for [FastAPI integration](api/api-architecture.md).
    *   **Interactions:** Integrated into the [API Layer](api/api-architecture.md) via [FastAPI's `Depends`](dependency-management-architecture.md) for user authentication/authorization.

10. Logging and Monitoring ([Telemetry](telemetry/telemetry-architecture.md))

*   **Purpose:** Provides mechanisms for logging application events, errors, and performance metrics, and enables distributed tracing.
    *   **Location:** Integrated throughout the application via [logging configurations](how-to/how-to_logging.md) and instrumentation libraries.
    *   **Interactions:** A cross-cutting concern, used by all layers for observability.

11. [Dependency Management](dependency-management-architecture.md)

*   **Purpose:** Leverages [FastAPI's `Depends` system](dependency-management-architecture.md) to inject dependencies (e.g., database sessions, service instances, authenticated users) into [API routes](api/api-architecture.md) and other components. Promotes modularity, testability, and resource management.
    *   **Location:** Implemented via provider functions (e.g., `get_db()`, `get_service()`) often located in dependency-specific modules (e.g., `src/core/database/dependencies.py`, `src/core/services/dependencies.py`).
    *   **Interactions:** Utilized by almost all layers, primarily the [API Layer](api/api-architecture.md) to receive dependencies.

12. [Utility Functions](utils/utils-architecture.md) (`src/core/utils/`)

*   **Purpose:** Provides generic helper functions and classes for common, reusable tasks that are not specific to any particular business domain (e.g., UUID management, date/time operations, string manipulation, pagination helpers).
    *   **Location:** `src/core/utils/`.
    *   **Interactions:** Consumed by various layers ([API](api/api-architecture.md), [Service](core/services/services-architecture.md), [Repository](core/repositories/repositories-architecture.md), [Calculations](core/calculations/calculations-architecture.md)) as needed for common operations.

13. [Calculations Layer](core/calculations/calculations-architecture.md) (`src/core/calculations/`)

*   **Purpose:** Encapsulates complex engineering formulas, algorithms, and domain-specific logic for heat tracing design (e.g., heat loss, cable sizing).
    *   **Location:** `src/core/calculations/`.
    *   **Interactions:** Consumed by the [Service Layer](core/services/services-architecture.md). Performs its own specific validation and raises `CalculationError` or `InvalidInputError`.

14. [Error and Exception Handling](core/errors/errors-architecture.md) (`src/core/errors/`)

*   **Purpose:** Provides a standardized, centralized mechanism for managing errors throughout the application, from low-level issues to [API responses](api/api-architecture.md).
    *   **Location:** [`src/core/errors/exceptions.py`](core/errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy) (custom exceptions), [`src/core/errors/handlers.py`](core/errors/errors-architecture.md#3-error-handling-middleware-srccoreerrorserror_handlingpy) (FastAPI handlers), [`src/core/schemas/error.py`](core/schemas/schemas-architecture.md#4-error-response-schemas-srccoreschemaserrorpy) (response schema).
    *   **Interactions:** A critical cross-cutting concern. Lower layers raise specific exceptions, higher layers catch and translate them. [API Layer](api/api-architecture.md) uses global handlers for consistent responses.

15. [Standards Layer](core/standards/standards-architecture.md) (`src/core/standards/`)

*   **Purpose:** Implements logic for applying rules and validating designs against engineering standards (e.g., TR 50410, hazardous area certifications).
    *   **Location:** `src/core/standards/`.
    *   **Interactions:** Consumed by the [Service Layer](core/services/services-architecture.md) (after [calculations](core/calculations/calculations-architecture.md)). Raises `StandardComplianceError` for violations. May provide standard data to the [Calculations Layer](core/calculations/calculations-architecture.md).

16. [Report Generation](core/reports/reports-architecture.md) (`src/core/reports/`)

*   **Purpose:** Generates various technical reports (e.g., heat loss, cable sizing, compliance) from project data and [calculations](core/calculations/calculations-architecture.md), using native document templates (XLSX, DOCX) and converting them to PDF.
    *   **Location:** `src/core/reports/`.
    *   **Interactions:** Consumed by the [Service Layer](core/services/services-architecture.md). Consumes data from [Repository](core/repositories/repositories-architecture.md) and [Calculations Layers](core/calculations/calculations-architecture.md). Raises `ReportGenerationError`.

17. [Data Import](core/data_import/data_import-architecture.md) (`src/core/data_import/`)

*   **Purpose:** Ingests structured data from external files (XLSX, JSON, CSV) into the application's database, handling both global and project-specific imports with robust validation.
    *   **Location:** `src/core/data_import/`.
    *   **Interactions:** Consumed by the [Service Layer](core/services/services-architecture.md). Utilizes Parsers, Validators, Mappers. Raises `ImportError`.

This comprehensive set of layers provides a clear roadmap for building your application, promoting modularity, testability, and maintainability across all functional areas.

## Proposed Architecture Flow from Principles Document:

1.  UI Layer: User interacts with the application (e.g., fills a form to create a project).
2.  UI Layer -> [Schema Layer](core/schemas/schemas-architecture.md) (Input): User input is passed to a [Pydantic schema](core/schemas/schemas-architecture.md) for validation (e.g., ProjectCreateSchema).
3.  [Schema Layer](core/schemas/schemas-architecture.md) -> [Service Layer](core/services/services-architecture.md): If validation passes, the validated data (as a Pydantic model or converted dict) is passed to the appropriate method in the [Service Layer](core/services/services-architecture.md) (e.g., project_service.create_project(project_data)).
4.  [Service Layer](core/services/services-architecture.md) -> [Repository Layer](core/repositories/repositories-architecture.md): The [Service Layer](core/services/services-architecture.md) uses the [Repository Layer](core/repositories/repositories-architecture.md) to interact with the database (e.g., project_repository.add(project_orm_instance)).
5.  [Repository Layer](core/repositories/repositories-architecture.md) -> [Database](core/database/architecture.md): The [Repository Layer](core/repositories/repositories-architecture.md) uses SQLAlchemy to persist the data to SQLite.
6.  [Database](core/database/architecture.md) -> [Repository Layer](core/repositories/repositories-architecture.md) -> [Service Layer](core/services/services-architecture.md): When data is retrieved, it flows back up.
7.  [Service Layer](core/services/services-architecture.md) -> [Schema Layer](core/schemas/schemas-architecture.md) (Output): The [Service Layer](core/services/services-architecture.md) can transform the [ORM models](core/models/models-architecture.md) into [Pydantic schemas](core/schemas/schemas-architecture.md) (e.g., ProjectReadSchema) for consistent output.
8.  [Schema Layer](core/schemas/schemas-architecture.md) -> UI Layer: The UI displays the formatted data to the user.

This layered approach, while requiring a bit more initial setup, will significantly improve the long-term maintainability, testability, and extendability of your Heat Tracing Design Application.
