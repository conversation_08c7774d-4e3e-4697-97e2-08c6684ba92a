# backend/tests/test_services/test_heat_tracing_service.py
"""
Tests for Heat Tracing Service

This module contains comprehensive tests for the HeatTracingService including:
- CRUD operation tests
- Business logic validation tests
- Integration tests with repository layer
- Error handling and exception tests
"""

import os
import sys
from unittest.mock import Mock

import pytest
from sqlalchemy.exc import IntegrityError

# Add the backend directory to the Python path
sys.path.insert(
    0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)

from core.errors.exceptions import (
    DataValidationError,
    DuplicateEntryError,
    NotFoundError,
)
from core.models.heat_tracing import Pipe, Vessel
from core.schemas.heat_tracing_schemas import (
    HeatLossCalculationInputSchema,
    HeatLossCalculationResultSchema,
    HeatTracingDesignInputSchema,
    PipeCreateSchema,
    PipeReadSchema,
    PipeUpdateSchema,
    StandardsValidationInputSchema,
    StandardsValidationResultSchema,
    VesselCreateSchema,
    VesselReadSchema,
)
from core.services.heat_tracing_service import HeatTracingService


class TestHeatTracingServicePipeOperations:
    """Test pipe operations in HeatTracingService."""

    @pytest.fixture
    def mock_repository(self):
        """Create a mock repository for testing."""
        mock_repo = Mock()
        mock_repo.db_session = Mock()
        mock_repo.pipes = Mock()
        mock_repo.vessels = Mock()
        mock_repo.ht_circuits = Mock()
        mock_repo.control_circuits = Mock()
        return mock_repo

    @pytest.fixture
    def heat_tracing_service(self, mock_repository):
        """Create a HeatTracingService instance for testing."""
        return HeatTracingService(mock_repository)

    @pytest.fixture
    def sample_pipe_create_data(self):
        """Sample pipe creation data."""
        return PipeCreateSchema(
            name="Test Pipe 001",
            project_id=1,
            pipe_material_id=10,
            insulation_material_id=15,
            nominal_diameter_mm=100.0,
            wall_thickness_mm=5.0,
            outer_diameter_mm=110.0,
            length_m=50.0,
            insulation_thickness_mm=50.0,
            fluid_type="Process Water",
            line_tag="L-001",
        )

    @pytest.fixture
    def sample_pipe_model(self):
        """Sample pipe model instance."""
        pipe = Pipe()
        pipe.id = 1
        pipe.name = "Test Pipe 001"
        pipe.project_id = 1
        pipe.length_m = 50.0
        pipe.is_deleted = False
        return pipe

    def test_create_pipe_success(
        self,
        heat_tracing_service,
        mock_repository,
        sample_pipe_create_data,
        sample_pipe_model,
    ):
        """Test successful pipe creation."""
        # Setup mocks
        mock_repository.pipes.create.return_value = sample_pipe_model

        # Call service method
        result = heat_tracing_service.create_pipe(sample_pipe_create_data)

        # Assertions
        assert isinstance(result, PipeReadSchema)
        assert result.name == "Test Pipe 001"
        assert result.id == 1

        # Verify repository calls
        mock_repository.pipes.create.assert_called_once()
        mock_repository.db_session.commit.assert_called_once()
        mock_repository.db_session.refresh.assert_called_once_with(sample_pipe_model)

    def test_create_pipe_validation_error(self, heat_tracing_service):
        """Test pipe creation with validation error."""
        # Create invalid data (outer diameter smaller than nominal)
        invalid_data = PipeCreateSchema(
            name="Test Pipe",
            project_id=1,
            pipe_material_id=10,
            insulation_material_id=15,
            nominal_diameter_mm=100.0,
            outer_diameter_mm=95.0,  # Invalid: smaller than nominal
            length_m=50.0,
            insulation_thickness_mm=50.0,
        )

        # Should raise validation error
        with pytest.raises(DataValidationError) as exc_info:
            heat_tracing_service.create_pipe(invalid_data)

        assert "diameter" in exc_info.value.metadata["validation_errors"]

    def test_create_pipe_duplicate_error(
        self, heat_tracing_service, mock_repository, sample_pipe_create_data
    ):
        """Test pipe creation with duplicate name."""
        # Setup mock to raise IntegrityError
        mock_repository.pipes.create.side_effect = IntegrityError("", "", "")

        # Should raise DuplicateEntryError
        with pytest.raises(DuplicateEntryError):
            heat_tracing_service.create_pipe(sample_pipe_create_data)

        # Verify rollback was called
        mock_repository.db_session.rollback.assert_called_once()

    def test_get_pipe_details_success(
        self, heat_tracing_service, mock_repository, sample_pipe_model
    ):
        """Test successful pipe details retrieval."""
        # Setup mock
        mock_repository.pipes.get_by_id.return_value = sample_pipe_model

        # Call service method
        result = heat_tracing_service.get_pipe_details(1)

        # Assertions
        assert isinstance(result, PipeReadSchema)
        assert result.id == 1
        assert result.name == "Test Pipe 001"

        # Verify repository call
        mock_repository.pipes.get_by_id.assert_called_once_with(1)

    def test_get_pipe_details_not_found(self, heat_tracing_service, mock_repository):
        """Test pipe details retrieval with non-existent ID."""
        # Setup mock to return None
        mock_repository.pipes.get_by_id.return_value = None

        # Should raise NotFoundError
        with pytest.raises(NotFoundError) as exc_info:
            heat_tracing_service.get_pipe_details(999)

        assert exc_info.value.code == "PIPE_NOT_FOUND"

    def test_get_pipe_details_deleted(
        self, heat_tracing_service, mock_repository, sample_pipe_model
    ):
        """Test pipe details retrieval for deleted pipe."""
        # Setup mock with deleted pipe
        sample_pipe_model.is_deleted = True
        mock_repository.pipes.get_by_id.return_value = sample_pipe_model

        # Should raise NotFoundError
        with pytest.raises(NotFoundError):
            heat_tracing_service.get_pipe_details(1)

    def test_update_pipe_success(
        self, heat_tracing_service, mock_repository, sample_pipe_model
    ):
        """Test successful pipe update."""
        # Setup mocks
        mock_repository.pipes.get_by_id.return_value = sample_pipe_model

        # Create update data
        update_data = PipeUpdateSchema(name="Updated Pipe Name", length_m=75.0)

        # Call service method
        result = heat_tracing_service.update_pipe(1, update_data)

        # Assertions
        assert isinstance(result, PipeReadSchema)
        assert result.id == 1

        # Verify repository calls
        mock_repository.pipes.get_by_id.assert_called_once_with(1)
        mock_repository.db_session.commit.assert_called_once()

    def test_update_pipe_not_found(self, heat_tracing_service, mock_repository):
        """Test pipe update with non-existent ID."""
        # Setup mock to return None
        mock_repository.pipes.get_by_id.return_value = None

        update_data = PipeUpdateSchema(name="Updated Name")

        # Should raise NotFoundError
        with pytest.raises(NotFoundError):
            heat_tracing_service.update_pipe(999, update_data)

    def test_delete_pipe_success(
        self, heat_tracing_service, mock_repository, sample_pipe_model
    ):
        """Test successful pipe deletion."""
        # Setup mock
        mock_repository.pipes.get_by_id.return_value = sample_pipe_model

        # Call service method
        heat_tracing_service.delete_pipe(1)

        # Assertions
        assert sample_pipe_model.is_deleted is True
        assert sample_pipe_model.deleted_at is not None

        # Verify repository calls
        mock_repository.pipes.get_by_id.assert_called_once_with(1)
        mock_repository.db_session.commit.assert_called_once()

    def test_get_pipes_list_success(self, heat_tracing_service, mock_repository):
        """Test successful pipes list retrieval."""
        # Setup mocks
        mock_pipes = [Mock(id=1, name="Pipe 1"), Mock(id=2, name="Pipe 2")]
        mock_repository.pipes.get_by_project_id.return_value = mock_pipes
        mock_repository.pipes.count_by_project.return_value = 2

        # Call service method
        result = heat_tracing_service.get_pipes_list(project_id=1, page=1, per_page=10)

        # Assertions
        assert result.total == 2
        assert result.page == 1
        assert result.per_page == 10
        assert len(result.pipes) == 2

        # Verify repository calls
        mock_repository.pipes.get_by_project_id.assert_called_once_with(
            1, skip=0, limit=10
        )
        mock_repository.pipes.count_by_project.assert_called_once_with(1)


class TestHeatTracingServiceVesselOperations:
    """Test vessel operations in HeatTracingService."""

    @pytest.fixture
    def mock_repository(self):
        """Create a mock repository for testing."""
        mock_repo = Mock()
        mock_repo.db_session = Mock()
        mock_repo.vessels = Mock()
        return mock_repo

    @pytest.fixture
    def heat_tracing_service(self, mock_repository):
        """Create a HeatTracingService instance for testing."""
        return HeatTracingService(mock_repository)

    @pytest.fixture
    def sample_vessel_create_data(self):
        """Sample vessel creation data."""
        return VesselCreateSchema(
            name="Test Vessel T-001",
            project_id=1,
            material_id=12,
            insulation_material_id=15,
            dimensions_json='{"type": "cylinder", "diameter": 2.0, "height": 3.0}',
            surface_area_m2=25.13,
            insulation_thickness_mm=75.0,
            equipment_tag="T-001",
        )

    @pytest.fixture
    def sample_vessel_model(self):
        """Sample vessel model instance."""
        vessel = Vessel()
        vessel.id = 1
        vessel.name = "Test Vessel T-001"
        vessel.project_id = 1
        vessel.surface_area_m2 = 25.13
        vessel.is_deleted = False
        return vessel

    def test_create_vessel_success(
        self,
        heat_tracing_service,
        mock_repository,
        sample_vessel_create_data,
        sample_vessel_model,
    ):
        """Test successful vessel creation."""
        # Setup mocks
        mock_repository.vessels.create.return_value = sample_vessel_model

        # Call service method
        result = heat_tracing_service.create_vessel(sample_vessel_create_data)

        # Assertions
        assert isinstance(result, VesselReadSchema)
        assert result.name == "Test Vessel T-001"
        assert result.id == 1

        # Verify repository calls
        mock_repository.vessels.create.assert_called_once()
        mock_repository.db_session.commit.assert_called_once()

    def test_create_vessel_invalid_dimensions(self, heat_tracing_service):
        """Test vessel creation with invalid dimensions JSON."""
        invalid_data = VesselCreateSchema(
            name="Test Vessel",
            project_id=1,
            material_id=12,
            insulation_material_id=15,
            dimensions_json='{"diameter": 2.0, "height": 3.0}',  # Missing type
            surface_area_m2=25.13,
            insulation_thickness_mm=75.0,
        )

        # Should raise validation error
        with pytest.raises(DataValidationError) as exc_info:
            heat_tracing_service.create_vessel(invalid_data)

        assert "dimensions" in exc_info.value.metadata["validation_errors"]

    def test_get_vessel_details_success(
        self, heat_tracing_service, mock_repository, sample_vessel_model
    ):
        """Test successful vessel details retrieval."""
        # Setup mock
        mock_repository.vessels.get_by_id.return_value = sample_vessel_model

        # Call service method
        result = heat_tracing_service.get_vessel_details(1)

        # Assertions
        assert isinstance(result, VesselReadSchema)
        assert result.id == 1
        assert result.name == "Test Vessel T-001"


class TestHeatTracingServiceCalculations:
    """Test calculation operations in HeatTracingService."""

    @pytest.fixture
    def mock_repository(self):
        """Create a mock repository for testing."""
        mock_repo = Mock()
        mock_repo.db_session = Mock()
        mock_repo.pipes = Mock()
        return mock_repo

    @pytest.fixture
    def heat_tracing_service(self, mock_repository):
        """Create a HeatTracingService instance for testing."""
        return HeatTracingService(mock_repository)

    @pytest.fixture
    def sample_pipe_model(self):
        """Sample pipe model instance."""
        pipe = Pipe()
        pipe.id = 1
        pipe.name = "Test Pipe"
        pipe.outer_diameter_mm = 110.0
        pipe.length_m = 50.0
        pipe.insulation_thickness_mm = 50.0
        pipe.is_deleted = False
        return pipe

    @pytest.fixture
    def sample_calculation_input(self):
        """Sample calculation input."""
        return HeatLossCalculationInputSchema(
            pipe_diameter=0.11,
            pipe_length=50.0,
            fluid_temperature=60.0,
            ambient_temperature=-10.0,
            insulation_thickness=0.05,
            insulation_type="mineral_wool",
            wind_speed=5.0,
            pipe_material="carbon_steel",
        )

    def test_calculate_pipe_heat_loss_success(
        self,
        heat_tracing_service,
        mock_repository,
        sample_pipe_model,
        sample_calculation_input,
    ):
        """Test successful pipe heat loss calculation."""
        # Setup mocks
        mock_repository.pipes.get_by_id.return_value = sample_pipe_model
        mock_repository.pipes.update_heat_loss_calculation.return_value = (
            sample_pipe_model
        )

        # Call service method
        result = heat_tracing_service.calculate_pipe_heat_loss(
            1, sample_calculation_input
        )

        # Assertions
        assert isinstance(result, HeatLossCalculationResultSchema)
        assert result.heat_loss_rate > 0
        assert result.total_heat_loss > 0
        assert result.required_power > 0

        # Verify repository calls
        mock_repository.pipes.get_by_id.assert_called_once_with(1)
        mock_repository.pipes.update_heat_loss_calculation.assert_called_once()
        mock_repository.db_session.commit.assert_called_once()

    def test_calculate_pipe_heat_loss_pipe_not_found(
        self, heat_tracing_service, mock_repository, sample_calculation_input
    ):
        """Test heat loss calculation with non-existent pipe."""
        # Setup mock to return None
        mock_repository.pipes.get_by_id.return_value = None

        # Should raise NotFoundError
        with pytest.raises(NotFoundError):
            heat_tracing_service.calculate_pipe_heat_loss(999, sample_calculation_input)

    def test_validate_standards_compliance_success(self, heat_tracing_service):
        """Test successful standards compliance validation."""
        # Create sample validation input
        heat_loss_result = HeatLossCalculationResultSchema(
            heat_loss_rate=25.5,
            total_heat_loss=1275.0,
            surface_temperature=15.0,
            required_power=1530.0,
            safety_factor=1.2,
        )

        validation_input = StandardsValidationInputSchema(
            heat_loss_result=heat_loss_result,
            design_parameters={"fluid_temperature": 60.0},
            project_standards=["TR_50410"],
            hazardous_area_zone="Zone_1",
            gas_group="IIA",
            temperature_class="T3",
        )

        # Call service method
        result = heat_tracing_service.validate_standards_compliance(validation_input)

        # Assertions
        assert isinstance(result, StandardsValidationResultSchema)
        assert result.standard is not None
        assert isinstance(result.is_compliant, bool)


class TestHeatTracingServiceDesignWorkflow:
    """Test design workflow operations in HeatTracingService."""

    @pytest.fixture
    def mock_repository(self):
        """Create a mock repository for testing."""
        mock_repo = Mock()
        mock_repo.db_session = Mock()
        mock_repo.pipes = Mock()
        mock_repo.vessels = Mock()
        return mock_repo

    @pytest.fixture
    def heat_tracing_service(self, mock_repository):
        """Create a HeatTracingService instance for testing."""
        return HeatTracingService(mock_repository)

    @pytest.fixture
    def sample_design_input(self):
        """Sample design workflow input."""
        heat_loss_result = HeatLossCalculationResultSchema(
            heat_loss_rate=25.5,
            total_heat_loss=1275.0,
            surface_temperature=15.0,
            required_power=1530.0,
            safety_factor=1.2,
        )

        standards_context = StandardsValidationInputSchema(
            heat_loss_result=heat_loss_result,
            design_parameters={},
            project_standards=["TR_50410"],
            hazardous_area_zone="Zone_1",
            gas_group="IIA",
            temperature_class="T3",
        )

        return HeatTracingDesignInputSchema(
            project_id=1,
            pipe_ids=[1, 2],
            vessel_ids=None,
            design_parameters={"fluid_temperature": 60.0, "ambient_temperature": -10.0},
            standards_context=standards_context,
            auto_assign_circuits=True,
            optimization_enabled=True,
        )

    def test_execute_design_workflow_success(
        self, heat_tracing_service, mock_repository, sample_design_input
    ):
        """Test successful design workflow execution."""
        # Setup mocks
        sample_pipe = Mock()
        sample_pipe.id = 1
        sample_pipe.name = "Test Pipe"
        sample_pipe.outer_diameter_mm = 110.0
        sample_pipe.length_m = 50.0
        sample_pipe.insulation_thickness_mm = 50.0

        mock_repository.pipes.get_by_id.return_value = sample_pipe
        mock_repository.pipes.update_heat_loss_calculation.return_value = sample_pipe

        # Call service method
        result = heat_tracing_service.execute_heat_tracing_design(sample_design_input)

        # Assertions
        assert result.project_id == 1
        assert result.design_summary is not None
        assert isinstance(result.design_summary, dict)

        # Verify repository calls were made
        assert mock_repository.db_session.commit.called
