# backend/core/services/heat_tracing_service.py
"""
Heat Tracing Service Layer

This module contains the business logic for Heat Tracing entity operations including:
- Heat tracing design workflow orchestration
- Integration with calculations and standards layers
- Business validation and rules enforcement
- Transaction management and orchestration
- Error handling and logging
"""

import json
from typing import Any, Dict, List, Optional

from sqlalchemy.exc import IntegrityError, SQLAlchemyError

try:
    from backend.config.logging_config import get_logger
    from backend.core.errors.exceptions import (
        DatabaseError,
        DataValidationError,
        DuplicateEntryError,
        NotFoundError,
    )
    from backend.core.models.heat_tracing import Pipe
    from backend.core.repositories.heat_tracing_repository import HeatTracingRepository
except ImportError:
    # For testing and relative imports
    import sys
    import os

    sys.path.insert(
        0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    )
    from config.logging_config import get_logger
    from core.errors.exceptions import (
        <PERSON><PERSON>rro<PERSON>,
        DataValidationError,
        Du<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>r,
        NotFoundError,
    )
    from core.models.heat_tracing import Pipe
    from core.repositories.heat_tracing_repository import HeatTracingRepository
try:
    from backend.core.schemas.heat_tracing_schemas import (
        HeatLossCalculationInputSchema,
        HeatLossCalculationResultSchema,
        # Design workflow schemas
        HeatTracingDesignInputSchema,
        HeatTracingDesignResultSchema,
        # HTCircuit schemas
        HTCircuitReadSchema,
        # Pipe schemas
        PipeCreateSchema,
        PipeListResponseSchema,
        PipeReadSchema,
        PipeSummarySchema,
        PipeUpdateSchema,
        StandardsValidationInputSchema,
        StandardsValidationResultSchema,
        # Vessel schemas
        VesselCreateSchema,
        VesselListResponseSchema,
        VesselReadSchema,
        VesselSummarySchema,
    )
except ImportError:
    from core.schemas.heat_tracing_schemas import (
        HeatLossCalculationInputSchema,
        HeatLossCalculationResultSchema,
        # Design workflow schemas
        HeatTracingDesignInputSchema,
        HeatTracingDesignResultSchema,
        # HTCircuit schemas
        HTCircuitReadSchema,
        # Pipe schemas
        PipeCreateSchema,
        PipeListResponseSchema,
        PipeReadSchema,
        PipeSummarySchema,
        PipeUpdateSchema,
        StandardsValidationInputSchema,
        StandardsValidationResultSchema,
        # Vessel schemas
        VesselCreateSchema,
        VesselListResponseSchema,
        VesselReadSchema,
        VesselSummarySchema,
    )

# Initialize logger for this module
logger = get_logger(__name__)


class HeatTracingService:
    """
    Service class for Heat Tracing entity business logic.

    This service handles all business operations related to heat tracing including
    CRUD operations, design workflow orchestration, calculations integration,
    and standards compliance validation.
    """

    def __init__(
        self,
        heat_tracing_repository: HeatTracingRepository,
        calculation_service: Optional[Any] = None,
        standards_manager: Optional[Any] = None,
    ):
        """
        Initialize the Heat Tracing service.

        Args:
            heat_tracing_repository: Repository for heat tracing data access
            calculation_service: Service for heat loss calculations (optional)
            standards_manager: Manager for standards compliance (optional)
        """
        self.repository = heat_tracing_repository
        self.calculation_service = calculation_service
        self.standards_manager = standards_manager
        logger.debug(
            f"HeatTracingService initialized with repository: {type(heat_tracing_repository).__name__}"
        )

    # ============================================================================
    # PIPE OPERATIONS
    # ============================================================================

    def create_pipe(self, pipe_data: PipeCreateSchema) -> PipeReadSchema:
        """
        Create a new pipe with business validation.

        Args:
            pipe_data: Validated pipe creation data

        Returns:
            PipeReadSchema: Created pipe data

        Raises:
            DuplicateEntryError: If pipe name already exists in project
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Attempting to create new pipe: '{pipe_data.name}' in project {pipe_data.project_id}"
        )

        try:
            # Business validation
            self._validate_pipe_creation(pipe_data)

            # Convert schema to dict for repository
            pipe_dict = pipe_data.model_dump()

            # Create pipe via repository
            new_pipe = self.repository.pipes.create(pipe_dict)

            # Commit the transaction
            self.repository.db_session.commit()
            self.repository.db_session.refresh(new_pipe)

            logger.info(
                f"Pipe '{new_pipe.name}' (ID: {new_pipe.id}) created successfully"
            )

            # Convert to read schema
            return PipeReadSchema.model_validate(new_pipe)

        except IntegrityError as e:
            self.repository.db_session.rollback()
            logger.warning(f"Duplicate pipe detected: {pipe_data.name}")
            raise DuplicateEntryError(
                message=f"A pipe with the name '{pipe_data.name}' already exists in this project.",
                original_exception=e,
            )

        except SQLAlchemyError as e:
            self.repository.db_session.rollback()
            logger.error(
                f"Database error creating pipe '{pipe_data.name}': {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to create pipe due to database error: {str(e)}",
                original_exception=e,
            )

    def get_pipe_details(self, pipe_id: int) -> PipeReadSchema:
        """
        Retrieve detailed pipe information.

        Args:
            pipe_id: Pipe ID

        Returns:
            PipeReadSchema: Pipe details

        Raises:
            NotFoundError: If pipe doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving pipe details for ID: {pipe_id}")

        try:
            pipe = self.repository.pipes.get_by_id(pipe_id)
            if pipe is None or pipe.is_deleted:
                logger.warning(f"Pipe not found: {pipe_id}")
                raise NotFoundError(
                    code="PIPE_NOT_FOUND", detail=f"Pipe with ID {pipe_id} not found"
                )

            logger.debug(f"Pipe found: '{pipe.name}' (ID: {pipe.id})")
            return PipeReadSchema.model_validate(pipe)

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving pipe {pipe_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to retrieve pipe due to database error: {str(e)}",
                original_exception=e,
            )

    def update_pipe(self, pipe_id: int, pipe_data: PipeUpdateSchema) -> PipeReadSchema:
        """
        Update an existing pipe.

        Args:
            pipe_id: Pipe ID
            pipe_data: Validated pipe update data

        Returns:
            PipeReadSchema: Updated pipe data

        Raises:
            NotFoundError: If pipe doesn't exist
            DuplicateEntryError: If updated name conflicts
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to update pipe: {pipe_id}")

        try:
            # Get existing pipe
            existing_pipe = self.repository.pipes.get_by_id(pipe_id)
            if existing_pipe is None or existing_pipe.is_deleted:
                raise NotFoundError(
                    code="PIPE_NOT_FOUND", detail=f"Pipe with ID {pipe_id} not found"
                )

            # Business validation for updates
            self._validate_pipe_update(existing_pipe, pipe_data)

            # Update only provided fields
            update_dict = pipe_data.model_dump(exclude_unset=True)

            if not update_dict:
                logger.debug(f"No fields to update for pipe {pipe_id}")
                return PipeReadSchema.model_validate(existing_pipe)

            # Update the pipe
            for field, value in update_dict.items():
                setattr(existing_pipe, field, value)

            # Commit the transaction
            self.repository.db_session.commit()
            self.repository.db_session.refresh(existing_pipe)

            logger.info(
                f"Pipe '{existing_pipe.name}' (ID: {existing_pipe.id}) updated successfully"
            )
            return PipeReadSchema.model_validate(existing_pipe)

        except (NotFoundError, DuplicateEntryError):
            self.repository.db_session.rollback()
            raise
        except IntegrityError as e:
            self.repository.db_session.rollback()
            logger.warning(f"Duplicate constraint violation updating pipe {pipe_id}")
            raise DuplicateEntryError(
                message="Update would violate unique constraint.",
                original_exception=e,
            )
        except SQLAlchemyError as e:
            self.repository.db_session.rollback()
            logger.error(f"Database error updating pipe {pipe_id}: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to update pipe due to database error: {str(e)}",
                original_exception=e,
            )

    def delete_pipe(
        self, pipe_id: int, deleted_by_user_id: Optional[int] = None
    ) -> None:
        """
        Soft delete a pipe.

        Args:
            pipe_id: Pipe ID
            deleted_by_user_id: ID of user performing the deletion

        Raises:
            NotFoundError: If pipe doesn't exist
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to delete pipe: {pipe_id}")

        try:
            # Get existing pipe
            existing_pipe = self.repository.pipes.get_by_id(pipe_id)
            if existing_pipe is None or existing_pipe.is_deleted:
                raise NotFoundError(
                    code="PIPE_NOT_FOUND", detail=f"Pipe with ID {pipe_id} not found"
                )

            # Perform soft delete
            from datetime import datetime, timezone

            existing_pipe.is_deleted = True
            existing_pipe.deleted_at = datetime.now(timezone.utc)
            existing_pipe.deleted_by_user_id = deleted_by_user_id

            # Commit the transaction
            self.repository.db_session.commit()

            logger.info(
                f"Pipe '{existing_pipe.name}' (ID: {existing_pipe.id}) deleted successfully"
            )

        except NotFoundError:
            self.repository.db_session.rollback()
            raise
        except SQLAlchemyError as e:
            self.repository.db_session.rollback()
            logger.error(f"Database error deleting pipe {pipe_id}: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to delete pipe due to database error: {str(e)}",
                original_exception=e,
            )

    def get_pipes_list(
        self, project_id: int, page: int = 1, per_page: int = 10
    ) -> PipeListResponseSchema:
        """
        Get paginated list of pipes for a project.

        Args:
            project_id: Project ID
            page: Page number (1-based)
            per_page: Number of pipes per page

        Returns:
            PipeListResponseSchema: Paginated pipe list

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving pipes list for project {project_id}: page={page}, per_page={per_page}"
        )

        try:
            # Calculate offset
            skip = (page - 1) * per_page

            # Get pipes from repository
            pipes = self.repository.pipes.get_by_project_id(
                project_id, skip=skip, limit=per_page
            )

            # Get total count
            total = self.repository.pipes.count_by_project(project_id)

            # Convert to summary schemas
            pipe_summaries = [PipeSummarySchema.model_validate(p) for p in pipes]

            # Calculate total pages
            import math

            total_pages = math.ceil(total / per_page) if total > 0 else 1

            logger.debug(f"Retrieved {len(pipe_summaries)} pipes (total: {total})")

            return PipeListResponseSchema(
                pipes=pipe_summaries,
                total=total,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving pipes list: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to retrieve pipes list due to database error: {str(e)}",
                original_exception=e,
            )

    # ============================================================================
    # VESSEL OPERATIONS
    # ============================================================================

    def create_vessel(self, vessel_data: VesselCreateSchema) -> VesselReadSchema:
        """
        Create a new vessel with business validation.

        Args:
            vessel_data: Validated vessel creation data

        Returns:
            VesselReadSchema: Created vessel data

        Raises:
            DuplicateEntryError: If vessel name already exists in project
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Attempting to create new vessel: '{vessel_data.name}' in project {vessel_data.project_id}"
        )

        try:
            # Business validation
            self._validate_vessel_creation(vessel_data)

            # Convert schema to dict for repository
            vessel_dict = vessel_data.model_dump()

            # Create vessel via repository
            new_vessel = self.repository.vessels.create(vessel_dict)

            # Commit the transaction
            self.repository.db_session.commit()
            self.repository.db_session.refresh(new_vessel)

            logger.info(
                f"Vessel '{new_vessel.name}' (ID: {new_vessel.id}) created successfully"
            )

            # Convert to read schema
            return VesselReadSchema.model_validate(new_vessel)

        except IntegrityError as e:
            self.repository.db_session.rollback()
            logger.warning(f"Duplicate vessel detected: {vessel_data.name}")
            raise DuplicateEntryError(
                message=f"A vessel with the name '{vessel_data.name}' already exists in this project.",
                original_exception=e,
            )

        except SQLAlchemyError as e:
            self.repository.db_session.rollback()
            logger.error(
                f"Database error creating vessel '{vessel_data.name}': {e}",
                exc_info=True,
            )
            raise DatabaseError(
                reason=f"Failed to create vessel due to database error: {str(e)}",
                original_exception=e,
            )

    def get_vessel_details(self, vessel_id: int) -> VesselReadSchema:
        """
        Retrieve detailed vessel information.

        Args:
            vessel_id: Vessel ID

        Returns:
            VesselReadSchema: Vessel details

        Raises:
            NotFoundError: If vessel doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving vessel details for ID: {vessel_id}")

        try:
            vessel = self.repository.vessels.get_by_id(vessel_id)
            if vessel is None or vessel.is_deleted:
                logger.warning(f"Vessel not found: {vessel_id}")
                raise NotFoundError(
                    code="VESSEL_NOT_FOUND",
                    detail=f"Vessel with ID {vessel_id} not found",
                )

            logger.debug(f"Vessel found: '{vessel.name}' (ID: {vessel.id})")
            return VesselReadSchema.model_validate(vessel)

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving vessel {vessel_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to retrieve vessel due to database error: {str(e)}",
                original_exception=e,
            )

    def get_vessels_list(
        self, project_id: int, page: int = 1, per_page: int = 10
    ) -> VesselListResponseSchema:
        """
        Get paginated list of vessels for a project.

        Args:
            project_id: Project ID
            page: Page number (1-based)
            per_page: Number of vessels per page

        Returns:
            VesselListResponseSchema: Paginated vessel list

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving vessels list for project {project_id}: page={page}, per_page={per_page}"
        )

        try:
            # Calculate offset
            skip = (page - 1) * per_page

            # Get vessels from repository
            vessels = self.repository.vessels.get_by_project_id(
                project_id, skip=skip, limit=per_page
            )

            # Get total count
            total = self.repository.vessels.count_by_project(project_id)

            # Convert to summary schemas
            vessel_summaries = [VesselSummarySchema.model_validate(v) for v in vessels]

            # Calculate total pages
            import math

            total_pages = math.ceil(total / per_page) if total > 0 else 1

            logger.debug(f"Retrieved {len(vessel_summaries)} vessels (total: {total})")

            return VesselListResponseSchema(
                vessels=vessel_summaries,
                total=total,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving vessels list: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to retrieve vessels list due to database error: {str(e)}",
                original_exception=e,
            )

    # ============================================================================
    # HEAT LOSS CALCULATION INTEGRATION
    # ============================================================================

    def calculate_pipe_heat_loss(
        self, pipe_id: int, calculation_input: HeatLossCalculationInputSchema
    ) -> HeatLossCalculationResultSchema:
        """
        Calculate heat loss for a pipe using the calculations service.

        Args:
            pipe_id: Pipe ID
            calculation_input: Heat loss calculation inputs

        Returns:
            HeatLossCalculationResultSchema: Calculation results

        Raises:
            NotFoundError: If pipe doesn't exist
            DataValidationError: If calculation service is not available
            DatabaseError: If database operation fails
        """
        logger.info(f"Calculating heat loss for pipe {pipe_id}")

        if self.calculation_service is None:
            raise DataValidationError(
                details={"calculation_service": "Calculation service is not available"}
            )

        try:
            # Get pipe details
            pipe = self.repository.pipes.get_by_id(pipe_id)
            if pipe is None or pipe.is_deleted:
                raise NotFoundError(
                    code="PIPE_NOT_FOUND", detail=f"Pipe with ID {pipe_id} not found"
                )

            # Perform heat loss calculation using the calculation service
            # This would integrate with the actual CalculationService
            calculation_result = self._perform_heat_loss_calculation(calculation_input)

            # Update pipe with calculation results
            self.repository.pipes.update_heat_loss_calculation(
                pipe_id=pipe_id,
                heat_loss_wm=calculation_result.heat_loss_rate,
                required_output_wm=calculation_result.required_power / pipe.length_m,
            )

            # Commit the transaction
            self.repository.db_session.commit()

            logger.info(
                f"Heat loss calculation completed for pipe {pipe_id}: {calculation_result.heat_loss_rate} W/m"
            )
            return calculation_result

        except NotFoundError:
            raise
        except SQLAlchemyError as e:
            self.repository.db_session.rollback()
            logger.error(
                f"Database error calculating heat loss for pipe {pipe_id}: {e}",
                exc_info=True,
            )
            raise DatabaseError(
                reason=f"Failed to calculate heat loss due to database error: {str(e)}",
                original_exception=e,
            )

    def validate_standards_compliance(
        self, validation_input: StandardsValidationInputSchema
    ) -> StandardsValidationResultSchema:
        """
        Validate standards compliance using the standards manager.

        Args:
            validation_input: Standards validation inputs

        Returns:
            StandardsValidationResultSchema: Validation results

        Raises:
            DataValidationError: If standards manager is not available
        """
        logger.info("Validating standards compliance")

        if self.standards_manager is None:
            raise DataValidationError(
                details={"standards_manager": "Standards manager is not available"}
            )

        try:
            # Perform standards validation using the standards manager
            # This would integrate with the actual StandardsManager
            validation_result = self._perform_standards_validation(validation_input)

            logger.info(
                f"Standards validation completed: {'COMPLIANT' if validation_result.is_compliant else 'NON-COMPLIANT'}"
            )
            return validation_result

        except Exception as e:
            logger.error(f"Error validating standards compliance: {e}", exc_info=True)
            raise

    # ============================================================================
    # DESIGN WORKFLOW ORCHESTRATION
    # ============================================================================

    def execute_heat_tracing_design(
        self, design_input: HeatTracingDesignInputSchema
    ) -> HeatTracingDesignResultSchema:
        """
        Execute the complete heat tracing design workflow.

        This method orchestrates the entire heat tracing design process including:
        1. Heat loss calculations for pipes and vessels
        2. Standards compliance validation
        3. Circuit assignment and optimization
        4. Power calculations and load balancing

        Args:
            design_input: Design workflow inputs

        Returns:
            HeatTracingDesignResultSchema: Complete design results

        Raises:
            DataValidationError: If design inputs are invalid
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Executing heat tracing design workflow for project {design_input.project_id}"
        )

        try:
            # Initialize result containers
            designed_pipes = []
            designed_vessels = []
            created_circuits = []
            calculation_results = []
            validation_results = []
            warnings = []
            errors = []

            # Step 1: Process pipes if provided
            if design_input.pipe_ids:
                logger.debug(f"Processing {len(design_input.pipe_ids)} pipes")
                for pipe_id in design_input.pipe_ids:
                    try:
                        pipe_result = self._design_pipe_heat_tracing(
                            pipe_id, design_input
                        )
                        designed_pipes.append(pipe_result["pipe"])
                        calculation_results.extend(pipe_result["calculations"])
                        validation_results.extend(pipe_result["validations"])
                        if design_input.auto_assign_circuits:
                            created_circuits.extend(pipe_result["circuits"])
                    except Exception as e:
                        error_msg = f"Failed to design heat tracing for pipe {pipe_id}: {str(e)}"
                        logger.warning(error_msg)
                        errors.append(error_msg)

            # Step 2: Process vessels if provided
            if design_input.vessel_ids:
                logger.debug(f"Processing {len(design_input.vessel_ids)} vessels")
                for vessel_id in design_input.vessel_ids:
                    try:
                        vessel_result = self._design_vessel_heat_tracing(
                            vessel_id, design_input
                        )
                        designed_vessels.append(vessel_result["vessel"])
                        calculation_results.extend(vessel_result["calculations"])
                        validation_results.extend(vessel_result["validations"])
                        if design_input.auto_assign_circuits:
                            created_circuits.extend(vessel_result["circuits"])
                    except Exception as e:
                        error_msg = f"Failed to design heat tracing for vessel {vessel_id}: {str(e)}"
                        logger.warning(error_msg)
                        errors.append(error_msg)

            # Step 3: Generate design summary
            design_summary = self._generate_design_summary(
                designed_pipes, designed_vessels, created_circuits, calculation_results
            )

            # Commit all changes
            self.repository.db_session.commit()

            logger.info(
                f"Heat tracing design workflow completed for project {design_input.project_id}"
            )

            return HeatTracingDesignResultSchema(
                project_id=design_input.project_id,
                designed_pipes=designed_pipes,
                designed_vessels=designed_vessels,
                created_circuits=created_circuits,
                calculation_results=calculation_results,
                validation_results=validation_results,
                design_summary=design_summary,
                warnings=warnings,
                errors=errors,
            )

        except Exception as e:
            self.repository.db_session.rollback()
            logger.error(
                f"Error executing heat tracing design workflow: {e}", exc_info=True
            )
            raise

    # ============================================================================
    # PROJECT SUMMARY AND READINESS
    # ============================================================================

    def get_project_heat_tracing_summary(self, project_id: int) -> Dict[str, Any]:
        """
        Get heat tracing summary for a project.

        Args:
            project_id: Project ID

        Returns:
            Dict[str, Any]: Project heat tracing summary

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Generating heat tracing summary for project {project_id}")

        try:
            return self.repository.get_project_summary(project_id)

        except Exception as e:
            logger.error(f"Error generating project summary: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to generate project summary: {str(e)}",
                original_exception=e,
            )

    def get_project_design_readiness(self, project_id: int) -> Dict[str, Any]:
        """
        Check design readiness for a project.

        Args:
            project_id: Project ID

        Returns:
            Dict[str, Any]: Design readiness status

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Checking design readiness for project {project_id}")

        try:
            return self.repository.get_design_readiness(project_id)

        except Exception as e:
            logger.error(f"Error checking design readiness: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to check design readiness: {str(e)}",
                original_exception=e,
            )

    # ============================================================================
    # PRIVATE HELPER METHODS
    # ============================================================================

    def _validate_pipe_creation(self, pipe_data: PipeCreateSchema) -> None:
        """
        Perform business validation for pipe creation.

        Args:
            pipe_data: Pipe creation data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Diameter validation
        if pipe_data.outer_diameter_mm and pipe_data.nominal_diameter_mm:
            if pipe_data.outer_diameter_mm <= pipe_data.nominal_diameter_mm:
                raise DataValidationError(
                    details={
                        "diameter": "Outer diameter must be greater than nominal diameter"
                    }
                )

        # Fluid properties validation
        if pipe_data.freezing_point_c is not None and pipe_data.freezing_point_c > 100:
            raise DataValidationError(
                details={
                    "freezing_point": "Freezing point seems unrealistic for typical fluids"
                }
            )

    def _validate_pipe_update(
        self, existing_pipe: Pipe, pipe_data: PipeUpdateSchema
    ) -> None:
        """
        Perform business validation for pipe updates.

        Args:
            existing_pipe: Current pipe data
            pipe_data: Update data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Get effective values (updated or existing)
        outer_diameter = (
            pipe_data.outer_diameter_mm
            if pipe_data.outer_diameter_mm is not None
            else existing_pipe.outer_diameter_mm
        )
        nominal_diameter = (
            pipe_data.nominal_diameter_mm
            if pipe_data.nominal_diameter_mm is not None
            else existing_pipe.nominal_diameter_mm
        )

        # Diameter validation
        if outer_diameter and nominal_diameter:
            if outer_diameter <= nominal_diameter:
                raise DataValidationError(
                    details={
                        "diameter": "Outer diameter must be greater than nominal diameter"
                    }
                )

    def _validate_vessel_creation(self, vessel_data: VesselCreateSchema) -> None:
        """
        Perform business validation for vessel creation.

        Args:
            vessel_data: Vessel creation data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Validate dimensions JSON
        try:
            dimensions = json.loads(vessel_data.dimensions_json)
            vessel_type = dimensions.get("type", "").lower()

            # Basic dimension validation based on vessel type
            if vessel_type == "cylinder":
                if "diameter" not in dimensions or "height" not in dimensions:
                    raise DataValidationError(
                        details={
                            "dimensions": "Cylindrical vessel must have diameter and height"
                        }
                    )
                if dimensions["diameter"] <= 0 or dimensions["height"] <= 0:
                    raise DataValidationError(
                        details={"dimensions": "Vessel dimensions must be positive"}
                    )

        except json.JSONDecodeError:
            raise DataValidationError(
                details={"dimensions_json": "Invalid JSON format for vessel dimensions"}
            )

    def _perform_heat_loss_calculation(
        self, calculation_input: HeatLossCalculationInputSchema
    ) -> HeatLossCalculationResultSchema:
        """
        Perform heat loss calculation using the calculation service.

        Args:
            calculation_input: Calculation inputs

        Returns:
            HeatLossCalculationResultSchema: Calculation results
        """
        # This is a placeholder implementation
        # In the actual implementation, this would call the CalculationService

        # Simple heat loss calculation for demonstration
        pipe_circumference = 3.14159 * calculation_input.pipe_diameter
        surface_area = pipe_circumference * calculation_input.pipe_length

        # Basic heat transfer calculation (simplified)
        temp_diff = (
            calculation_input.fluid_temperature - calculation_input.ambient_temperature
        )
        heat_loss_rate = (
            25.0 * temp_diff / calculation_input.insulation_thickness
        )  # W/m (simplified)
        total_heat_loss = heat_loss_rate * calculation_input.pipe_length

        # Apply safety factor
        safety_factor = 1.2
        required_power = total_heat_loss * safety_factor

        return HeatLossCalculationResultSchema(
            heat_loss_rate=heat_loss_rate,
            total_heat_loss=total_heat_loss,
            surface_temperature=calculation_input.ambient_temperature
            + 10.0,  # Simplified
            required_power=required_power,
            safety_factor=safety_factor,
            calculation_metadata={
                "method": "simplified_demo",
                "pipe_circumference": pipe_circumference,
                "surface_area": surface_area,
            },
        )

    def _perform_standards_validation(
        self, validation_input: StandardsValidationInputSchema
    ) -> StandardsValidationResultSchema:
        """
        Perform standards validation using the standards manager.

        Args:
            validation_input: Validation inputs

        Returns:
            StandardsValidationResultSchema: Validation results
        """
        # This is a placeholder implementation
        # In the actual implementation, this would call the StandardsManager

        violations = []
        warnings = []

        # Basic validation checks (simplified)
        heat_loss = validation_input.heat_loss_result

        if heat_loss.required_power > 5000:  # 5kW limit example
            violations.append(
                "Required power exceeds 5kW limit for standard installation"
            )

        if heat_loss.safety_factor < 1.1:
            warnings.append("Safety factor below recommended minimum of 1.1")

        is_compliant = len(violations) == 0

        return StandardsValidationResultSchema(
            is_compliant=is_compliant,
            standard="TR 50410 / IEC 60079-30-1",
            violations=violations,
            warnings=warnings,
            applied_factors={"safety_factor": heat_loss.safety_factor},
            metadata={"validation_method": "simplified_demo"},
        )

    def _design_pipe_heat_tracing(
        self, pipe_id: int, design_input: HeatTracingDesignInputSchema
    ) -> Dict[str, Any]:
        """
        Design heat tracing for a single pipe.

        Args:
            pipe_id: Pipe ID
            design_input: Design inputs

        Returns:
            Dict[str, Any]: Design results for the pipe
        """
        # Get pipe details
        pipe = self.repository.pipes.get_by_id(pipe_id)
        if pipe is None:
            raise NotFoundError(
                code="PIPE_NOT_FOUND", detail=f"Pipe {pipe_id} not found"
            )

        # Prepare calculation inputs
        pipe_diameter = (
            pipe.outer_diameter_mm or 100.0
        ) / 1000.0  # Convert to meters, default if None
        calc_input = HeatLossCalculationInputSchema(
            pipe_diameter=pipe_diameter,
            pipe_length=pipe.length_m,
            fluid_temperature=design_input.design_parameters.get(
                "fluid_temperature", 60.0
            ),
            ambient_temperature=design_input.design_parameters.get(
                "ambient_temperature", -10.0
            ),
            insulation_thickness=pipe.insulation_thickness_mm
            / 1000.0,  # Convert to meters
            insulation_type="standard",
            wind_speed=design_input.design_parameters.get("wind_speed", 0.0),
            pipe_material="carbon_steel",
        )

        # Perform calculations
        calc_result = self._perform_heat_loss_calculation(calc_input)

        # Update pipe with results
        self.repository.pipes.update_heat_loss_calculation(
            pipe_id=pipe_id,
            heat_loss_wm=calc_result.heat_loss_rate,
            required_output_wm=calc_result.required_power / pipe.length_m,
        )

        # Perform standards validation
        validation_result = self._perform_standards_validation(
            StandardsValidationInputSchema(
                heat_loss_result=calc_result,
                design_parameters=design_input.design_parameters,
                project_standards=design_input.standards_context.project_standards,
                hazardous_area_zone=design_input.design_parameters.get(
                    "hazardous_area_zone"
                ),
                gas_group=design_input.design_parameters.get("gas_group"),
                temperature_class=design_input.design_parameters.get(
                    "temperature_class"
                ),
            )
        )

        # Get updated pipe
        updated_pipe = self.repository.pipes.get_by_id(pipe_id)

        return {
            "pipe": PipeReadSchema.model_validate(updated_pipe),
            "calculations": [calc_result],
            "validations": [validation_result],
            "circuits": [],  # Circuit creation would be implemented here
        }

    def _design_vessel_heat_tracing(
        self, vessel_id: int, design_input: HeatTracingDesignInputSchema
    ) -> Dict[str, Any]:
        """
        Design heat tracing for a single vessel.

        Args:
            vessel_id: Vessel ID
            design_input: Design inputs

        Returns:
            Dict[str, Any]: Design results for the vessel
        """
        # Get vessel details
        vessel = self.repository.vessels.get_by_id(vessel_id)
        if vessel is None:
            raise NotFoundError(
                code="VESSEL_NOT_FOUND", detail=f"Vessel {vessel_id} not found"
            )

        # Simplified vessel heat loss calculation
        temp_diff = design_input.design_parameters.get(
            "fluid_temperature", 60.0
        ) - design_input.design_parameters.get("ambient_temperature", -10.0)
        heat_loss_w = (
            vessel.surface_area_m2
            * 50.0
            * temp_diff
            / (vessel.insulation_thickness_mm / 1000.0)
        )  # Simplified
        required_output_w = heat_loss_w * 1.25  # 25% safety factor

        # Update vessel with results
        self.repository.vessels.update_heat_loss_calculation(
            vessel_id=vessel_id,
            heat_loss_w=heat_loss_w,
            required_output_w=required_output_w,
        )

        # Create simplified calculation result
        calc_result = HeatLossCalculationResultSchema(
            heat_loss_rate=heat_loss_w / vessel.surface_area_m2,  # W/m²
            total_heat_loss=heat_loss_w,
            surface_temperature=design_input.design_parameters.get(
                "ambient_temperature", -10.0
            )
            + 15.0,
            required_power=required_output_w,
            safety_factor=1.25,
            calculation_metadata={
                "vessel_type": "simplified",
                "surface_area": vessel.surface_area_m2,
            },
        )

        # Perform standards validation
        validation_result = self._perform_standards_validation(
            StandardsValidationInputSchema(
                heat_loss_result=calc_result,
                design_parameters=design_input.design_parameters,
                project_standards=design_input.standards_context.project_standards,
                hazardous_area_zone=design_input.design_parameters.get(
                    "hazardous_area_zone"
                ),
                gas_group=design_input.design_parameters.get("gas_group"),
                temperature_class=design_input.design_parameters.get(
                    "temperature_class"
                ),
            )
        )

        # Get updated vessel
        updated_vessel = self.repository.vessels.get_by_id(vessel_id)

        return {
            "vessel": VesselReadSchema.model_validate(updated_vessel),
            "calculations": [calc_result],
            "validations": [validation_result],
            "circuits": [],  # Circuit creation would be implemented here
        }

    def _generate_design_summary(
        self,
        designed_pipes: List[PipeReadSchema],
        designed_vessels: List[VesselReadSchema],
        created_circuits: List[HTCircuitReadSchema],
        calculation_results: List[HeatLossCalculationResultSchema],
    ) -> Dict[str, Any]:
        """
        Generate design summary statistics.

        Args:
            designed_pipes: List of designed pipes
            designed_vessels: List of designed vessels
            created_circuits: List of created circuits
            calculation_results: List of calculation results

        Returns:
            Dict[str, Any]: Design summary
        """
        total_pipe_length = sum(pipe.length_m for pipe in designed_pipes)
        total_vessel_area = sum(vessel.surface_area_m2 for vessel in designed_vessels)
        total_power_requirement = sum(
            calc.required_power for calc in calculation_results
        )

        return {
            "total_pipes_designed": len(designed_pipes),
            "total_vessels_designed": len(designed_vessels),
            "total_circuits_created": len(created_circuits),
            "total_pipe_length_m": total_pipe_length,
            "total_vessel_area_m2": total_vessel_area,
            "total_power_requirement_w": total_power_requirement,
            "average_heat_loss_rate_wm": sum(
                calc.heat_loss_rate for calc in calculation_results
            )
            / len(calculation_results)
            if calculation_results
            else 0,
            "design_completion_timestamp": "2024-01-15T10:30:00Z",  # Would use actual timestamp
        }
