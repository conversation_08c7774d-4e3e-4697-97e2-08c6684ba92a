# backend/core/calculations/electrical_sizing/voltage_drop.py
"""
Voltage Drop Calculations.

This module implements voltage drop calculations for electrical circuits
in heat tracing applications.
"""

import logging
import math
from typing import Dict, Any

from backend.core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


def calculate_voltage_drop(
    current: float,
    length: float,
    cable_resistance: float,
    power_factor: float = 1.0,
    cable_reactance: float = 0.0
) -> float:
    """
    Calculate voltage drop in a cable.
    
    Args:
        current: Current flowing through cable (A)
        length: Cable length (m)
        cable_resistance: Cable resistance per unit length (Ohm/m)
        power_factor: Power factor (default 1.0 for resistive loads)
        cable_reactance: Cable reactance per unit length (Ohm/m)
        
    Returns:
        float: Voltage drop (V)
        
    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating voltage drop: I={current}A, L={length}m")
    
    try:
        # Validate inputs
        if current < 0:
            raise InvalidInputError("Current cannot be negative")
        
        if length <= 0:
            raise InvalidInputError("Cable length must be positive")
        
        if cable_resistance < 0:
            raise InvalidInputError("Cable resistance cannot be negative")
        
        if not 0 <= power_factor <= 1:
            raise InvalidInputError("Power factor must be between 0 and 1")
        
        # Calculate total resistance and reactance
        total_resistance = cable_resistance * length
        total_reactance = cable_reactance * length
        
        # Calculate voltage drop
        if cable_reactance == 0:
            # Simple resistive case
            voltage_drop = current * total_resistance
        else:
            # AC case with reactance
            # V_drop = I * sqrt((R*cos(φ) + X*sin(φ))²)
            # For simplicity, assuming resistive load (sin(φ) ≈ 0)
            voltage_drop = current * math.sqrt(
                (total_resistance * power_factor) ** 2 + 
                (total_reactance * math.sqrt(1 - power_factor ** 2)) ** 2
            )
        
        logger.debug(f"Calculated voltage drop: {voltage_drop:.2f}V")
        return voltage_drop
        
    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Voltage drop calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Voltage drop calculation failed: {str(e)}")


def calculate_voltage_drop_percentage(
    current: float,
    length: float,
    cable_resistance: float,
    supply_voltage: float,
    power_factor: float = 1.0
) -> float:
    """
    Calculate voltage drop as percentage of supply voltage.
    
    Args:
        current: Current flowing through cable (A)
        length: Cable length (m)
        cable_resistance: Cable resistance per unit length (Ohm/m)
        supply_voltage: Supply voltage (V)
        power_factor: Power factor (default 1.0)
        
    Returns:
        float: Voltage drop percentage (%)
        
    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    if supply_voltage <= 0:
        raise InvalidInputError("Supply voltage must be positive")
    
    voltage_drop = calculate_voltage_drop(current, length, cable_resistance, power_factor)
    voltage_drop_percent = (voltage_drop / supply_voltage) * 100
    
    logger.debug(f"Voltage drop percentage: {voltage_drop_percent:.1f}%")
    return voltage_drop_percent


def calculate_maximum_cable_length(
    current: float,
    cable_resistance: float,
    supply_voltage: float,
    max_voltage_drop_percent: float = 5.0,
    power_factor: float = 1.0
) -> float:
    """
    Calculate maximum cable length for given voltage drop limit.
    
    Args:
        current: Current flowing through cable (A)
        cable_resistance: Cable resistance per unit length (Ohm/m)
        supply_voltage: Supply voltage (V)
        max_voltage_drop_percent: Maximum allowed voltage drop (%)
        power_factor: Power factor (default 1.0)
        
    Returns:
        float: Maximum cable length (m)
        
    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating max length for {max_voltage_drop_percent}% voltage drop")
    
    try:
        # Validate inputs
        if current <= 0:
            raise InvalidInputError("Current must be positive")
        
        if cable_resistance <= 0:
            raise InvalidInputError("Cable resistance must be positive")
        
        if supply_voltage <= 0:
            raise InvalidInputError("Supply voltage must be positive")
        
        if max_voltage_drop_percent <= 0 or max_voltage_drop_percent > 50:
            raise InvalidInputError("Maximum voltage drop percentage must be between 0 and 50%")
        
        # Calculate maximum allowable voltage drop
        max_voltage_drop = supply_voltage * (max_voltage_drop_percent / 100)
        
        # Calculate maximum length
        # V_drop = I * R * L, so L = V_drop / (I * R)
        max_length = max_voltage_drop / (current * cable_resistance * power_factor)
        
        logger.debug(f"Maximum cable length: {max_length:.1f}m")
        return max_length
        
    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Maximum length calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Maximum length calculation failed: {str(e)}")


def calculate_required_cable_size(
    current: float,
    length: float,
    supply_voltage: float,
    max_voltage_drop_percent: float = 5.0,
    power_factor: float = 1.0
) -> Dict[str, Any]:
    """
    Calculate required cable size (resistance) for voltage drop limit.
    
    Args:
        current: Current flowing through cable (A)
        length: Cable length (m)
        supply_voltage: Supply voltage (V)
        max_voltage_drop_percent: Maximum allowed voltage drop (%)
        power_factor: Power factor (default 1.0)
        
    Returns:
        Dict with cable sizing results
        
    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating required cable size for {length}m length")
    
    try:
        # Validate inputs
        if current <= 0:
            raise InvalidInputError("Current must be positive")
        
        if length <= 0:
            raise InvalidInputError("Cable length must be positive")
        
        if supply_voltage <= 0:
            raise InvalidInputError("Supply voltage must be positive")
        
        # Calculate maximum allowable voltage drop
        max_voltage_drop = supply_voltage * (max_voltage_drop_percent / 100)
        
        # Calculate maximum allowable resistance per unit length
        # V_drop = I * R * L, so R = V_drop / (I * L)
        max_resistance_per_meter = max_voltage_drop / (current * length * power_factor)
        
        # Calculate total resistance
        max_total_resistance = max_resistance_per_meter * length
        
        result = {
            "max_resistance_per_meter": max_resistance_per_meter,
            "max_total_resistance": max_total_resistance,
            "max_voltage_drop": max_voltage_drop,
            "max_voltage_drop_percent": max_voltage_drop_percent,
            "design_current": current,
            "cable_length": length
        }
        
        logger.debug(f"Required cable resistance: ≤{max_resistance_per_meter:.4f} Ohm/m")
        return result
        
    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Cable sizing calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Cable sizing calculation failed: {str(e)}")


def validate_voltage_drop_compliance(
    current: float,
    length: float,
    cable_resistance: float,
    supply_voltage: float,
    max_voltage_drop_percent: float = 5.0
) -> Dict[str, Any]:
    """
    Validate if cable meets voltage drop requirements.
    
    Args:
        current: Current flowing through cable (A)
        length: Cable length (m)
        cable_resistance: Cable resistance per unit length (Ohm/m)
        supply_voltage: Supply voltage (V)
        max_voltage_drop_percent: Maximum allowed voltage drop (%)
        
    Returns:
        Dict with compliance results
    """
    voltage_drop = calculate_voltage_drop(current, length, cable_resistance)
    voltage_drop_percent = calculate_voltage_drop_percentage(
        current, length, cable_resistance, supply_voltage
    )
    
    is_compliant = voltage_drop_percent <= max_voltage_drop_percent
    
    result = {
        "is_compliant": is_compliant,
        "voltage_drop": voltage_drop,
        "voltage_drop_percent": voltage_drop_percent,
        "max_allowed_percent": max_voltage_drop_percent,
        "margin_percent": max_voltage_drop_percent - voltage_drop_percent,
        "supply_voltage": supply_voltage,
        "cable_resistance": cable_resistance,
        "cable_length": length,
        "current": current
    }
    
    logger.debug(f"Voltage drop compliance: {is_compliant} ({voltage_drop_percent:.1f}%)")
    return result
