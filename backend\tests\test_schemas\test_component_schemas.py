# backend/tests/test_schemas/test_component_schemas.py
"""
Tests for Component and ComponentCategory Pydantic schemas.

This module tests the validation logic and serialization behavior
of component-related schemas.
"""

import pytest
from pydantic import ValidationError

from backend.core.schemas.component_schemas import (
    ComponentCreateSchema,
    ComponentUpdateSchema,
    ComponentReadSchema,
    ComponentSummarySchema,
    ComponentListResponseSchema,
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategoryReadSchema,
    ComponentCategoryListResponseSchema
)


class TestComponentCreateSchema:
    """Test ComponentCreateSchema validation."""

    def test_valid_component_creation(self):
        """Test valid component creation data."""
        data = {
            "name": "Circuit Breaker 20A",
            "category_id": 1,
            "manufacturer": "Schneider Electric",
            "model": "QO120",
            "specific_data": '{"rating_amps": 20, "voltage": 120, "poles": 1}',
            "notes": "Standard single-pole circuit breaker"
        }
        
        schema = ComponentCreateSchema(**data)
        
        assert schema.name == "Circuit Breaker 20A"
        assert schema.category_id == 1
        assert schema.manufacturer == "Schneider Electric"
        assert schema.model == "QO120"
        assert schema.specific_data == '{"rating_amps": 20, "voltage": 120, "poles": 1}'
        assert schema.notes == "Standard single-pole circuit breaker"

    def test_minimal_component_creation(self):
        """Test component creation with minimal required fields."""
        data = {
            "name": "Basic Component",
            "category_id": 1
        }
        
        schema = ComponentCreateSchema(**data)
        
        assert schema.name == "Basic Component"
        assert schema.category_id == 1
        assert schema.manufacturer is None
        assert schema.model is None
        assert schema.specific_data is None
        assert schema.notes is None

    def test_name_validation_empty(self):
        """Test validation fails for empty component name."""
        data = {
            "name": "",
            "category_id": 1
        }
        with pytest.raises(ValidationError) as exc_info:
            ComponentCreateSchema(**data)

        assert "String should have at least 3 characters" in str(exc_info.value)

    def test_name_validation_whitespace(self):
        """Test validation fails for whitespace-only component name."""
        data = {
            "name": "   ",
            "category_id": 1
        }
        with pytest.raises(ValidationError) as exc_info:
            ComponentCreateSchema(**data)

        assert "Component name cannot be empty" in str(exc_info.value)

    def test_name_normalization(self):
        """Test component name whitespace normalization."""
        data = {
            "name": "  Circuit   Breaker  20A  ",
            "category_id": 1
        }
        
        schema = ComponentCreateSchema(**data)
        assert schema.name == "Circuit Breaker 20A"

    def test_category_id_validation(self):
        """Test category_id is required and must be positive."""
        # Missing category_id
        with pytest.raises(ValidationError) as exc_info:
            ComponentCreateSchema(name="Test Component")
        assert "Field required" in str(exc_info.value)

        # Zero category_id should be allowed (validation happens at service level)
        data = {
            "name": "Test Component",
            "category_id": 0
        }
        schema = ComponentCreateSchema(**data)
        assert schema.category_id == 0

    def test_specific_data_json_validation_valid(self):
        """Test valid JSON in specific_data field."""
        data = {
            "name": "Test Component",
            "category_id": 1,
            "specific_data": '{"key": "value", "number": 42, "array": [1, 2, 3]}'
        }
        
        schema = ComponentCreateSchema(**data)
        assert schema.specific_data == '{"key": "value", "number": 42, "array": [1, 2, 3]}'

    def test_specific_data_json_validation_invalid(self):
        """Test invalid JSON in specific_data field."""
        data = {
            "name": "Test Component",
            "category_id": 1,
            "specific_data": '{"invalid": json}'
        }
        
        with pytest.raises(ValidationError) as exc_info:
            ComponentCreateSchema(**data)
        assert "specific_data must be valid JSON" in str(exc_info.value)

    def test_specific_data_empty_string(self):
        """Test empty string in specific_data field is converted to None."""
        data = {
            "name": "Test Component",
            "category_id": 1,
            "specific_data": ""
        }
        
        schema = ComponentCreateSchema(**data)
        assert schema.specific_data is None

    def test_specific_data_whitespace_only(self):
        """Test whitespace-only string in specific_data field is converted to None."""
        data = {
            "name": "Test Component",
            "category_id": 1,
            "specific_data": "   "
        }
        
        schema = ComponentCreateSchema(**data)
        assert schema.specific_data is None


class TestComponentUpdateSchema:
    """Test ComponentUpdateSchema validation."""

    def test_valid_partial_update(self):
        """Test valid partial update with some fields."""
        data = {
            "name": "Updated Circuit Breaker",
            "manufacturer": "Updated Manufacturer"
        }
        
        schema = ComponentUpdateSchema(**data)
        
        assert schema.name == "Updated Circuit Breaker"
        assert schema.manufacturer == "Updated Manufacturer"
        assert schema.category_id is None
        assert schema.model is None
        assert schema.specific_data is None
        assert schema.notes is None

    def test_empty_update(self):
        """Test empty update (no fields provided)."""
        schema = ComponentUpdateSchema()
        
        # All fields should be None
        assert schema.name is None
        assert schema.category_id is None
        assert schema.manufacturer is None
        assert schema.model is None
        assert schema.specific_data is None
        assert schema.notes is None

    def test_update_validation_same_as_create(self):
        """Test that update validation uses same rules as create."""
        # Test invalid name
        with pytest.raises(ValidationError):
            ComponentUpdateSchema(name="")

        # Test invalid specific_data
        with pytest.raises(ValidationError):
            ComponentUpdateSchema(specific_data='{"invalid": json}')


class TestComponentCategoryCreateSchema:
    """Test ComponentCategoryCreateSchema validation."""

    def test_valid_category_creation(self):
        """Test valid category creation data."""
        data = {
            "name": "Electrical Components",
            "description": "Components for electrical systems",
            "parent_category_id": None
        }
        
        schema = ComponentCategoryCreateSchema(**data)
        
        assert schema.name == "Electrical Components"
        assert schema.description == "Components for electrical systems"
        assert schema.parent_category_id is None

    def test_minimal_category_creation(self):
        """Test category creation with minimal required fields."""
        data = {
            "name": "Basic Category"
        }
        
        schema = ComponentCategoryCreateSchema(**data)
        
        assert schema.name == "Basic Category"
        assert schema.description is None
        assert schema.parent_category_id is None

    def test_category_with_parent(self):
        """Test category creation with parent category."""
        data = {
            "name": "Subcategory",
            "description": "A subcategory",
            "parent_category_id": 1
        }
        
        schema = ComponentCategoryCreateSchema(**data)
        
        assert schema.name == "Subcategory"
        assert schema.description == "A subcategory"
        assert schema.parent_category_id == 1

    def test_category_name_validation_empty(self):
        """Test validation fails for empty category name."""
        data = {
            "name": ""
        }
        with pytest.raises(ValidationError) as exc_info:
            ComponentCategoryCreateSchema(**data)

        assert "String should have at least 3 characters" in str(exc_info.value)

    def test_category_name_validation_whitespace(self):
        """Test validation fails for whitespace-only category name."""
        data = {
            "name": "   "
        }
        with pytest.raises(ValidationError) as exc_info:
            ComponentCategoryCreateSchema(**data)

        assert "Category name cannot be empty" in str(exc_info.value)

    def test_category_name_normalization(self):
        """Test category name whitespace normalization."""
        data = {
            "name": "  Electrical   Components  "
        }
        
        schema = ComponentCategoryCreateSchema(**data)
        assert schema.name == "Electrical Components"


class TestComponentCategoryUpdateSchema:
    """Test ComponentCategoryUpdateSchema validation."""

    def test_valid_partial_category_update(self):
        """Test valid partial category update."""
        data = {
            "name": "Updated Category",
            "description": "Updated description"
        }
        
        schema = ComponentCategoryUpdateSchema(**data)
        
        assert schema.name == "Updated Category"
        assert schema.description == "Updated description"
        assert schema.parent_category_id is None

    def test_empty_category_update(self):
        """Test empty category update (no fields provided)."""
        schema = ComponentCategoryUpdateSchema()
        
        # All fields should be None
        assert schema.name is None
        assert schema.description is None
        assert schema.parent_category_id is None

    def test_category_update_validation_same_as_create(self):
        """Test that category update validation uses same rules as create."""
        # Test invalid name
        with pytest.raises(ValidationError):
            ComponentCategoryUpdateSchema(name="")


class TestComponentReadSchema:
    """Test ComponentReadSchema functionality."""

    def test_read_schema_includes_all_fields(self):
        """Test that read schema includes all expected fields."""
        # This would typically be tested with actual ORM objects
        # For now, we'll test the schema structure
        data = {
            "id": 1,
            "name": "Circuit Breaker 20A",
            "category_id": 1,
            "manufacturer": "Schneider Electric",
            "model": "QO120",
            "specific_data": '{"rating_amps": 20}',
            "notes": "Test notes",
            "created_at": "2024-01-15T10:30:00Z",
            "updated_at": "2024-01-15T10:30:00Z",
            "is_deleted": False,
            "deleted_at": None,
            "deleted_by_user_id": None
        }
        
        schema = ComponentReadSchema(**data)
        
        assert schema.id == 1
        assert schema.name == "Circuit Breaker 20A"
        assert schema.category_id == 1
        assert schema.manufacturer == "Schneider Electric"
        assert schema.model == "QO120"
        assert schema.specific_data == '{"rating_amps": 20}'
        assert schema.notes == "Test notes"
        assert schema.is_deleted is False


class TestComponentListResponseSchema:
    """Test ComponentListResponseSchema functionality."""

    def test_list_response_structure(self):
        """Test the structure of component list response."""
        data = {
            "components": [
                {
                    "id": 1,
                    "name": "Circuit Breaker 20A",
                    "category_id": 1,
                    "manufacturer": "Schneider Electric",
                    "model": "QO120"
                }
            ],
            "total": 1,
            "page": 1,
            "per_page": 10,
            "total_pages": 1
        }
        
        schema = ComponentListResponseSchema(**data)
        
        assert len(schema.components) == 1
        assert schema.components[0].name == "Circuit Breaker 20A"
        assert schema.total == 1
        assert schema.page == 1
        assert schema.per_page == 10
        assert schema.total_pages == 1

    def test_empty_list_response(self):
        """Test empty component list response."""
        data = {
            "components": [],
            "total": 0,
            "page": 1,
            "per_page": 10,
            "total_pages": 1
        }
        
        schema = ComponentListResponseSchema(**data)
        
        assert len(schema.components) == 0
        assert schema.total == 0
