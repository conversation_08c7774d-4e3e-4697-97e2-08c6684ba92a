from sqlalchemy import <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Text, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, SoftDeleteColumns
from .heat_tracing import Pipe, Vessel
from .project import Project
from .users import User


class ImportedDataRevision(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "ImportedDataRevision"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    imported_by_user_id: Mapped[int | None] = mapped_column(
        ForeignKey("User.id"), nullable=True
    )
    source_filename: Mapped[str] = mapped_column(nullable=False)
    revision_identifier: Mapped[str | None] = mapped_column(nullable=True)
    import_type: Mapped[str] = mapped_column(nullable=False)
    is_active_revision: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False
    )

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="imported_data_revisions")
    imported_by_user: Mapped["User | None"] = relationship(
        back_populates="imported_data_revisions", foreign_keys=[imported_by_user_id]
    )
    pipes: Mapped[list["Pipe"]] = relationship(back_populates="imported_revision")
    vessels: Mapped[list["Vessel"]] = relationship(back_populates="imported_revision")

    __table_args__ = (
        Index(
            "uq_active_import_revision",  # Name first for Index
            "project_id",
            "source_filename",
            "is_active_revision",
            unique=True,  # Specify unique=True for Index
            postgresql_where=Boolean(True),
        ),
    )

    def __repr__(self):
        return f"<ImportedDataRevision(id={self.id}, project_id={self.project_id}, filename='{self.source_filename}', revision='{self.revision_identifier}', active={self.is_active_revision})>"


class ExportedDocument(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "ExportedDocument"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    generated_by_user_id: Mapped[int | None] = mapped_column(
        ForeignKey("User.id"), nullable=True
    )

    document_type: Mapped[str] = mapped_column(nullable=False)
    filename: Mapped[str] = mapped_column(nullable=False)
    revision: Mapped[str | None] = mapped_column(nullable=True)
    file_path_or_url: Mapped[str | None] = mapped_column(nullable=True)
    is_latest_revision: Mapped[bool] = mapped_column(
        Boolean, default=True, nullable=False
    )

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="exported_documents")
    generated_by_user: Mapped["User | None"] = relationship(
        back_populates="exported_documents", foreign_keys=[generated_by_user_id]
    )

    __table_args__ = (
        Index(
            "uq_latest_export_doc",  # Name first for Index
            "project_id",
            "document_type",
            "name",
            "is_latest_revision",
            unique=True,  # Specify unique=True for Index
            postgresql_where=Boolean(True),
        ),
    )

    def __repr__(self):
        return (
            f"<ExportedDocument(id={self.id}, proj_id={self.project_id}, doc_type='{self.document_type}', "
            f"name='{self.name}', rev='{self.revision}', latest={self.is_latest_revision})>"
        )


class CalculationStandard(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "CalculationStandard"  # ADD THIS LINE

    standard_code: Mapped[str] = mapped_column(unique=True, nullable=False)
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    parameters_json: Mapped[str | None] = mapped_column(Text, nullable=True)

    def __repr__(self):
        return f"<CalculationStandard(id={self.id}, name='{self.name}', code='{self.standard_code}')>"
