# backend/tests/test_activity_log_repository.py
"""
Tests for Activity Log Repository.

This module tests the data access operations for activity logs including
CRUD operations, filtering, querying, and audit trail functionality.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

from core.models.activity_log import ActivityLog
from core.repositories.activity_log_repository import ActivityLogRepository
from core.schemas.activity_log_schemas import (
    ActivityLogFilterSchema,
    EventTypeEnum,
    EntityTypeEnum,
)


class TestActivityLogRepository:
    """Test ActivityLogRepository functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Create a mock database session."""
        return Mock()

    @pytest.fixture
    def activity_log_repo(self, mock_db_session):
        """Create an ActivityLogRepository instance."""
        return ActivityLogRepository(mock_db_session)

    @pytest.fixture
    def sample_activity_log(self):
        """Create a sample activity log for testing."""
        return ActivityLog(
            id=1,
            timestamp=datetime.now(),
            user_id=1,
            event_type=EventTypeEnum.CREATE.value,
            entity_type=EntityTypeEnum.PROJECT.value,
            entity_id=123,
            details="Created new project",
        )

    def test_repository_initialization(self, mock_db_session):
        """Test repository initialization."""
        repo = ActivityLogRepository(mock_db_session)
        assert repo.db_session == mock_db_session
        assert repo.model == ActivityLog

    def test_get_by_user_id(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test getting activity logs by user ID."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        result = activity_log_repo.get_by_user_id(user_id=1, skip=0, limit=10)

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_get_by_entity(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test getting activity logs by entity."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        result = activity_log_repo.get_by_entity(
            entity_type="Project", entity_id=123, skip=0, limit=10
        )

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_get_by_event_type(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test getting activity logs by event type."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        result = activity_log_repo.get_by_event_type(
            event_type="CREATE", skip=0, limit=10
        )

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_get_by_date_range(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test getting activity logs by date range."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        start_date = datetime.now() - timedelta(days=7)
        end_date = datetime.now()
        result = activity_log_repo.get_by_date_range(
            start_date=start_date, end_date=end_date, skip=0, limit=10
        )

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_search_by_details(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test searching activity logs by details."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        result = activity_log_repo.search_by_details(
            search_term="project", skip=0, limit=10
        )

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_filter_activity_logs(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test filtering activity logs with complex criteria."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Create filter criteria
        filters = ActivityLogFilterSchema(
            user_id=1,
            event_type=EventTypeEnum.CREATE,
            entity_type=EntityTypeEnum.PROJECT,
            entity_id=123,
            start_date=datetime.now() - timedelta(days=7),
            end_date=datetime.now(),
            event_category=None,
            search_details="project",
        )

        # Test the method
        result = activity_log_repo.filter_activity_logs(filters, skip=0, limit=10)

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_count_filtered_activity_logs(self, activity_log_repo, mock_db_session):
        """Test counting filtered activity logs."""
        # Mock the database query
        mock_db_session.scalar.return_value = 5

        # Create filter criteria
        filters = ActivityLogFilterSchema(
            user_id=1,
            event_type=EventTypeEnum.CREATE,
            entity_type=None,
            entity_id=None,
            start_date=None,
            end_date=None,
            event_category=None,
            search_details=None,
        )

        # Test the method
        result = activity_log_repo.count_filtered_activity_logs(filters)

        # Verify results
        assert result == 5
        mock_db_session.scalar.assert_called_once()

    def test_get_security_events(self, activity_log_repo, mock_db_session):
        """Test getting security events."""
        # Create a security event
        security_event = ActivityLog(
            id=2,
            timestamp=datetime.now(),
            user_id=1,
            event_type=EventTypeEnum.LOGIN_FAILED.value,
            entity_type=EntityTypeEnum.USER.value,
            entity_id=1,
            details="Failed login attempt",
        )

        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [security_event]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        start_date = datetime.now() - timedelta(days=7)
        end_date = datetime.now()
        result = activity_log_repo.get_security_events(
            start_date=start_date, end_date=end_date, skip=0, limit=10
        )

        # Verify results
        assert len(result) == 1
        assert result[0] == security_event
        mock_db_session.scalars.assert_called_once()

    def test_get_user_activity_summary(self, activity_log_repo, mock_db_session):
        """Test getting user activity summary."""
        # Mock the database queries
        mock_db_session.scalar.return_value = 10  # Total events
        mock_db_session.execute.return_value.all.side_effect = [
            [("CREATE", 5), ("UPDATE", 3), ("DELETE", 2)],  # Event type counts
            [("Project", 7), ("Component", 3)],  # Entity type counts
        ]

        # Test the method
        user_id = 1
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        result = activity_log_repo.get_user_activity_summary(
            user_id=user_id, start_date=start_date, end_date=end_date
        )

        # Verify results
        assert result["user_id"] == user_id
        assert result["total_events"] == 10
        assert result["event_type_counts"] == {"CREATE": 5, "UPDATE": 3, "DELETE": 2}
        assert result["entity_type_counts"] == {"Project": 7, "Component": 3}

    def test_get_daily_activity_counts(self, activity_log_repo, mock_db_session):
        """Test getting daily activity counts."""
        # Mock the database query
        from datetime import date

        mock_db_session.execute.return_value.all.return_value = [
            (date(2024, 1, 15), 10),
            (date(2024, 1, 16), 15),
            (date(2024, 1, 17), 8),
        ]

        # Test the method
        start_date = datetime(2024, 1, 15)
        end_date = datetime(2024, 1, 17)
        result = activity_log_repo.get_daily_activity_counts(
            start_date=start_date, end_date=end_date
        )

        # Verify results
        expected = {
            "2024-01-15": 10,
            "2024-01-16": 15,
            "2024-01-17": 8,
        }
        assert result == expected

    def test_get_recent_activity(
        self, activity_log_repo, mock_db_session, sample_activity_log
    ):
        """Test getting recent activity logs."""
        # Mock the database query
        mock_scalars = Mock()
        mock_scalars.all.return_value = [sample_activity_log]
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        result = activity_log_repo.get_recent_activity(limit=50)

        # Verify results
        assert len(result) == 1
        assert result[0] == sample_activity_log
        mock_db_session.scalars.assert_called_once()

    def test_delete_old_logs(self, activity_log_repo, mock_db_session):
        """Test deleting old activity logs."""
        # Mock the database queries
        mock_db_session.scalar.return_value = 5  # Count to delete
        mock_execute_result = Mock()
        mock_execute_result.rowcount = 5
        mock_db_session.execute.return_value = mock_execute_result

        # Test the method
        cutoff_date = datetime.now() - timedelta(days=365)
        result = activity_log_repo.delete_old_logs(cutoff_date)

        # Verify results
        assert result == 5
        mock_db_session.execute.assert_called()

    def test_get_unique_users_count(self, activity_log_repo, mock_db_session):
        """Test getting unique users count."""
        # Mock the database query
        mock_db_session.scalar.return_value = 3

        # Test the method
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        result = activity_log_repo.get_unique_users_count(
            start_date=start_date, end_date=end_date
        )

        # Verify results
        assert result == 3
        mock_db_session.scalar.assert_called_once()

    def test_get_unique_entities_count(self, activity_log_repo, mock_db_session):
        """Test getting unique entities count."""
        # Mock the database query
        mock_db_session.scalar.return_value = 15

        # Test the method
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        result = activity_log_repo.get_unique_entities_count(
            start_date=start_date, end_date=end_date
        )

        # Verify results
        assert result == 15
        mock_db_session.scalar.assert_called_once()

    @patch("core.repositories.activity_log_repository.logger")
    def test_database_error_handling(
        self, mock_logger, activity_log_repo, mock_db_session
    ):
        """Test database error handling."""
        from sqlalchemy.exc import SQLAlchemyError

        # Mock database error
        mock_db_session.scalars.side_effect = SQLAlchemyError(
            "Database connection failed"
        )

        # Test that error is handled and logged
        with pytest.raises(Exception):  # Should re-raise after handling
            activity_log_repo.get_by_user_id(user_id=1)

        # Verify error was logged
        mock_logger.error.assert_called()

    def test_empty_results(self, activity_log_repo, mock_db_session):
        """Test handling of empty query results."""
        # Mock empty results
        mock_scalars = Mock()
        mock_scalars.all.return_value = []
        mock_db_session.scalars.return_value = mock_scalars

        # Test the method
        result = activity_log_repo.get_by_user_id(user_id=999)

        # Verify empty results are handled correctly
        assert result == []
        assert len(result) == 0
