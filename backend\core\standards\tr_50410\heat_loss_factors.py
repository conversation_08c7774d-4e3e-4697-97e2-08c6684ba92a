# backend/core/standards/tr_50410/heat_loss_factors.py
"""
TR 50410 Heat Loss Factors and Validation.

This module implements TR 50410 specific requirements for heat loss calculations
and safety factors.
"""

import logging
from typing import Dict, Any

from backend.core.standards.standards_manager import ValidationResult

logger = logging.getLogger(__name__)

# TR 50410 safety factors
TR_50410_SAFETY_FACTORS = {
    "power_safety_factor": 1.2,  # 20% safety margin for power calculations
    "heat_loss_safety_factor": 1.1,  # 10% safety margin for heat loss
    "temperature_safety_margin": 5.0,  # 5°C safety margin for temperatures
}

# TR 50410 limits and requirements
TR_50410_LIMITS = {
    "max_surface_temperature": 85.0,  # °C for general applications
    "max_power_density": 50.0,  # W/m² for standard installations
    "min_insulation_thickness": 0.025,  # 25mm minimum insulation
    "max_voltage_drop_percent": 5.0,  # 5% maximum voltage drop
}


def validate_tr_50410_heat_loss(
    heat_loss_result: Dict[str, Any],
    design_parameters: Dict[str, Any]
) -> ValidationResult:
    """
    Validate heat loss calculation against TR 50410 requirements.
    
    Args:
        heat_loss_result: Results from heat loss calculation
        design_parameters: Design parameters used
        
    Returns:
        ValidationResult: Validation results
    """
    logger.debug("Validating against TR 50410 heat loss requirements")
    
    violations = []
    warnings = []
    applied_factors = {}
    
    # Check surface temperature limits
    surface_temp = heat_loss_result.get("surface_temperature", 0)
    if surface_temp > TR_50410_LIMITS["max_surface_temperature"]:
        violations.append(
            f"Surface temperature {surface_temp:.1f}°C exceeds TR 50410 limit "
            f"of {TR_50410_LIMITS['max_surface_temperature']}°C"
        )
    elif surface_temp > TR_50410_LIMITS["max_surface_temperature"] - TR_50410_SAFETY_FACTORS["temperature_safety_margin"]:
        warnings.append(
            f"Surface temperature {surface_temp:.1f}°C is close to TR 50410 limit "
            f"({TR_50410_LIMITS['max_surface_temperature']}°C)"
        )
    
    # Check insulation thickness
    insulation_thickness = design_parameters.get("insulation_thickness", 0)
    if insulation_thickness < TR_50410_LIMITS["min_insulation_thickness"]:
        violations.append(
            f"Insulation thickness {insulation_thickness*1000:.0f}mm is below TR 50410 minimum "
            f"of {TR_50410_LIMITS['min_insulation_thickness']*1000:.0f}mm"
        )
    
    # Check power density if available
    power_density = heat_loss_result.get("power_density")
    if power_density and power_density > TR_50410_LIMITS["max_power_density"]:
        violations.append(
            f"Power density {power_density:.1f} W/m² exceeds TR 50410 limit "
            f"of {TR_50410_LIMITS['max_power_density']} W/m²"
        )
    
    # Apply TR 50410 safety factors
    applied_factors.update(TR_50410_SAFETY_FACTORS)
    
    return ValidationResult(
        is_compliant=len(violations) == 0,
        standard="TR 50410",
        violations=violations,
        warnings=warnings,
        applied_factors=applied_factors,
        metadata={
            "standard_version": "TR 50410:2019",
            "validation_scope": "heat_loss"
        }
    )
