### Report Generation Architecture Specification (Revised for Native Document Templates)

**1. Purpose & Role**
The Report Generation module is responsible for synthesizing application data, calculation results, and design decisions into various standardized reports. Its primary purpose is to:
*   **Create Technical Documentation:** Produce formal documentation (e.g., heat loss reports, cable sizing reports, compliance certificates) based on project data.
*   **Summarize Project Information:** Provide clear, concise summaries of project parameters, design choices, and calculated outcomes.
*   **Enable Data Export & Sharing:** Facilitate the output of application data into distributable formats (primarily PDF) using familiar native document templates.
*   **Ensure Consistency:** Utilize native document templates (DOCX, XLSX) to maintain a consistent professional look and feel.

**2. Location & Structure (`src/core/reports/`)**
The Report Generation layer will be organized within `src/core/reports/`. This structure emphasizes modularity by report type and separates concerns related to data preparation, document population, and final PDF output.

```
src/core/reports/
├── __init__.py
├── templates/                      # Directory for native report templates (*.docx, *.xlsx)
│   ├── circuit_heat_loss_template.docx
│   ├── cable_sizing_template.xlsx
│   └── compliance_summary_template.docx
├── data_preparators/               # Sub-package for report-specific data aggregation/transformation
│   ├── __init__.py
│   ├── heat_loss_data_preparator.py
│   ├── cable_sizing_data_preparator.py
│   └── compliance_data_preparator.py
├── document_populator/             # Sub-package for populating native templates
│   ├── __init__.py
│   ├── docx_populator.py           # Logic for populating DOCX files (e.g., using python-docx)
│   └── xlsx_populator.py           # Logic for populating XLSX files (e.g., using openpyxl)
├── pdf_converter/                  # Sub-package for converting populated documents to PDF
│   ├── __init__.py
│   ├── libreoffice_converter.py    # Example: using LibreOffice headless for conversion
│   └── converter_interface.py      # Abstract interface for different PDF conversion tools
├── circuit_reports/                # Sub-package for specific circuit-related reports
│   ├── __init__.py
│   ├── heat_loss_report.py         # Logic for heat loss report generation (orchestrates preparator, populator, converter)
│   ├── cable_sizing_report.py      # Logic for cable sizing report generation
│   └── standards_compliance_report.py # Logic for standards compliance report generation
└── report_manager.py               # Central interface for orchestrating report generation
```

**3. Core Responsibilities & Functionality**

*   **Data Aggregation & Transformation (`data_preparators/`):**
    *   **Gathering Data:** Functions responsible for fetching all necessary raw data from the [Service Layer](../../services/services-architecture.md), which in turn interacts with the [Repository Layer](../../repositories/repositories-architecture.md) (for project/entity data) and the [Calculations Layer](../calculations/calculations-architecture.md) (for calculation results).
    *   **Structuring Data:** Transforms the aggregated raw data into a structured format (e.g., dictionaries, lists of dictionaries) that can be easily mapped to the placeholders (e.g., mail merge fields, named ranges, specific cell locations) within the native document templates. This might involve summarization, unit conversions for display, or filtering.
*   **Document Population (`document_populator/`):**
    *   **Tool Integration:** Integrates with Python libraries capable of reading and modifying native Office documents.
        *   **`python-docx`:** For manipulating `.docx` files (replacing placeholders, populating tables, adding content).
        *   **`openpyxl`:** For manipulating `.xlsx` files (writing data to cells, populating sheets, managing named ranges).
    *   **Functionality:** Takes the path to a native template file and the prepared report data, then programmatically populates the document with the data. This produces a *populated* native document file (e.g., a `.docx` or `.xlsx` file).
    *   **Placeholder Strategy:** Templates will use specific placeholder formats (e.g., `{{FIELD_NAME}}` for `python-docx` using template engines like `docxtpl`, or named cells/ranges for `openpyxl`) that the populators can identify and replace.

    **Example (Populating .docx with python-docx - basic placeholder replacement):**
    ```python
    # Example in src/core/reports/document_populator/docx_populator.py
    from docx import Document # Using python-docx library
    # Note: For template variables like {{FIELD_NAME}}, a library like `docxtpl` is often more convenient
    # but python-docx can be used for simpler find/replace or table population.

    def populate_docx_report(template_path: str, data: dict, output_path: str):
        try:
            document = Document(template_path)

            # Simple placeholder replacement (requires finding runs containing text)
            # For more complex templating, consider docxtpl
            for p in document.paragraphs:
                for run in p.runs:
                    for key, value in data.items():
                        # Basic replacement: replace {{KEY}} with value
                        if f'{{'{key}'}}' in run.text:
                             run.text = run.text.replace(f'{{'{key}'}}', str(value))

            # Example: Populating a table (assumes a table exists with placeholders)
            # Iterate through tables and cells to find and replace placeholders

            document.save(output_path)

        except Exception as e:
            # Log and raise a specific report generation error
            raise ReportGenerationError(details=f"Failed to populate DOCX report: {e}")
    ```

    **Example (Populating .xlsx with openpyxl - cell and named range population):**
    ```python
    # Example in src/core/reports/document_populator/xlsx_populator.py
    from openpyxl import load_workbook
    from openpyxl.utils.cell import coordinate_from_string, column_index_from_string

    def populate_xlsx_report(template_path: str, data: dict, output_path: str):
        try:
            workbook = load_workbook(template_path)
            sheet = workbook.active # Or specify a sheet name

            # Populate data into specific cells
            if 'project_name' in data:
                sheet['B2'] = data['project_name'] # Assuming B2 is the project name cell
            if 'report_date' in data:
                sheet['C2'] = data['report_date']

            # Populate data into named ranges (more robust than cell addresses)
            if 'total_heat_loss' in data and 'TotalHeatLoss' in workbook.defined_names:
                 dest = workbook.defined_names['TotalHeatLoss'].destinations[0]
                 sheet_name, cell_coords = dest
                 workbook[sheet_name][cell_coords] = data['total_heat_loss']

            # Example: Populating a table (e.g., a list of circuits)
            # Assumes a starting row and structure, requires iterating data and writing rows
            # start_row = 10
            # for idx, circuit_data in enumerate(data.get('circuits', [])):
            #     sheet.cell(row=start_row + idx, column=1, value=circuit_data.get('name'))
            #     sheet.cell(row=start_row + idx, column=2, value=circuit_data.get('power'))
            #     ...

            workbook.save(output_path)

        except Exception as e:
            # Log and raise a specific report generation error
            raise ReportGenerationError(details=f"Failed to populate XLSX report: {e}")
    ```

*   **PDF Conversion (`pdf_converter/`):**
    *   **Tool Integration:** This component is responsible for taking the *populated native document* (DOCX/XLSX) and converting it into a PDF byte stream. This typically requires external system PDF software or libraries.
        *   **Recommended:** Utilizing a headless office suite like **LibreOffice** (via `subprocess` calls or a dedicated Python wrapper) is a robust and cross-platform solution for converting various Office formats to PDF.
        *   **Alternatives:** `Pandoc` (universal document converter), or commercial APIs/libraries if specific features or performance are critical.
    *   **Functionality:** Receives the path to the populated DOCX/XLSX file, invokes the external converter, and returns the resulting PDF as bytes.

    **Example (PDF Conversion using LibreOffice - Subprocess):**
    ```python
    # Example in src/core/reports/pdf_converter/libreoffice_converter.py
    import subprocess
    import sys
    import os

    def convert_office_to_pdf(input_path: str, output_dir: str) -> str:
        """Converts a DOCX or XLSX file to PDF using LibreOffice in headless mode."""
        # Ensure LibreOffice path is correctly configured or in system PATH
        # On Windows, this might be something like 'C:\Program Files\LibreOffice\program\soffice.exe'
        libreoffice_path = "soffice"

        # Determine the output path for the PDF
        input_filename = os.path.basename(input_path)
        output_filename = os.path.splitext(input_filename)[0] + ".pdf"
        output_path = os.path.join(output_dir, output_filename)

        try:
            # Command to convert using LibreOffice
            # - headless: run without GUI
            # - convert-to pdf: specify output format
            # - outdir: specify output directory
            # - <input_path>: the input file
            command = [
                libreoffice_path,
                "--headless",
                "--convert-to",
                "pdf",
                "--outdir",
                output_dir,
                input_path,
            ]

            # Run the command
            # Use capture_output=True and text=True for Python 3.7+
            # For older versions, check=True handles errors
            result = subprocess.run(command, capture_output=True, text=True, check=True)

            # Check for errors in stderr (LibreOffice might print warnings here)
            if result.stderr:
                # Log warnings if necessary
                print(f"LibreOffice conversion warnings/errors: {result.stderr}", file=sys.stderr)

            return output_path

        except FileNotFoundError:
            raise ReportGenerationError(details="LibreOffice command not found. Is LibreOffice installed and in your system PATH?")
        except subprocess.CalledProcessError as e:
            raise ReportGenerationError(details=f"LibreOffice conversion failed: {e.stderr}")
        except Exception as e:
            raise ReportGenerationError(details=f"An unexpected error occurred during PDF conversion: {e}")
    ```

*   **Report Type Management (`circuit_reports/` & `report_manager.py`):**
    *   **Specific Report Logic:** Each report-specific module (e.g., `heat_loss_report.py`) contains the unique orchestration logic for generating that particular report type:
        1.  Calls the relevant `data_preparator`.
        2.  Selects the appropriate native template.
        3.  Calls the `document_populator` to fill the template.
        4.  Calls the `pdf_converter` to transform the populated document to PDF.
    *   **Orchestration (`report_manager.py`):** Provides a central interface (e.g., `generate_report(report_type, project_id, ...)`). This manager selects the correct report module, invokes its logic, and returns the final PDF.
*   **Templates (`src/core/reports/templates/`)**
    *   **Format:** Native Microsoft Office files (`.docx`, `.xlsx`).
    *   **Content:** Contains static layout, formatting, and dynamic placeholders that the `document_populator` can fill.

**4. Error Handling (Integration with `src/core/errors/exceptions.py`)**

*   **Data Preparation Errors:** If required data cannot be aggregated or is inconsistent, `ReportGenerationError` or `NotFoundError` (if crucial entities are missing) should be raised, providing details about the data deficiency.
*   **Document Population Errors:** If the document populators fail to replace placeholders, encounter invalid document structures, or other issues, `ReportGenerationError` should be raised, detailing the problem within the document template.
*   **PDF Conversion Errors:** If the external PDF conversion process fails (e.g., LibreOffice not installed, corrupted populated document, conversion timeout), `ReportGenerationError` (or a more specific `PdfGenerationError` if needed) should be raised, detailing the conversion issue.
*   **Propagation:** All such errors are captured and propagated up to the [Service Layer](../../services/services-architecture.md), which will log them and translate them into user-friendly messages via the global [error handling framework](../../errors/errors-architecture.md).

**5. Interaction with Other Layers**

*   **Service Layer (Primary Consumer):**
    *   The `ReportService` ([`src/core/services/report_service.py`](../../services/services-architecture.md)) acts as the interface to the Report Generation Layer. It receives requests from the [API Layer](../../../api/api-architecture.md), fetches necessary data (potentially coordinating with `ProjectService`, `CalculationService`), and then calls the `report_manager.py` to generate the report.
    *   **Error Handling:** Catches `ReportGenerationError` and other relevant exceptions from the Report Generation Layer, logs them, and translates them into appropriate [Service Layer](../../services/services-architecture.md) exceptions for the [API Layer](../../../api/api-architecture.md).
*   **API Layer:**
    *   Receives requests for report generation (e.g., `POST /projects/{project_id}/reports/circuit_heat_loss`).
    *   Calls the `ReportService`.
    *   Upon successful generation, returns the PDF document as a file response (e.g., `FileResponse` in FastAPI) or provides a link to download it.
    *   **Error Handling:** Relies on global exception handlers to present consistent error messages if report generation fails.
*   **Calculations Layer:** Reports will directly consume the structured results from the [Calculations Layer](../calculations/calculations-architecture.md) (via the [Service Layer](../../services/services-architecture.md)) to populate fields and tables.
*   **Repository Layer:** Reports will query the [Repository Layer](../../repositories/repositories-architecture.md) (via the [Service Layer](../../services/services-architecture.md)) to retrieve static project data, entity details, and historical information.
*   **File Storage (Temporary):** Generated populated native documents and final PDFs might be temporarily stored in a designated directory before being served or cleaned up.
*   **Telemetry:** Instruments report generation processes (e.g., `report_generation_duration` metric, tracing individual steps) to monitor performance and identify bottlenecks.
