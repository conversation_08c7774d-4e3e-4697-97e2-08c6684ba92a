# src/core/models/__init__.py

# Import all Enum classes to ensure they are registered
# Import all model classes to ensure they are registered with Base.metadata
# Importing the modules makes the classes available to Base.metadata
from . import (
    # audit,  # noqa: F401 # REMOVE THIS LINE
    components,  # noqa: F401
    documents,  # noqa: F401
    electrical,  # noqa: F401
    enums,  # noqa: F401
    heat_tracing,  # noqa: F401
    project,  # noqa: F401
    switchboard,  # noqa: F401
    users,  # noqa: F401
)
