# src/core/errors/exceptions.py
from typing import Any, Dict, Optional


class BaseApplicationException(Exception):
    """Base exception for all application-specific errors."""

    def __init__(
        self,
        code: str,
        detail: str,
        category: str = "ServerError",
        status_code: int = 500,
        metadata: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(detail)
        self.code = code
        self.detail = detail
        self.category = category
        self.status_code = status_code  # Useful for API/HTTP context, even in desktop
        self.metadata = metadata or {}


# Domain-specific exceptions
class NotFoundError(BaseApplicationException):
    pass  # Inherits attributes


class ProjectNotFoundError(NotFoundError):
    def __init__(self, project_id: str):
        super().__init__(
            code="404_001",
            detail=f"Project with ID '{project_id}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"project_id": project_id},
        )


class DataValidationError(BaseApplicationException):
    def __init__(self, details: dict):  # For Pydantic validation errors
        super().__init__(
            code="400_001",
            detail="Input validation failed.",
            category="Validation",
            status_code=400,
            metadata={"validation_errors": details},
        )


class InvalidInputError(BaseApplicationException):
    def __init__(self, message: str):
        super().__init__(
            code="400_002",
            detail=message,
            category="Validation",
            status_code=400,
            metadata={"input_error": message},
        )


class DuplicateEntryError(BaseApplicationException):
    def __init__(self, message: str, original_exception: Optional[Exception] = None):
        super().__init__(
            code="409_001",
            detail=message,
            category="ClientError",
            status_code=409,
            metadata={
                "original_exception": str(original_exception)
                if original_exception
                else None
            },
        )
        self.original_exception = original_exception


class DatabaseError(BaseApplicationException):
    def __init__(self, reason: str, original_exception: Optional[Exception] = None):
        super().__init__(
            code="500_002",
            detail=reason,
            category="ServerError",
            status_code=500,
            metadata={
                "original_exception": str(original_exception)
                if original_exception
                else None
            },
        )
        self.original_exception = original_exception


class ComponentNotFoundError(NotFoundError):
    def __init__(self, component_id_or_name: str):
        super().__init__(
            code="404_002",
            detail=f"Component '{component_id_or_name}' not found.",
            category="ClientError",
            status_code=404,
            metadata={"component_identifier": component_id_or_name},
        )


class CalculationError(BaseApplicationException):
    def __init__(self, details: str):
        super().__init__(
            code="422_002",  # Example code
            detail=f"Calculation error: {details}.",
            category="Calculation",
            status_code=422,  # Unprocessable Entity
            metadata={"reason": details},
        )


class StandardComplianceError(
    CalculationError
):  # Inherit from CalculationError or BaseApplicationException
    def __init__(self, details: str):
        super().__init__(details)
