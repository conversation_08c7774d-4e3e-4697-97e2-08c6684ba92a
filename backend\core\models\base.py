import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, String, TypeDecorator, func
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import Mapped, declarative_base, mapped_column, relationship

# Define the declarative base
Base = declarative_base()


# --- Mixin for Common Columns ---
class CommonColumns:
    """Mixin for common database table columns."""

    @declared_attr
    def id(cls) -> Mapped[int]:
        return mapped_column(primary_key=True, autoincrement=True)

    @declared_attr
    def name(cls) -> Mapped[str]:
        return mapped_column(nullable=False)

    @declared_attr
    def notes(cls) -> Mapped[str | None]:
        return mapped_column(nullable=True)

    @declared_attr
    def created_at(cls) -> Mapped[datetime.datetime]:
        return mapped_column(DateTime, default=func.now())

    @declared_attr
    def updated_at(cls) -> Mapped[datetime.datetime]:
        return mapped_column(DateTime, default=func.now(), onupdate=func.now())


# --- Mixin for Soft Delete ---
class SoftDeleteColumns:
    """Mixin for soft deletion columns."""

    @declared_attr
    def is_deleted(cls) -> Mapped[bool]:
        return mapped_column(Boolean, default=False, nullable=False)

    @declared_attr
    def deleted_at(cls) -> Mapped[datetime.datetime | None]:
        return mapped_column(DateTime, nullable=True)

    @declared_attr
    def deleted_by_user_id(cls) -> Mapped[int | None]:
        return mapped_column(ForeignKey("User.id"), nullable=True)

    @declared_attr
    def deleted_by_user(cls) -> Mapped["User | None"]:
        # Use a forward reference for 'User' as it's defined in another file
        return relationship(
            "User",
            foreign_keys=lambda: [cls.__table__.c.deleted_by_user_id],  # type: ignore
        )


# --- Custom TypeDecorator for Enums ---
class EnumType(TypeDecorator):
    impl = String
    cache_ok = True

    def __init__(self, enum_class=None, *arg, **kw):
        super(EnumType, self).__init__(*arg, **kw)
        self.enum_class = enum_class

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        if self.enum_class and not isinstance(value, self.enum_class):
            raise TypeError(
                f"Value must be an instance of {self.enum_class}, got {type(value)}"
            )
        return value.value if hasattr(value, "value") else str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        if self.enum_class:
            try:
                return self.enum_class(value)
            except ValueError:
                return None  # Or raise an error based on desired behavior
        return value

    def copy(self, **kw):
        return EnumType(self.enum_class, **kw)
