# backend/tests/test_activity_log_routes.py
"""
Tests for Activity Log API Routes.

This module tests the REST API endpoints for activity log operations including
CRUD operations, filtering, security events, and audit reporting.
"""

import json
import pytest
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from core.schemas.activity_log_schemas import (
    ActivityLogReadSchema,
    ActivityLogSummarySchema,
    EventTypeEnum,
    EntityTypeEnum,
)


class TestActivityLogRoutes:
    """Test Activity Log API routes."""

    @pytest.fixture
    def mock_activity_log_service(self):
        """Create a mock activity log service."""
        return Mock()

    @pytest.fixture
    def sample_activity_log_read(self):
        """Create a sample activity log read schema."""
        return ActivityLogReadSchema(
            id=1,
            timestamp=datetime.now(),
            user_id=1,
            event_type=EventTypeEnum.CREATE,
            entity_type=EntityTypeEnum.PROJECT,
            entity_id=123,
            details="Created new project",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )

    @pytest.fixture
    def sample_activity_log_summary(self):
        """Create a sample activity log summary schema."""
        return ActivityLogSummarySchema(
            id=1,
            timestamp=datetime.now(),
            user_id=1,
            event_type=EventTypeEnum.CREATE,
            entity_type=EntityTypeEnum.PROJECT,
            entity_id=123,
        )

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_create_activity_log_success(
        self, mock_service_class, sample_activity_log_read
    ):
        """Test successful activity log creation."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.log_event.return_value = sample_activity_log_read
        mock_service_class.return_value = mock_service

        # Test data
        test_data = {
            "user_id": 1,
            "event_type": "CREATE",
            "entity_type": "Project",
            "entity_id": 123,
            "details": "Created new project",
        }

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.post("/activity-logs", json=test_data)

        # Verify response
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["id"] == 1
        assert response_data["event_type"] == "CREATE"
        assert response_data["user_id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_activity_log_success(
        self, mock_service_class, sample_activity_log_read
    ):
        """Test successful activity log retrieval."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.get_activity_log.return_value = sample_activity_log_read
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/1")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == 1
        assert response_data["event_type"] == "CREATE"

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_activity_log_not_found(self, mock_service_class):
        """Test activity log retrieval when not found."""
        from api.v1.activity_log_routes import router
        from core.errors.exceptions import NotFoundError
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service to raise NotFoundError
        mock_service = Mock()
        mock_service.get_activity_log.side_effect = NotFoundError(
            code="ACTIVITY_LOG_NOT_FOUND", detail="Activity log 999 not found"
        )
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/999")

        # Verify response
        assert response.status_code == 404
        assert "Activity log 999 not found" in response.json()["detail"]

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_list_activity_logs_success(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test successful activity logs listing."""
        from api.v1.activity_log_routes import router
        from core.schemas.activity_log_schemas import (
            ActivityLogPaginatedResponseSchema,
        )
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        paginated_response = ActivityLogPaginatedResponseSchema(
            activity_logs=[sample_activity_log_summary],
            total=1,
            page=1,
            per_page=10,
            total_pages=1,
        )
        mock_service.get_activity_logs.return_value = paginated_response
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs?skip=0&limit=10")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total"] == 1
        assert len(response_data["activity_logs"]) == 1
        assert response_data["activity_logs"][0]["id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_list_activity_logs_with_filters(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test activity logs listing with filters."""
        from api.v1.activity_log_routes import router
        from core.schemas.activity_log_schemas import (
            ActivityLogPaginatedResponseSchema,
        )
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        paginated_response = ActivityLogPaginatedResponseSchema(
            activity_logs=[sample_activity_log_summary],
            total=1,
            page=1,
            per_page=10,
            total_pages=1,
        )
        mock_service.get_activity_logs.return_value = paginated_response
        mock_service_class.return_value = mock_service

        # Make request with filters
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get(
                "/activity-logs?user_id=1&event_type=CREATE&entity_type=Project&entity_id=123"
            )

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["total"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_update_activity_log_success(
        self, mock_service_class, sample_activity_log_read
    ):
        """Test successful activity log update."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.update_activity_log.return_value = sample_activity_log_read
        mock_service_class.return_value = mock_service

        # Test data
        test_data = {"details": "Updated project details"}

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.put("/activity-logs/1", json=test_data)

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_user_activity_logs_success(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test successful user activity logs retrieval."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.get_user_activity_logs.return_value = [sample_activity_log_summary]
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/users/1?skip=0&limit=10")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["id"] == 1
        assert response_data[0]["user_id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_user_activity_summary_success(self, mock_service_class):
        """Test successful user activity summary retrieval."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.get_user_activity_summary.return_value = {
            "user_id": 1,
            "total_events": 10,
            "event_type_counts": {"CREATE": 5, "UPDATE": 3, "DELETE": 2},
            "entity_type_counts": {"Project": 7, "Component": 3},
        }
        mock_service_class.return_value = mock_service

        # Make request
        start_date = (datetime.now() - timedelta(days=30)).isoformat()
        end_date = datetime.now().isoformat()

        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get(
                f"/activity-logs/users/1/summary?start_date={start_date}&end_date={end_date}"
            )

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["user_id"] == 1
        assert response_data["total_events"] == 10

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_entity_activity_logs_success(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test successful entity activity logs retrieval."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.get_entity_activity_logs.return_value = [
            sample_activity_log_summary
        ]
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/entities/Project/123?skip=0&limit=10")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["entity_type"] == "Project"
        assert response_data[0]["entity_id"] == 123

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_security_events_success(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test successful security events retrieval."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.get_security_events.return_value = [sample_activity_log_summary]
        mock_service_class.return_value = mock_service

        # Make request
        start_date = (datetime.now() - timedelta(days=7)).isoformat()
        end_date = datetime.now().isoformat()

        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get(
                f"/activity-logs/security-events?start_date={start_date}&end_date={end_date}&skip=0&limit=10"
            )

        # Verify response
        if response.status_code != 200:
            print(f"Response status: {response.status_code}")
            print(f"Response body: {response.json()}")
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data) == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_log_security_event_success(
        self, mock_service_class, sample_activity_log_read
    ):
        """Test successful security event logging."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.log_security_event.return_value = sample_activity_log_read
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.post(
                "/activity-logs/security-events?event_type=UNAUTHORIZED_ACCESS&user_id=1&severity=HIGH&threat_level=MEDIUM"
            )

        # Verify response
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_get_recent_activity_success(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test successful recent activity retrieval."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.get_recent_activity.return_value = [sample_activity_log_summary]
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/recent?limit=50")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_search_activity_logs_success(
        self, mock_service_class, sample_activity_log_summary
    ):
        """Test successful activity logs search."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.search_activity_logs.return_value = [sample_activity_log_summary]
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get(
                "/activity-logs/search?search_term=project&skip=0&limit=10"
            )

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert len(response_data) == 1
        assert response_data[0]["id"] == 1

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_delete_old_activity_logs_success(self, mock_service_class):
        """Test successful deletion of old activity logs."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service
        mock_service = Mock()
        mock_service.delete_old_activity_logs.return_value = 10
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.delete("/activity-logs/cleanup?days_to_keep=365")

        # Verify response
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["deleted_count"] == 10
        assert response_data["days_to_keep"] == 365
        assert "Deleted 10 old activity logs" in response_data["message"]

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_invalid_input_error_handling(self, mock_service_class):
        """Test invalid input error handling."""
        from api.v1.activity_log_routes import router
        from core.errors.exceptions import InvalidInputError
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service to raise InvalidInputError
        mock_service = Mock()
        mock_service.get_activity_log.side_effect = InvalidInputError(
            message="Invalid input provided"
        )
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/1")

        # Verify response
        assert response.status_code == 400
        assert "Invalid input provided" in response.json()["detail"]

    @patch("api.v1.activity_log_routes.ActivityLogService")
    def test_database_error_handling(self, mock_service_class):
        """Test database error handling."""
        from api.v1.activity_log_routes import router
        from core.errors.exceptions import DatabaseError
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Mock service to raise DatabaseError
        mock_service = Mock()
        mock_service.get_activity_log.side_effect = DatabaseError(
            reason="Database connection failed"
        )
        mock_service_class.return_value = mock_service

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.get("/activity-logs/1")

        # Verify response
        assert response.status_code == 500
        assert "Internal server error" in response.json()["detail"]

    def test_validation_error_handling(self):
        """Test request validation error handling."""
        from api.v1.activity_log_routes import router
        from fastapi import FastAPI

        # Create test app
        app = FastAPI()
        app.include_router(router)
        client = TestClient(app)

        # Test invalid data
        test_data = {
            "event_type": "INVALID_EVENT_TYPE",  # Invalid enum value
            "user_id": "not_a_number",  # Invalid type
        }

        # Make request
        with patch("api.v1.activity_log_routes.get_db_session"):
            response = client.post("/activity-logs", json=test_data)

        # Verify response
        assert response.status_code == 422  # Validation error
