from sqlalchemy import CheckConstraint, ForeignKey, UniqueConstraint, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, EnumType, SoftDeleteColumns

# from .components import Component
from .enums import CableInstallationMethod, ElectricalNodeType
# from .heat_tracing import ControlCircuit, Pipe, Vessel
# from .project import Project
# from .switchboard import Feeder, Switchboard


class ElectricalNode(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "ElectricalNode"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    node_type: Mapped[ElectricalNodeType] = mapped_column(
        EnumType(ElectricalNodeType), nullable=False
    )
    location_description: Mapped[str | None] = mapped_column(nullable=True)

    related_switchboard_id: Mapped[int | None] = mapped_column(
        ForeignKey("Switchboard.id"), nullable=True
    )
    related_feeder_id: Mapped[int | None] = mapped_column(
        ForeignKey("Feeder.id"), nullable=True
    )
    related_control_circuit_id: Mapped[int | None] = mapped_column(
        ForeignKey("ControlCircuit.id"), nullable=True
    )
    related_pipe_id: Mapped[int | None] = mapped_column(
        ForeignKey("Pipe.id"), nullable=True
    )
    related_vessel_id: Mapped[int | None] = mapped_column(
        ForeignKey("Vessel.id"), nullable=True
    )
    related_component_id: Mapped[int | None] = mapped_column(
        ForeignKey("Component.id"), unique=True, nullable=True
    )  # Enforced 1:1

    voltage_v: Mapped[float | None] = mapped_column(nullable=True)
    power_capacity_kva: Mapped[float | None] = mapped_column(nullable=True)

    # Relationships (using string forward references)
    project: Mapped["Project"] = relationship(back_populates="electrical_nodes")
    related_switchboard: Mapped["Switchboard | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_feeder: Mapped["Feeder | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_control_circuit: Mapped["ControlCircuit | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_pipe: Mapped["Pipe | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_vessel: Mapped["Vessel | None"] = relationship(
        back_populates="electrical_nodes"
    )
    related_component: Mapped["Component | None"] = relationship(
        back_populates="electrical_node"
    )

    outgoing_routes: Mapped[list["CableRoute"]] = relationship(
        foreign_keys="CableRoute.from_node_id",
        back_populates="from_node",
        cascade="all, delete-orphan",
    )
    incoming_routes: Mapped[list["CableRoute"]] = relationship(
        foreign_keys="CableRoute.to_node_id",
        back_populates="to_node",
        cascade="all, delete-orphan",
    )

    # Additional relationships for new models
    load_calculations: Mapped[list["LoadCalculation"]] = relationship(
        back_populates="electrical_node", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<ElectricalNode(id={self.id}, name='{self.name}', type='{self.node_type.value}', proj_id={self.project_id})>"


class CableRoute(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "CableRoute"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    from_node_id: Mapped[int] = mapped_column(
        ForeignKey("ElectricalNode.id"), nullable=False
    )
    to_node_id: Mapped[int] = mapped_column(
        ForeignKey("ElectricalNode.id"), nullable=False
    )

    cable_component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )

    length_m: Mapped[float] = mapped_column(nullable=False)
    number_of_runs: Mapped[int] = mapped_column(default=1, nullable=False)
    installation_method: Mapped[CableInstallationMethod] = mapped_column(
        EnumType(CableInstallationMethod), nullable=False
    )
    max_ambient_temp_c: Mapped[float | None] = mapped_column(nullable=True)
    min_ambient_temp_c: Mapped[float | None] = mapped_column(nullable=True)

    calculated_voltage_drop_v: Mapped[float | None] = mapped_column(nullable=True)
    calculated_current_capacity_a: Mapped[float | None] = mapped_column(nullable=True)

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="cable_routes")
    from_node: Mapped["ElectricalNode"] = relationship(
        foreign_keys=[from_node_id], back_populates="outgoing_routes"
    )
    to_node: Mapped["ElectricalNode"] = relationship(
        foreign_keys=[to_node_id], back_populates="incoming_routes"
    )
    cable_component: Mapped["Component"] = relationship()

    # Additional relationships for new models
    cable_segments: Mapped[list["CableSegment"]] = relationship(
        back_populates="cable_route", cascade="all, delete-orphan"
    )
    voltage_drop_calculations: Mapped[list["VoltageDropCalculation"]] = relationship(
        back_populates="cable_route", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint(
            "project_id",
            "from_node_id",
            "to_node_id",
            "cable_component_id",
            name="uq_cable_route_connection",
        ),
        CheckConstraint("from_node_id != to_node_id", name="chk_different_nodes"),
    )

    def __repr__(self):
        return (
            f"<CableRoute(id={self.id}, name='{self.name}', from='{self.from_node.name}', "
            f"to='{self.to_node.name}', cable='{self.cable_component.name}', length={self.length_m}m)>"
        )


class CableSegment(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "CableSegment"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    cable_route_id: Mapped[int] = mapped_column(
        ForeignKey("CableRoute.id"), nullable=False
    )
    cable_component_id: Mapped[int] = mapped_column(
        ForeignKey("Component.id"), nullable=False
    )

    segment_order: Mapped[int] = mapped_column(nullable=False, default=1)
    length_m: Mapped[float] = mapped_column(nullable=False)
    installation_method: Mapped[CableInstallationMethod] = mapped_column(
        EnumType(CableInstallationMethod), nullable=False
    )

    # Environmental conditions
    ambient_temperature_c: Mapped[float | None] = mapped_column(nullable=True)
    ground_temperature_c: Mapped[float | None] = mapped_column(nullable=True)
    burial_depth_m: Mapped[float | None] = mapped_column(nullable=True)

    # Cable specifications
    conductor_size_mm2: Mapped[float | None] = mapped_column(nullable=True)
    insulation_type: Mapped[str | None] = mapped_column(nullable=True)
    sheath_type: Mapped[str | None] = mapped_column(nullable=True)
    armour_type: Mapped[str | None] = mapped_column(nullable=True)

    # Calculated values
    calculated_resistance_ohm_per_m: Mapped[float | None] = mapped_column(nullable=True)
    calculated_reactance_ohm_per_m: Mapped[float | None] = mapped_column(nullable=True)
    calculated_current_capacity_a: Mapped[float | None] = mapped_column(nullable=True)
    calculated_voltage_drop_v: Mapped[float | None] = mapped_column(nullable=True)
    calculated_power_loss_w: Mapped[float | None] = mapped_column(nullable=True)

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="cable_segments")
    cable_route: Mapped["CableRoute"] = relationship(back_populates="cable_segments")
    cable_component: Mapped["Component"] = relationship()

    __table_args__ = (
        UniqueConstraint(
            "cable_route_id",
            "segment_order",
            name="uq_cable_segment_order",
        ),
        CheckConstraint("length_m > 0", name="chk_segment_length_positive"),
        CheckConstraint("segment_order > 0", name="chk_segment_order_positive"),
    )

    def __repr__(self):
        return (
            f"<CableSegment(id={self.id}, name='{self.name}', route_id={self.cable_route_id}, "
            f"order={self.segment_order}, length={self.length_m}m)>"
        )


class LoadCalculation(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "LoadCalculation"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    electrical_node_id: Mapped[int] = mapped_column(
        ForeignKey("ElectricalNode.id"), nullable=False
    )

    # Load identification
    load_type: Mapped[str] = mapped_column(
        nullable=False
    )  # e.g., "heat_tracing", "motor", "lighting"
    load_description: Mapped[str | None] = mapped_column(nullable=True)

    # Load parameters
    rated_power_kw: Mapped[float] = mapped_column(nullable=False)
    rated_voltage_v: Mapped[float] = mapped_column(nullable=False)
    rated_current_a: Mapped[float] = mapped_column(nullable=False)
    power_factor: Mapped[float] = mapped_column(nullable=False, default=1.0)
    efficiency_percent: Mapped[float] = mapped_column(nullable=False, default=100.0)

    # Operating conditions
    operating_hours_per_day: Mapped[float] = mapped_column(nullable=False, default=24.0)
    load_factor_percent: Mapped[float] = mapped_column(nullable=False, default=100.0)
    diversity_factor: Mapped[float] = mapped_column(nullable=False, default=1.0)

    # Calculated values
    calculated_operating_power_kw: Mapped[float | None] = mapped_column(nullable=True)
    calculated_operating_current_a: Mapped[float | None] = mapped_column(nullable=True)
    calculated_daily_energy_kwh: Mapped[float | None] = mapped_column(nullable=True)
    calculated_annual_energy_mwh: Mapped[float | None] = mapped_column(nullable=True)

    # Safety and design factors
    safety_factor: Mapped[float] = mapped_column(nullable=False, default=1.2)
    design_margin_percent: Mapped[float] = mapped_column(nullable=False, default=20.0)

    # Related heat tracing loads (if applicable)
    related_pipe_id: Mapped[int | None] = mapped_column(
        ForeignKey("Pipe.id"), nullable=True
    )
    related_vessel_id: Mapped[int | None] = mapped_column(
        ForeignKey("Vessel.id"), nullable=True
    )
    related_htcircuit_id: Mapped[int | None] = mapped_column(
        ForeignKey("HTCircuit.id"), nullable=True
    )

    # Relationships
    project: Mapped["Project"] = relationship(back_populates="load_calculations")
    electrical_node: Mapped["ElectricalNode"] = relationship(
        back_populates="load_calculations"
    )
    related_pipe: Mapped["Pipe | None"] = relationship(
        back_populates="load_calculations"
    )
    related_vessel: Mapped["Vessel | None"] = relationship(
        back_populates="load_calculations"
    )
    related_htcircuit: Mapped["HTCircuit | None"] = relationship(
        back_populates="load_calculations"
    )

    # Additional relationships for new models
    voltage_drop_calculations: Mapped[list["VoltageDropCalculation"]] = relationship(
        back_populates="load_calculation", cascade="all, delete-orphan"
    )

    __table_args__ = (
        CheckConstraint("rated_power_kw > 0", name="chk_rated_power_positive"),
        CheckConstraint("rated_voltage_v > 0", name="chk_rated_voltage_positive"),
        CheckConstraint("rated_current_a > 0", name="chk_rated_current_positive"),
        CheckConstraint(
            "power_factor > 0 AND power_factor <= 1", name="chk_power_factor_range"
        ),
        CheckConstraint(
            "efficiency_percent > 0 AND efficiency_percent <= 100",
            name="chk_efficiency_range",
        ),
        CheckConstraint(
            "operating_hours_per_day >= 0 AND operating_hours_per_day <= 24",
            name="chk_operating_hours_range",
        ),
        CheckConstraint(
            "load_factor_percent > 0 AND load_factor_percent <= 100",
            name="chk_load_factor_range",
        ),
        CheckConstraint("diversity_factor > 0", name="chk_diversity_factor_positive"),
        CheckConstraint("safety_factor >= 1", name="chk_safety_factor_minimum"),
    )

    def __repr__(self):
        return (
            f"<LoadCalculation(id={self.id}, name='{self.name}', type='{self.load_type}', "
            f"power={self.rated_power_kw}kW, node_id={self.electrical_node_id})>"
        )


class VoltageDropCalculation(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "VoltageDropCalculation"

    project_id: Mapped[int] = mapped_column(ForeignKey("Project.id"), nullable=False)
    cable_route_id: Mapped[int] = mapped_column(
        ForeignKey("CableRoute.id"), nullable=False
    )
    load_calculation_id: Mapped[int | None] = mapped_column(
        ForeignKey("LoadCalculation.id"), nullable=True
    )

    # Calculation inputs
    supply_voltage_v: Mapped[float] = mapped_column(nullable=False)
    load_current_a: Mapped[float] = mapped_column(nullable=False)
    cable_length_m: Mapped[float] = mapped_column(nullable=False)
    cable_resistance_ohm_per_m: Mapped[float] = mapped_column(nullable=False)
    cable_reactance_ohm_per_m: Mapped[float] = mapped_column(
        nullable=False, default=0.0
    )
    power_factor: Mapped[float] = mapped_column(nullable=False, default=1.0)

    # Environmental factors
    ambient_temperature_c: Mapped[float] = mapped_column(nullable=False, default=25.0)
    ground_temperature_c: Mapped[float | None] = mapped_column(nullable=True)
    derating_factor: Mapped[float] = mapped_column(nullable=False, default=1.0)

    # Calculation results
    calculated_voltage_drop_v: Mapped[float | None] = mapped_column(nullable=True)
    calculated_voltage_drop_percent: Mapped[float | None] = mapped_column(nullable=True)
    calculated_power_loss_w: Mapped[float | None] = mapped_column(nullable=True)
    calculated_efficiency_percent: Mapped[float | None] = mapped_column(nullable=True)

    # Compliance and validation
    max_allowed_voltage_drop_percent: Mapped[float] = mapped_column(
        nullable=False, default=5.0
    )
    is_compliant: Mapped[bool | None] = mapped_column(nullable=True)
    compliance_margin_percent: Mapped[float | None] = mapped_column(nullable=True)

    # Calculation metadata
    calculation_method: Mapped[str] = mapped_column(nullable=False, default="IEC")
    calculation_standard: Mapped[str | None] = mapped_column(nullable=True)
    calculation_notes: Mapped[str | None] = mapped_column(Text, nullable=True)

    # Relationships
    project: Mapped["Project"] = relationship(
        back_populates="voltage_drop_calculations"
    )
    cable_route: Mapped["CableRoute"] = relationship(
        back_populates="voltage_drop_calculations"
    )
    load_calculation: Mapped["LoadCalculation | None"] = relationship(
        back_populates="voltage_drop_calculations"
    )

    __table_args__ = (
        CheckConstraint("supply_voltage_v > 0", name="chk_supply_voltage_positive"),
        CheckConstraint("load_current_a > 0", name="chk_load_current_positive"),
        CheckConstraint("cable_length_m > 0", name="chk_cable_length_positive"),
        CheckConstraint(
            "cable_resistance_ohm_per_m >= 0", name="chk_cable_resistance_non_negative"
        ),
        CheckConstraint(
            "cable_reactance_ohm_per_m >= 0", name="chk_cable_reactance_non_negative"
        ),
        CheckConstraint(
            "power_factor > 0 AND power_factor <= 1", name="chk_vd_power_factor_range"
        ),
        CheckConstraint(
            "derating_factor > 0 AND derating_factor <= 1",
            name="chk_derating_factor_range",
        ),
        CheckConstraint(
            "max_allowed_voltage_drop_percent > 0 AND max_allowed_voltage_drop_percent <= 50",
            name="chk_max_voltage_drop_range",
        ),
    )

    def __repr__(self):
        return (
            f"<VoltageDropCalculation(id={self.id}, name='{self.name}', route_id={self.cable_route_id}, "
            f"voltage_drop={self.calculated_voltage_drop_percent}%, compliant={self.is_compliant})>"
        )
