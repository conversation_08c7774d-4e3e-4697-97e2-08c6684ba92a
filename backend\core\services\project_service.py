# backend/core/services/project_service.py
"""
Project Service Layer

This module contains the business logic for Project entity operations including:
- Project creation, retrieval, updating, and deletion
- Business validation and rules enforcement
- Transaction management and orchestration
- Error handling and logging
"""

from typing import Optional

from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from backend.config.logging_config import get_logger
from backend.core.errors.exceptions import (
    DatabaseError,
    DataValidationError,
    DuplicateEntryError,
    ProjectNotFoundError,
)
from backend.core.models.project import Project
from backend.core.repositories.project_repository import ProjectRepository
from backend.core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectSummarySchema,
    ProjectUpdateSchema,
)

# Initialize logger for this module
logger = get_logger(__name__)


class ProjectService:
    """
    Service class for Project entity business logic.

    This service handles all business operations related to projects including
    CRUD operations, validation, and orchestration with other services.
    """

    def __init__(self, project_repository: ProjectRepository):
        """
        Initialize the Project service.

        Args:
            project_repository: Repository for Project data access
        """
        self.project_repository = project_repository
        logger.debug(
            f"ProjectService initialized with repository: {type(project_repository).__name__}"
        )

    def create_project(self, project_data: ProjectCreateSchema) -> ProjectReadSchema:
        """
        Create a new project with business validation.

        Args:
            project_data: Validated project creation data

        Returns:
            ProjectReadSchema: Created project data

        Raises:
            DuplicateEntryError: If project name or number already exists
            DataValidationError: If business validation fails
            DatabaseError: If database operation fails
        """
        logger.info(
            f"Attempting to create new project: '{project_data.name}' ({project_data.project_number})"
        )

        try:
            # Business validation
            self._validate_project_creation(project_data)

            # Convert schema to dict for repository
            project_dict = project_data.model_dump()

            # Create project via repository
            new_project = self.project_repository.create(project_dict)

            # Commit the transaction (repository doesn't auto-commit)
            self.project_repository.db_session.commit()
            self.project_repository.db_session.refresh(new_project)

            logger.info(
                f"Project '{new_project.name}' (ID: {new_project.id}) created successfully"
            )

            # Convert to read schema
            return ProjectReadSchema.model_validate(new_project)

        except IntegrityError as e:
            self.project_repository.db_session.rollback()
            logger.warning(
                f"Duplicate project detected: {project_data.name} or {project_data.project_number}"
            )

            # Determine which field caused the duplicate
            error_msg = str(e.orig).lower()
            if "uq_project_name" in error_msg or "name" in error_msg:
                raise DuplicateEntryError(
                    message=f"A project with the name '{project_data.name}' already exists.",
                    original_exception=e,
                )
            elif "uq_project_number" in error_msg or "project_number" in error_msg:
                raise DuplicateEntryError(
                    message=f"A project with the number '{project_data.project_number}' already exists.",
                    original_exception=e,
                )
            else:
                raise DuplicateEntryError(
                    message="A project with the given unique constraint already exists.",
                    original_exception=e,
                )

        except SQLAlchemyError as e:
            self.project_repository.db_session.rollback()
            logger.error(
                f"Database error creating project '{project_data.name}': {e}",
                exc_info=True,
            )
            raise DatabaseError(
                reason=f"Failed to create project due to database error: {str(e)}",
                original_exception=e,
            )
        except Exception as e:
            self.project_repository.db_session.rollback()
            logger.error(
                f"Unexpected error creating project '{project_data.name}': {e}",
                exc_info=True,
            )
            raise

    def get_project_details(self, project_id: str) -> ProjectReadSchema:
        """
        Retrieve detailed project information.

        Args:
            project_id: Project ID (can be numeric ID or project number)

        Returns:
            ProjectReadSchema: Project details

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving project details for ID: {project_id}")

        try:
            # Try to get by numeric ID first, then by project number
            project = None

            if project_id.isdigit():
                project = self.project_repository.get_by_id(int(project_id))

            if project is None:
                # Try by project number
                try:
                    project = self.project_repository.get_by_code(project_id)
                except ProjectNotFoundError:
                    pass

            if project is None:
                logger.warning(f"Project not found: {project_id}")
                raise ProjectNotFoundError(project_id=project_id)

            # Check if project is soft deleted
            if project.is_deleted:
                logger.warning(f"Attempted to access deleted project: {project_id}")
                raise ProjectNotFoundError(project_id=project_id)

            logger.debug(f"Project found: '{project.name}' (ID: {project.id})")
            return ProjectReadSchema.model_validate(project)

        except ProjectNotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving project {project_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to retrieve project due to database error: {str(e)}",
                original_exception=e,
            )

    def update_project(
        self, project_id: str, project_data: ProjectUpdateSchema
    ) -> ProjectReadSchema:
        """
        Update an existing project.

        Args:
            project_id: Project ID (can be numeric ID or project number)
            project_data: Validated project update data

        Returns:
            ProjectReadSchema: Updated project data

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DuplicateEntryError: If updated name/number conflicts
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to update project: {project_id}")

        try:
            # Get existing project
            existing_project = self._get_project_by_id_or_code(project_id)

            # Business validation for updates
            self._validate_project_update(existing_project, project_data)

            # Update only provided fields
            update_dict = project_data.model_dump(exclude_unset=True)

            if not update_dict:
                logger.debug(f"No fields to update for project {project_id}")
                return ProjectReadSchema.model_validate(existing_project)

            # Update the project
            for field, value in update_dict.items():
                setattr(existing_project, field, value)

            # Commit the transaction
            self.project_repository.db_session.commit()
            self.project_repository.db_session.refresh(existing_project)

            logger.info(
                f"Project '{existing_project.name}' (ID: {existing_project.id}) updated successfully"
            )
            return ProjectReadSchema.model_validate(existing_project)

        except (ProjectNotFoundError, DuplicateEntryError):
            self.project_repository.db_session.rollback()
            raise
        except IntegrityError as e:
            self.project_repository.db_session.rollback()
            logger.warning(
                f"Duplicate constraint violation updating project {project_id}"
            )

            error_msg = str(e.orig).lower()
            if "uq_project_name" in error_msg:
                raise DuplicateEntryError(
                    message=f"A project with the name '{project_data.name}' already exists.",
                    original_exception=e,
                )
            elif "uq_project_number" in error_msg:
                raise DuplicateEntryError(
                    message=f"A project with the number '{project_data.project_number}' already exists.",
                    original_exception=e,
                )
            else:
                raise DuplicateEntryError(
                    message="Update would violate unique constraint.",
                    original_exception=e,
                )
        except SQLAlchemyError as e:
            self.project_repository.db_session.rollback()
            logger.error(
                f"Database error updating project {project_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to update project due to database error: {str(e)}",
                original_exception=e,
            )

    def delete_project(
        self, project_id: str, deleted_by_user_id: Optional[int] = None
    ) -> None:
        """
        Soft delete a project.

        Args:
            project_id: Project ID (can be numeric ID or project number)
            deleted_by_user_id: ID of user performing the deletion

        Raises:
            ProjectNotFoundError: If project doesn't exist
            DatabaseError: If database operation fails
        """
        logger.info(f"Attempting to delete project: {project_id}")

        try:
            # Get existing project
            existing_project = self._get_project_by_id_or_code(project_id)

            if existing_project.is_deleted:
                logger.warning(f"Project {project_id} is already deleted")
                raise ProjectNotFoundError(project_id=project_id)

            # Perform soft delete
            from datetime import datetime

            existing_project.is_deleted = True
            existing_project.deleted_at = datetime.utcnow()
            existing_project.deleted_by_user_id = deleted_by_user_id

            # Commit the transaction
            self.project_repository.db_session.commit()

            logger.info(
                f"Project '{existing_project.name}' (ID: {existing_project.id}) deleted successfully"
            )

        except ProjectNotFoundError:
            self.project_repository.db_session.rollback()
            raise
        except SQLAlchemyError as e:
            self.project_repository.db_session.rollback()
            logger.error(
                f"Database error deleting project {project_id}: {e}", exc_info=True
            )
            raise DatabaseError(
                reason=f"Failed to delete project due to database error: {str(e)}",
                original_exception=e,
            )

    def get_projects_list(
        self, page: int = 1, per_page: int = 10, include_deleted: bool = False
    ) -> ProjectListResponseSchema:
        """
        Get paginated list of projects.

        Args:
            page: Page number (1-based)
            per_page: Number of projects per page
            include_deleted: Whether to include soft-deleted projects

        Returns:
            ProjectListResponseSchema: Paginated project list

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Retrieving projects list: page={page}, per_page={per_page}, include_deleted={include_deleted}"
        )

        try:
            # Calculate offset
            skip = (page - 1) * per_page

            # Get projects from repository
            projects = self.project_repository.get_all(skip=skip, limit=per_page)

            # Filter out deleted projects if not requested
            if not include_deleted:
                projects = [p for p in projects if not p.is_deleted]

            # Get total count (this is simplified - in production you'd want a separate count query)
            all_projects = self.project_repository.get_all(
                skip=0, limit=1000
            )  # Simplified for now
            if not include_deleted:
                all_projects = [p for p in all_projects if not p.is_deleted]
            total = len(all_projects)

            # Convert to summary schemas
            project_summaries = [
                ProjectSummarySchema.model_validate(p) for p in projects
            ]

            # Calculate total pages
            import math

            total_pages = math.ceil(total / per_page) if total > 0 else 1

            logger.debug(
                f"Retrieved {len(project_summaries)} projects (total: {total})"
            )

            return ProjectListResponseSchema(
                projects=project_summaries,
                total=total,
                page=page,
                per_page=per_page,
                total_pages=total_pages,
            )

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving projects list: {e}", exc_info=True)
            raise DatabaseError(
                reason=f"Failed to retrieve projects list due to database error: {str(e)}",
                original_exception=e,
            )

    def _get_project_by_id_or_code(self, project_id: str) -> Project:
        """
        Helper method to get project by ID or project number.

        Args:
            project_id: Project ID (can be numeric ID or project number)

        Returns:
            Project: The found project

        Raises:
            ProjectNotFoundError: If project doesn't exist
        """
        project = None

        if project_id.isdigit():
            project = self.project_repository.get_by_id(int(project_id))

        if project is None:
            try:
                project = self.project_repository.get_by_code(project_id)
            except ProjectNotFoundError:
                pass

        if project is None or project.is_deleted:
            raise ProjectNotFoundError(project_id=project_id)

        return project

    def _validate_project_creation(self, project_data: ProjectCreateSchema) -> None:
        """
        Perform business validation for project creation.

        Args:
            project_data: Project creation data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Temperature validation
        if project_data.max_ambient_temp_c <= project_data.min_ambient_temp_c:
            raise DataValidationError(
                details={
                    "temperature_range": "Maximum ambient temperature must be greater than minimum ambient temperature"
                }
            )

        # Maintenance temperature validation
        if project_data.desired_maintenance_temp_c <= project_data.max_ambient_temp_c:
            raise DataValidationError(
                details={
                    "maintenance_temperature": f"Desired maintenance temperature ({project_data.desired_maintenance_temp_c}°C) must be greater than maximum ambient temperature ({project_data.max_ambient_temp_c}°C)"
                }
            )

    def _validate_project_update(
        self, existing_project: Project, project_data: ProjectUpdateSchema
    ) -> None:
        """
        Perform business validation for project updates.

        Args:
            existing_project: Current project data
            project_data: Update data to validate

        Raises:
            DataValidationError: If business validation fails
        """
        # Get effective values (updated or existing)
        min_temp = (
            project_data.min_ambient_temp_c
            if project_data.min_ambient_temp_c is not None
            else existing_project.min_ambient_temp_c
        )
        max_temp = (
            project_data.max_ambient_temp_c
            if project_data.max_ambient_temp_c is not None
            else existing_project.max_ambient_temp_c
        )
        maintenance_temp = (
            project_data.desired_maintenance_temp_c
            if project_data.desired_maintenance_temp_c is not None
            else existing_project.desired_maintenance_temp_c
        )

        # Temperature range validation
        if max_temp <= min_temp:
            raise DataValidationError(
                details={
                    "temperature_range": "Maximum ambient temperature must be greater than minimum ambient temperature"
                }
            )

        # Maintenance temperature validation
        if maintenance_temp <= max_temp:
            raise DataValidationError(
                details={
                    "maintenance_temperature": f"Desired maintenance temperature ({maintenance_temp}°C) must be greater than maximum ambient temperature ({max_temp}°C)"
                }
            )
