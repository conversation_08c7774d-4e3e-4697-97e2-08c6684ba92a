# Initial Prompt for Next AI Agent

## 🎯 **Mission: Implement Document Entity for Ultimate Electrical Designer Backend**

You are implementing the Document Entity for the Ultimate Electrical Designer backend. This entity provides critical document management functionality including data import/export, report generation, and calculation standards management.

## 📋 **Current State**
✅ **COMPLETED**: Project, Component, Heat Tracing, Electrical, Switchboard, and User entities (6/7 entities complete)
🎯 **YOUR TASK**: Document Entity implementation (final core entity)

## 🏗️ **What You Need to Implement**

### **Document Entity Models** (Already exist in `backend/core/models/documents.py`):
- **ImportedDataRevision** - Data import management with revision tracking
- **ExportedDocument** - Report generation and export management  
- **CalculationStandard** - Engineering standards and parameters

### **Required Implementation (5-Layer Architecture)**:
1. **Schemas** (`backend/core/schemas/document_schemas.py`) - Pydantic validation
2. **Repositories** (`backend/core/repositories/document_repository.py`) - Data access layer
3. **Services** (`backend/core/services/document_service.py`) - Business logic
4. **API Routes** (`backend/api/v1/document_routes.py`) - REST endpoints
5. **Tests** - Comprehensive test suite (>90% coverage)

## 🔗 **Key Integration Points**
- **Project Entity**: Document scoping and access control
- **User Entity**: Audit trails and user tracking
- **Calculation Layer**: Standards management and parameter validation
- **File Management**: Secure upload, storage, and retrieval

## 📚 **Reference Patterns**
Study these completed implementations:
- `backend/docs/switchboard-entity-completion-summary.md` - Recent implementation
- `backend/docs/user-entity-completion-summary.md` - User integration patterns
- `backend/core/schemas/project_schemas.py` - Schema validation patterns
- `backend/core/services/user_service.py` - Service layer patterns

## 🎯 **Success Criteria**
- [ ] Complete CRUD operations for all document entities
- [ ] File upload/download with validation and security
- [ ] Report generation from project data
- [ ] Calculation standards management
- [ ] Revision control with active version tracking
- [ ] >90% test coverage across all layers

## 🚀 **Getting Started**
1. Review the existing document models in `backend/core/models/documents.py`
2. Study the established patterns from completed entities
3. Start with schemas - build comprehensive validation
4. Follow the 5-layer architecture pattern
5. Test early and often with file handling

**You're building on a solid foundation with 6 complete entities. Focus on robust file handling and document generation that integrates seamlessly with the existing engineering workflows!** 📄🔧
