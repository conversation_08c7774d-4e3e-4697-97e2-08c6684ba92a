# src/core/errors/error_factory.py
from .exceptions import BaseApplicationException
from .error_registry import ERROR_REGISTRY
from .error_templates import ERROR_MESSAGES

class ErrorFactory:
    @staticmethod
    def create_exception(code: str, **kwargs) -> BaseApplicationException:
        error_info = ERROR_REGISTRY.get(code)
        if not error_info:
            raise ValueError(f"Error code '{code}' not found in registry.")

        message = ERROR_MESSAGES.get(error_info["message_key"], "An unexpected error occurred.")
        formatted_message = message.format(**kwargs)

        # Assuming BaseApplicationException can take code, detail, category, etc.
        return BaseApplicationException(
            code=code,
            detail=formatted_message,
            category=error_info["category"],
            status_code=error_info.get("http_status", 500),
            metadata=kwargs # Store placeholders for logging/debugging
        )