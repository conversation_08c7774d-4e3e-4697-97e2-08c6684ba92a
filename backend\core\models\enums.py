import enum

class InstallationEnvironment(enum.Enum):
    INDOOR = "indoor"
    OUTDOOR = "outdoor"

class HTCircuitApplicationType(enum.Enum):
    FREEZE_PROTECT = "Freeze Protect"
    PROCESS_TEMP = "Process Temp"

class HeatingMethodType(enum.Enum):
    PARALLEL = "Parallel"
    SPIRAL = "Spiral"
    PANEL = "Panel"
    PAD = "Pad"
    GRID = "Grid"

class SwitchboardType(enum.Enum):
    MAIN = "Main"
    SUB_DISTRIBUTION = "Sub-distribution"

class PipeMaterialType(enum.Enum):
    CARBON_STEEL = "carbon steel"
    STAINLESS_STEEL = "stainless steel"
    COPPER = "copper"

class ControlCircuitType(enum.Enum):
    TEMPERATURE_CONTROL = "Temperature Control"
    LIMITING = "Limiting"

class SensorType(enum.Enum):
    RTD = "RTD"
    THERMOCOUPLE = "Thermocouple"
    AMBIENT = "Ambient"
    LINE = "Line"

class HeatTracingCableType(enum.Enum):
    SELF_REGULATING = "self-regulating"
    CONSTANT_WATTAGE = "constant wattage"
    MI = "MI"
    SKIN_EFFECT = "skin effect"
    SERIES_RESISTANCE = "series resistance"

class ElectricalComponentType(enum.Enum):
    FUSE = "Fuse"
    CONTACTOR = "Contactor"
    MCB = "Miniature Circuit Breaker"
    MCCB = "Molded-Case Circuit Breaker"
    CIRCUIT_BREAKER = "Circuit Breaker"
    ISOLATION_SWITCH = "Isolation Switch"
    RCD = "Residual Current Device"
    RCBO = "Residual Current Circuit Breaker with Overcurrent Protection"
    EMR = "Electromechanical Relay"
    SSR = "Solid State Relay"
    CONNECTION_KIT = "Connection Kit"
    END_SEAL = "End Seal"
    SPLICE_KIT = "Splice Kit"
    TEE_KIT = "Tee Kit"
    THERMOSTAT = "Thermostat"
    CURRENT_TRANSFORMER = "Current Transformer"
    VOLTAGE_TRANSFORMER = "Voltage Transformer"
    METER = "Meter"
    INDICATOR_LIGHT = "Indicator Light"
    PUSH_BUTTON = "Push Button"
    TERMINAL_BLOCK = "Terminal Block"
    ENCLOSURE = "Enclosure"

class ElectricalNodeType(enum.Enum):
    SWITCHBOARD_INCOMING = "Switchboard Incoming"
    SWITCHBOARD_OUTGOING = "Switchboard Outgoing"
    FEEDER_OUTPUT = "Feeder Output"
    CONTROL_CIRCUIT_INPUT = "Control Circuit Input"
    CONTROL_CIRCUIT_OUTPUT = "Control Circuit Output"
    LOAD_TERMINAL_BOX = "Load Terminal Box"
    SENSOR_TERMINAL = "Sensor Terminal"
    JUNCTION_BOX = "Junction Box"
    FIELD_DEVICE = "Field Device"
    OTHER = "Other"

class CableInstallationMethod(enum.Enum):
    CABLE_TRAY = "Cable Tray"
    CONDUIT = "Conduit"
    DIRECT_BURIED = "Direct Buried"
    OPEN_AIR = "Open Air"
    DUCT_BANK = "Duct Bank"
    OTHER = "Other"
