# backend/tests/test_services/test_component_service.py
"""
Tests for Component and ComponentCategory service layer.

This module tests the business logic and service operations
for component-related functionality.
"""

from unittest.mock import Mock

import pytest
from sqlalchemy.exc import IntegrityError

from backend.core.errors.exceptions import (
    ComponentNotFoundError,
    DataValidationError,
    DuplicateEntryError,
)
from backend.core.models.components import Component
from backend.core.repositories.component_repository import (
    ComponentCategoryRepository,
    ComponentRepository,
)
from backend.core.schemas.component_schemas import (
    ComponentCreateSchema,
    ComponentReadSchema,
    ComponentUpdateSchema,
)
from backend.core.services.component_service import ComponentService


class TestComponentService:
    """Test ComponentService business logic."""

    @pytest.fixture
    def mock_component_repository(self):
        """Create a mock ComponentRepository for testing."""
        mock_repo = Mock(spec=ComponentRepository)
        mock_repo.db_session = Mock()
        return mock_repo

    @pytest.fixture
    def mock_category_repository(self):
        """Create a mock ComponentCategoryRepository for testing."""
        mock_repo = Mock(spec=ComponentCategoryRepository)
        mock_repo.db_session = Mock()
        return mock_repo

    @pytest.fixture
    def component_service(self, mock_component_repository, mock_category_repository):
        """Create ComponentService instance with mocked dependencies."""
        return ComponentService(mock_component_repository, mock_category_repository)

    @pytest.fixture
    def sample_component_create_data(self):
        """Sample component creation data."""
        return ComponentCreateSchema(
            name="Circuit Breaker 20A",
            category_id=1,
            manufacturer="Schneider Electric",
            model="QO120",
            specific_data='{"rating_amps": 20, "voltage": 120, "poles": 1}',
            notes="Standard single-pole circuit breaker",
        )

    @pytest.fixture
    def sample_component_model(self):
        """Sample Component model instance."""
        from datetime import datetime, timezone

        component = Mock(spec=Component)
        component.id = 1
        component.name = "Circuit Breaker 20A"
        component.category_id = 1
        component.manufacturer = "Schneider Electric"
        component.model = "QO120"
        component.specific_data = '{"rating_amps": 20, "voltage": 120, "poles": 1}'
        component.notes = "Standard single-pole circuit breaker"
        component.is_deleted = False
        component.created_at = datetime.now(timezone.utc)
        component.updated_at = datetime.now(timezone.utc)
        component.deleted_at = None
        component.deleted_by_user_id = None
        return component

    def test_create_component_success(
        self,
        component_service,
        mock_component_repository,
        mock_category_repository,
        sample_component_create_data,
        sample_component_model,
    ):
        """Test successful component creation."""
        # Setup mocks
        mock_category_repository.get_by_id.return_value = Mock(
            is_deleted=False
        )  # Category exists
        mock_component_repository.get_by_name_and_category.return_value = (
            None  # No duplicate
        )
        mock_component_repository.create.return_value = sample_component_model

        # Execute
        result = component_service.create_component(sample_component_create_data)

        # Verify
        assert isinstance(result, ComponentReadSchema)
        assert result.name == "Circuit Breaker 20A"
        assert result.category_id == 1

        # Verify repository calls
        mock_category_repository.get_by_id.assert_called_once_with(1)
        mock_component_repository.get_by_name_and_category.assert_called_once_with(
            "Circuit Breaker 20A", 1
        )
        mock_component_repository.create.assert_called_once()
        mock_component_repository.db_session.commit.assert_called_once()

    def test_create_component_category_not_found(
        self,
        component_service,
        mock_component_repository,
        mock_category_repository,
        sample_component_create_data,
    ):
        """Test component creation with non-existent category."""
        # Setup mocks
        mock_category_repository.get_by_id.return_value = None  # Category doesn't exist

        # Execute and verify
        with pytest.raises(DataValidationError) as exc_info:
            component_service.create_component(sample_component_create_data)

        assert "category_id" in exc_info.value.metadata["validation_errors"]

        # Repository should not be called for creation
        mock_component_repository.create.assert_not_called()

    def test_create_component_duplicate_name(
        self,
        component_service,
        mock_component_repository,
        mock_category_repository,
        sample_component_create_data,
        sample_component_model,
    ):
        """Test component creation with duplicate name in category."""
        # Setup mocks
        mock_category_repository.get_by_id.return_value = Mock(
            is_deleted=False
        )  # Category exists
        mock_component_repository.get_by_name_and_category.return_value = (
            sample_component_model  # Duplicate exists
        )

        # Execute and verify
        with pytest.raises(DataValidationError) as exc_info:
            component_service.create_component(sample_component_create_data)

        assert "name" in exc_info.value.metadata["validation_errors"]

        # Repository should not be called for creation
        mock_component_repository.create.assert_not_called()

    def test_create_component_integrity_error(
        self,
        component_service,
        mock_component_repository,
        mock_category_repository,
        sample_component_create_data,
    ):
        """Test component creation with database integrity error."""
        # Setup mocks
        mock_category_repository.get_by_id.return_value = Mock(is_deleted=False)
        mock_component_repository.get_by_name_and_category.return_value = None

        # Setup mock to raise IntegrityError
        integrity_error = IntegrityError(
            "statement", "params", Exception("uq_component_name_category")
        )
        integrity_error.orig = Mock()
        integrity_error.orig.__str__ = Mock(return_value="uq_component_name_category")
        mock_component_repository.create.side_effect = integrity_error

        # Execute and verify
        with pytest.raises(DuplicateEntryError) as exc_info:
            component_service.create_component(sample_component_create_data)

        assert "Circuit Breaker 20A" in exc_info.value.detail

        # Verify rollback was called
        mock_component_repository.db_session.rollback.assert_called_once()

    def test_get_component_details_success(
        self, component_service, mock_component_repository, sample_component_model
    ):
        """Test successful component retrieval."""
        # Setup mocks
        mock_component_repository.get_by_id.return_value = sample_component_model

        # Execute
        result = component_service.get_component_details(1)

        # Verify
        assert isinstance(result, ComponentReadSchema)
        assert result.name == "Circuit Breaker 20A"
        assert result.id == 1

        mock_component_repository.get_by_id.assert_called_once_with(1)

    def test_get_component_details_not_found(
        self, component_service, mock_component_repository
    ):
        """Test component retrieval when component doesn't exist."""
        # Setup mocks
        mock_component_repository.get_by_id.return_value = None

        # Execute and verify
        with pytest.raises(ComponentNotFoundError):
            component_service.get_component_details(999)

        mock_component_repository.get_by_id.assert_called_once_with(999)

    def test_get_component_details_deleted(
        self, component_service, mock_component_repository, sample_component_model
    ):
        """Test component retrieval when component is soft deleted."""
        # Setup mocks
        sample_component_model.is_deleted = True
        mock_component_repository.get_by_id.return_value = sample_component_model

        # Execute and verify
        with pytest.raises(ComponentNotFoundError):
            component_service.get_component_details(1)

    def test_update_component_success(
        self,
        component_service,
        mock_component_repository,
        mock_category_repository,
        sample_component_model,
    ):
        """Test successful component update."""
        # Setup mocks
        mock_component_repository.get_by_id.return_value = sample_component_model
        mock_category_repository.get_by_id.return_value = Mock(
            is_deleted=False
        )  # Category exists
        mock_component_repository.get_by_name_and_category.return_value = (
            None  # No duplicate
        )

        update_data = ComponentUpdateSchema(
            name="Updated Circuit Breaker", manufacturer="Updated Manufacturer"
        )

        # Execute
        result = component_service.update_component(1, update_data)

        # Verify
        assert isinstance(result, ComponentReadSchema)

        # Verify repository calls
        mock_component_repository.get_by_id.assert_called_once_with(1)
        mock_component_repository.db_session.commit.assert_called_once()

    def test_update_component_not_found(
        self, component_service, mock_component_repository
    ):
        """Test component update when component doesn't exist."""
        # Setup mocks
        mock_component_repository.get_by_id.return_value = None

        update_data = ComponentUpdateSchema(name="Updated Name")

        # Execute and verify
        with pytest.raises(ComponentNotFoundError):
            component_service.update_component(999, update_data)

    def test_delete_component_success(
        self, component_service, mock_component_repository, sample_component_model
    ):
        """Test successful component deletion."""
        # Setup mocks
        mock_component_repository.get_by_id.return_value = sample_component_model

        # Execute
        component_service.delete_component(1, deleted_by_user_id=123)

        # Verify
        assert sample_component_model.is_deleted is True
        assert sample_component_model.deleted_by_user_id == 123
        assert sample_component_model.deleted_at is not None

        mock_component_repository.get_by_id.assert_called_once_with(1)
        mock_component_repository.db_session.commit.assert_called_once()

    def test_delete_component_not_found(
        self, component_service, mock_component_repository
    ):
        """Test component deletion when component doesn't exist."""
        # Setup mocks
        mock_component_repository.get_by_id.return_value = None

        # Execute and verify
        with pytest.raises(ComponentNotFoundError):
            component_service.delete_component(999)

    def test_get_components_list_success(
        self, component_service, mock_component_repository, sample_component_model
    ):
        """Test successful components list retrieval."""
        # Setup mocks
        mock_component_repository.get_active_components.return_value = [
            sample_component_model
        ]

        # Execute
        result = component_service.get_components_list(page=1, per_page=10)

        # Verify
        assert len(result.components) == 1
        assert result.components[0].name == "Circuit Breaker 20A"
        assert result.total == 1
        assert result.page == 1
        assert result.per_page == 10

        mock_component_repository.get_active_components.assert_called_once_with(
            skip=0, limit=10
        )

    def test_get_components_list_with_category_filter(
        self, component_service, mock_component_repository, sample_component_model
    ):
        """Test components list retrieval with category filter."""
        # Setup mocks
        mock_component_repository.get_by_category.return_value = [
            sample_component_model
        ]
        mock_component_repository.count_by_category.return_value = 1

        # Execute
        result = component_service.get_components_list(
            page=1, per_page=10, category_id=1
        )

        # Verify
        assert len(result.components) == 1
        assert result.total == 1

        mock_component_repository.get_by_category.assert_called_once_with(
            category_id=1, skip=0, limit=10
        )
        mock_component_repository.count_by_category.assert_called_once_with(1)

    def test_get_components_list_with_search(
        self, component_service, mock_component_repository, sample_component_model
    ):
        """Test components list retrieval with search term."""
        # Setup mocks
        mock_component_repository.search_components.return_value = [
            sample_component_model
        ]
        mock_component_repository.get_active_components.return_value = [
            sample_component_model
        ]

        # Execute
        result = component_service.get_components_list(
            page=1, per_page=10, search_term="circuit"
        )

        # Verify
        assert len(result.components) == 1

        mock_component_repository.search_components.assert_called_once_with(
            search_term="circuit", category_id=None, manufacturer=None, skip=0, limit=10
        )
