# How-To: Configure the Main Application Entry Point (main.py)

This document explains how the `main.py` file is structured using Typer to provide a command-line interface for running and managing the application.

We'll use `Typer` as a lightweight yet powerful tool for building a command-line interface (CLI), which is excellent for orchestrating different application modes (like running the web server, performing migrations, or seeding data).

First, ensure you have `uvicorn` and `typer` installed:
```bash
pip install uvicorn "typer[all]"
```

---

**Explanation and Setup Notes:**

1.  **`sys.path.insert(0, ...)`**: This line is important if you intend to run `main.py` directly from your project root (e.g., `python main.py run`). It ensures that Python can find modules like `src.app` or `src.config` correctly. Without it, you might get `ModuleNotFoundError`.
2.  **`cli_app = typer.Typer(...)`**: Initializes the Typer application.
3.  **`setup_logging()`**: Calls your centralized logging configuration. This should happen early.
4.  **`@cli_app.command()` decorators**: These turn Python functions into CLI subcommands.
    *   **`run` command**:
        *   Takes optional `host`, `port`, and `reload` arguments, with defaults from the [Configuration Layer](../config/config-architecture.md).
        *   Uses `uvicorn.run(app, ...)` to start the [FastAPI server](../api/api-architecture.md).

        See the [API Layer Architecture](../api/api-architecture.md) and [Lifespan Events Architecture](../lifespan-architecture.md) for more details on application startup.

        *   `log_level=[settings](../config/config-architecture.md).LOG_LEVEL.lower()` ensures Uvicorn's logging matches your app's [configuration](how-to_config.md).

    *   **`migrate` command**:
        *   A placeholder for your [database migration logic](../core/database/migrations-architecture.md) (e.g., using Alembic).

        See the [Database Migrations Architecture](../core/database/migrations-architecture.md) for more details.

        You would integrate `alembic.command.upgrade` here. `get_alembic_config` is a placeholder for retrieving the path to your `alembic.ini`.
    *   **`seed-data` command**:
        *   A placeholder for populating your [database](../core/database/architecture.md) with initial data (e.g., default material properties, standard components).

        See the [Database Seeding Architecture](../core/database/seeding-architecture.md) for more details.

        You'd call a function from your [service layer](../core/services/services-architecture.md) here.
    *   **`create-superuser` command**:
        *   A useful command for administrative setup, allowing you to create an initial user with elevated privileges. This would interact with your [User Service](../core/services/services-architecture.md).

5.  **`if __name__ == "__main__":`**: The standard Python entry point. It calls `cli_app()` to parse command-line arguments and execute the corresponding command.

**How to Run:**

From your project root directory (the directory containing `src/`):

*   **To run the web server:**
    ```bash
    python main.py run
    ```
    or, for development with auto-reload:
    ```bash
    python main.py run --reload
    ```
    (Note: `reload` default value often comes from [`settings.DEBUG`](../config/config-architecture.md), so `--reload` might not be necessary if `DEBUG=True` in your `.env`)

*   **To run database migrations (placeholder):**
    ```bash
    python main.py migrate
    ```

*   **To seed initial data (placeholder):**
    ```bash
    python main.py seed-data
    ```

*   **To create a superuser (placeholder):**
    ```bash
    python main.py create-superuser --username admin --password securepass --email <EMAIL>
    ```

This `main.py` provides a clean and powerful entry point for your application, separating the concerns of application definition from execution and orchestration.
