### Telemetry Architecture Specification

**1. Purpose & Role**
The Telemetry Layer provides comprehensive **observability** for the Heat Tracing Design Application. Its purpose is to collect, process, and export application telemetry data (traces, metrics, and [logs](../../how-to/how-to_logging.md)) to enable:
*   **Distributed Tracing:** Understanding the end-to-end flow of [requests](../../../api/api-architecture.md) across different components and identifying latency bottlenecks.
*   **Performance & Usage Monitoring:** Tracking application performance indicators ([latency](../../../api/api-architecture.md), error rates, resource utilization) and key business metrics.
*   **Enhanced Logging:** Enriching traditional [logs](../../how-to/how-to_logging.md) with contextual information (like trace and span IDs) for easier debugging.
*   **Root Cause Analysis:** Accelerating the identification and resolution of issues by providing deep insights into application behavior.

**2. Location (`src/core/telemetry/telemetry_manager.py`)**
The core initialization and management of OpenTelemetry components will reside in `src/core/telemetry/telemetry_manager.py`. Specific instrumentation logic might be applied at relevant points across other layers (e.g., [API handlers](../../../api/api-architecture.md), [services](../../core/services/services-architecture.md)).

**3. Framework/Library Choice (OpenTelemetry Python SDK)**
*   **OpenTelemetry Python SDK:** The industry-standard vendor-neutral open-source project for collecting telemetry data.
*   **Justification:** Provides a unified API for traces, metrics, and [logs](../../how-to/how-to_logging.md), extensive automatic instrumentation libraries, and flexibility in choosing backend analysis tools (e.g., Jaeger, Prometheus, Grafana, ELK Stack, commercial APMs).

**4. Core Responsibilities & Functionality**

*   **Instrumentation Bootstrap (`telemetry_manager.py`):**
    *   **Resource Definition:** Define common attributes for the service instance (e.g., `service.name`, `service.version`, `host.name`) that apply to all generated telemetry.
    *   **Provider Setup:** Initialize the OpenTelemetry `TracerProvider`, `MeterProvider`, and `LoggerProvider`.
    *   **Exporter Configuration:** Configure the destination(s) for telemetry data. Common choices include:
        *   **OTLP Exporter:** Recommended for sending data to an OpenTelemetry Collector.
        *   **Console Exporter:** For local development/debugging.
        *   **File Exporter:** For persisting telemetry locally in an offline scenario.
    *   **Sampler Configuration:** Define how traces are sampled (e.g., always sample, probabilistic sampling).
    *   **Automatic Instrumentation:** Apply out-of-the-box instrumentation for common libraries used in the application (e.g., [FastAPI](../../../api/api-architecture.md)/`Starlette`, [SQLAlchemy](../database/architecture.md), `HTTPX`/`requests`, `Redis`).
*   **Tracing (`TracerProvider`):**
    *   **Distributed Tracing:** Generates traces composed of spans that represent operations within a [request's lifecycle](../../lifespan-architecture.md).
    *   **Context Propagation:** Ensures trace and span IDs are propagated automatically across process boundaries (e.g., HTTP headers for external calls) and asynchronously within the application.
    *   **Span Management:**
        *   **Automatic Spans:** Generated for [HTTP requests](../../../api/api-architecture.md), [database queries](../database/architecture.md), and other instrumented library calls.
        *   **Manual Spans:** Developers can manually create custom spans around critical business logic within the [Service Layer](../services/services-architecture.md) to provide granular visibility into specific operations and their durations.
        *   **Span Attributes:** Attach relevant contextual information (e.g., user ID, project ID, specific calculation parameters) to spans.
*   **Metrics (`MeterProvider`):**
    *   **Instrumentation:** Define and register various instrument types (Counter, Gauge, Histogram, Sum) to collect numerical data.
    *   **Custom Metrics:** Collect application-specific metrics, such as:
        *   `projects_created_total` (Counter)
        *   `heat_loss_calculation_duration_seconds` (Histogram)
        *   `active_users` (Gauge)
        *   [**API request latency**](../../../api/api-architecture.md) (Histogram, often covered by auto-instrumentation)
    *   **Aggregators & Exporters:** Configure how metrics are aggregated and sent to monitoring backends.
*   **Logging (`LoggerProvider`):**
    *   **Contextual Logging:** Integrates with Python's standard `logging` module to automatically inject current trace and span IDs into log records.
    *   **Structured Logging:** Encourages structured logging practices to make [logs](../../how-to/how-to_logging.md) more easily parsable and queryable in log management systems.

**5. Interaction with Other Layers**

*   **Application Root (`src/app.py`):**
    *   The OpenTelemetry SDK will be initialized during the [**lifespan startup event**](../../lifespan-architecture.md), including setting up providers and exporters.
    *   Automatic instrumentation for the [FastAPI application](../../../api/api-architecture.md) will be applied here.
*   **Middleware Layer (`src/middleware/`):**
    *   [FastAPI](../../../api/api-architecture.md)/Starlette auto-instrumentation will cover incoming [HTTP requests](../../../api/api-architecture.md).
    *   Manual spans can be created within custom [middleware](../../middleware/middleware-architecture.md) (e.g., around authentication checks) if deeper tracing is required there.

    See the [Middleware Layer Architecture](../../middleware/middleware-architecture.md) for more details.

*   **API Layer ([`src/api/`](../../../api/api-architecture.md)):**
    *   [HTTP request handling](../../../api/api-architecture.md) is automatically traced.
    *   Developers can add manual spans around specific [handler logic](../../../api/api-architecture.md) if a single endpoint performs multiple distinct business steps.

    See the [API Layer Architecture](../../../api/api-architecture.md) for more details.

*   **Service Layer ([`src/core/services/`](../services/services-architecture.md)):**
    *   This is a primary area for **manual span creation** to trace the execution of core business logic and complex [calculations](../calculations/calculations-architecture.md).
    *   **Custom metrics** related to business operations (e.g., [calculation times](../calculations/calculations-architecture.md#3-core-responsibilities--functionality), feature usage) will be emitted from here.

    See the [Service Layer Architecture](../services/services-architecture.md) and [Calculations Layer Architecture](../calculations/calculations-architecture.md) for more details.

*   **Repository Layer ([`src/core/repositories/`](../repositories/repositories-architecture.md)):**
    *   [SQLAlchemy instrumentation](../database/architecture.md) will automatically trace [database queries](../database/architecture.md).
    *   Manual spans can be added for complex [repository operations](../repositories/repositories-architecture.md) (e.g., bulk inserts, custom ORM queries).

    See the [Repository Layer Architecture](../repositories/repositories-architecture.md) and [Database Layer Architecture](../database/architecture.md) for more details.

*   **Logging Layer (`src/core/logging_config.py`):** The OpenTelemetry [logging integration](../../how-to/how-to_logging.md) will enrich all [logs](../../how-to/how-to_logging.md) generated by `app_logger` with trace context.

    See the [How-To: Configure Logging](../../how-to/how-to_logging.md) for more details.

**6. Key Principles**

*   **Low Overhead:** Instrumentation should have minimal impact on application performance.
*   **Standardization:** Adheres strictly to OpenTelemetry specifications to ensure vendor neutrality and future compatibility with various observability backends.
*   **Context Propagation:** Ensures that the tracing context (trace ID, span ID) flows seamlessly through all components involved in a [request](../../../api/api-architecture.md).
*   **Actionable Data:** Focuses on collecting metrics and traces that provide clear, actionable insights into application health, performance, and user behavior.
*   **Conditional Enablement:** The entire telemetry system can be enabled or disabled via [configuration](../config/config-architecture.md) (`settings.TELEMETRY_ENABLED`), allowing for fine-grained control across different environments.
