from sqlalchemy import Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base, CommonColumns, EnumType, SoftDeleteColumns

# from .documents import ExportedDocument, ImportedDataRevision
# from .electrical import CableRoute, ElectricalNode
from .enums import InstallationEnvironment

# from .heat_tracing import Pipe, Vessel
# from .switchboard import Switchboard


class Project(CommonColumns, SoftDeleteColumns, Base):
    __tablename__ = "Project"

    project_number: Mapped[str] = mapped_column(
        nullable=False
    )  # REMOVE UniqueConstraint from here
    description: Mapped[str | None] = mapped_column(Text, nullable=True)
    designer: Mapped[str | None] = mapped_column(nullable=True)
    min_ambient_temp_c: Mapped[float] = mapped_column(nullable=False)
    max_ambient_temp_c: Mapped[float] = mapped_column(nullable=False)
    desired_maintenance_temp_c: Mapped[float] = mapped_column(nullable=False)
    wind_speed_ms: Mapped[float | None] = mapped_column(nullable=True)
    installation_environment: Mapped[InstallationEnvironment | None] = mapped_column(
        EnumType(InstallationEnvironment), nullable=True
    )
    available_voltages_json: Mapped[str | None] = mapped_column(nullable=True)

    default_cable_manufacturer: Mapped[str | None] = mapped_column(nullable=True)
    default_control_device_manufacturer: Mapped[str | None] = mapped_column(
        nullable=True
    )

    # Relationships (using string forward references for models defined in other files)
    switchboards: Mapped[list["Switchboard"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    pipes: Mapped[list["Pipe"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    vessels: Mapped[list["Vessel"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    imported_data_revisions: Mapped[list["ImportedDataRevision"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    exported_documents: Mapped[list["ExportedDocument"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    electrical_nodes: Mapped[list["ElectricalNode"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    cable_routes: Mapped[list["CableRoute"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    cable_segments: Mapped[list["CableSegment"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    load_calculations: Mapped[list["LoadCalculation"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )
    voltage_drop_calculations: Mapped[list["VoltageDropCalculation"]] = relationship(
        back_populates="project", cascade="all, delete-orphan"
    )

    __table_args__ = (
        UniqueConstraint("name", name="uq_project_name"),
        UniqueConstraint(
            "project_number", name="uq_project_number"
        ),  # ADD UniqueConstraint here
    )

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', project_number='{self.project_number}')>"


# Model-level validation using SQLAlchemy event listeners
import json
import logging

from sqlalchemy import event

from ..errors.exceptions import DataValidationError

logger = logging.getLogger(__name__)


def validate_project_data(mapper, connection, target):
    """
    Validate project data before insert/update.

    Args:
        mapper: SQLAlchemy mapper
        connection: Database connection
        target: Project instance being validated

    Raises:
        DataValidationError: If validation fails
    """
    errors = []

    # Validate temperature ranges
    if target.min_ambient_temp_c >= target.max_ambient_temp_c:
        errors.append(
            {
                "loc": ["min_ambient_temp_c", "max_ambient_temp_c"],
                "msg": "Minimum ambient temperature must be less than maximum ambient temperature",
                "type": "value_error",
            }
        )

    # Validate maintenance temperature
    if target.desired_maintenance_temp_c <= target.max_ambient_temp_c:
        errors.append(
            {
                "loc": ["desired_maintenance_temp_c"],
                "msg": "Desired maintenance temperature must be greater than maximum ambient temperature",
                "type": "value_error",
            }
        )

    # Validate temperature ranges are reasonable
    if target.min_ambient_temp_c < -100 or target.min_ambient_temp_c > 100:
        errors.append(
            {
                "loc": ["min_ambient_temp_c"],
                "msg": "Minimum ambient temperature must be between -100°C and 100°C",
                "type": "value_error",
            }
        )

    if target.max_ambient_temp_c < -50 or target.max_ambient_temp_c > 150:
        errors.append(
            {
                "loc": ["max_ambient_temp_c"],
                "msg": "Maximum ambient temperature must be between -50°C and 150°C",
                "type": "value_error",
            }
        )

    if target.desired_maintenance_temp_c < 0 or target.desired_maintenance_temp_c > 500:
        errors.append(
            {
                "loc": ["desired_maintenance_temp_c"],
                "msg": "Desired maintenance temperature must be between 0°C and 500°C",
                "type": "value_error",
            }
        )

    # Validate wind speed
    if target.wind_speed_ms is not None and target.wind_speed_ms < 0:
        errors.append(
            {
                "loc": ["wind_speed_ms"],
                "msg": "Wind speed cannot be negative",
                "type": "value_error",
            }
        )

    # Validate available voltages JSON
    if target.available_voltages_json:
        try:
            voltages = json.loads(target.available_voltages_json)
            if not isinstance(voltages, list):
                errors.append(
                    {
                        "loc": ["available_voltages_json"],
                        "msg": "Available voltages must be a JSON array",
                        "type": "value_error",
                    }
                )
            else:
                # Validate voltage values
                for voltage in voltages:
                    if not isinstance(voltage, (int, float)) or voltage <= 0:
                        errors.append(
                            {
                                "loc": ["available_voltages_json"],
                                "msg": "All voltage values must be positive numbers",
                                "type": "value_error",
                            }
                        )
                        break
        except json.JSONDecodeError:
            errors.append(
                {
                    "loc": ["available_voltages_json"],
                    "msg": "Available voltages must be valid JSON",
                    "type": "value_error",
                }
            )

    # Validate project name and number are not empty
    if not target.name or not target.name.strip():
        errors.append(
            {
                "loc": ["name"],
                "msg": "Project name cannot be empty",
                "type": "value_error",
            }
        )

    if not target.project_number or not target.project_number.strip():
        errors.append(
            {
                "loc": ["project_number"],
                "msg": "Project number cannot be empty",
                "type": "value_error",
            }
        )

    # If there are validation errors, raise exception
    if errors:
        logger.warning(f"Project validation failed: {len(errors)} errors")
        raise DataValidationError(details={"validation_errors": errors})

    logger.debug(f"Project validation passed for: {target.name}")


# Register event listeners for Project model validation
@event.listens_for(Project, "before_insert")
def before_insert_listener(mapper, connection, target):
    """Validate project data before insert."""
    validate_project_data(mapper, connection, target)


@event.listens_for(Project, "before_update")
def before_update_listener(mapper, connection, target):
    """Validate project data before update."""
    validate_project_data(mapper, connection, target)
