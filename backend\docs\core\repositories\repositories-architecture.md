### Repositories Layer Architecture Specification

**1. Purpose & Role**
The Repositories Layer acts as an abstraction over the data persistence mechanism (SQLAlchemy ORM in our case). Its primary purposes are to:
*   **Decouple Business Logic from Data Access:** Provide a clean, consistent interface for the [Service Layer](../services/services-architecture.md) to interact with data, without exposing the underlying database or ORM specifics (e.g., SQL syntax, SQLAlchemy session management details).
*   **Centralize Data Access Logic:** Encapsulate all direct database CRUD (Create, Read, Update, Delete) operations for specific domain entities.
*   **Translate Database Exceptions:** Catch lower-level, database-specific exceptions (e.g., SQLAlchemy's `IntegrityError` or `NoResultFound`) and translate them into generic, [application-specific exceptions](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy) (e.g., `DuplicateEntryError`, `NotFoundError`) defined in our `src/core/errors/exceptions.py`. This shields higher layers from database implementation details.
*   **Enhance Testability:** By abstracting data access, the [Service Layer](../services/services-architecture.md) can be tested independently of the actual database by mocking repository interfaces.

**2. Location & Structure (`src/core/repositories/`)**
The Repositories Layer will be organized within `src/core/repositories/`, following a modular structure where each significant domain entity (or aggregate root) has its own repository.

```
src/core/repositories/
├── __init__.py
├── base_repository.py      # Generic base class for common CRUD operations
├── project_repository.py   # Repository for Project entities
├── component_repository.py # Repository for various Component entities (e.g., Pipe, Insulation)
├── heat_tracing_repository.py # Repository for Circuit and related heat tracing data
├── electrical_repository.py   # Repository for Electrical Load, Cable entities
├── switchboard_repository.py  # Repository for DistributionBoard, Feeder entities
├── user_repository.py      # Repository for User entities
└── document_repository.py  # Repository for managing generated documents/reports
```

**3. Core Responsibilities & Functionality**

*   **CRUD Operations:** Implement standard Create, Read, Update, and Delete operations for its specific domain entity.
*   **Query Construction:** Build and execute database queries using SQLAlchemy's ORM capabilities, often including filtering, sorting, pagination, and eager loading of related objects.
*   **Session Management (Scoped Usage):** Repositories receive a SQLAlchemy `Session` instance (typically via [Dependency Injection](../../../dependency-management-architecture.md) from the [Database Layer](../database/architecture.md)) and perform all database operations within the scope of this session. [Repositories](repositories-architecture.md) themselves **do not** manage session creation or closing; they rely on the caller ([Service Layer](../services/services-architecture.md)) to provide and manage the session's lifecycle.
*   **Exception Translation:** Crucially, a repository is responsible for catching exceptions raised by SQLAlchemy or the underlying database driver and re-raising them as appropriate [application-level exceptions](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy). This is vital for maintaining the database-agnostic nature of the [Service Layer](../services/services-architecture.md).

    For a detailed explanation of error handling, see the [Error and Exception Handling Architecture](../../errors/errors-architecture.md).

*   **Complex Query Logic:** For queries that go beyond simple CRUD and involve joins, subqueries, or intricate filtering, the repository is the place to encapsulate this complexity.

**4. Key Components/Concepts**

*   **`base_repository.py`:**
    *   Defines a generic `BaseRepository` class that provides common CRUD methods (e.g., `get_by_id`, `get_all`, `create`, `update`, `delete`).
    *   This class takes a SQLAlchemy `Session` and the [ORM `Model` class](../models/models-architecture.md) as dependencies.
    *   It handles common `try-except` blocks for database errors, translating them to `DatabaseError` or `NotFoundError` from [`src/core/errors/exceptions.py`](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy).

    ```python
    # src/core/repositories/base_repository.py
    from typing import TypeVar, Generic, Type, Optional, List, Dict, Any
    from sqlalchemy.orm import Session
    from sqlalchemy import select, func
    from sqlalchemy.exc import IntegrityError, NoResultFound, SQLAlchemyError

    from backend.core.models.base import Base # Assuming Base is defined here
    from backend.core.errors.exceptions import NotFoundError, DatabaseError, DuplicateEntryError

    # Define a generic type for SQLAlchemy models
    ModelType = TypeVar("ModelType", bound=Base)

    class BaseRepository(Generic[ModelType]):
        def __init__(self, db_session: Session, model: Type[ModelType]):
            self.db_session = db_session
            self.model = model

        def _handle_db_exception(self, e: SQLAlchemyError, entity_name: str = "resource") -> None:
            if isinstance(e, IntegrityError):
                raise DuplicateEntryError(
                    message=f"A {entity_name} with the given unique constraint already exists.",
                    original_exception=e
                )
            elif isinstance(e, NoResultFound):
                raise NotFoundError(
                    resource_name=entity_name,
                    message=f"{entity_name.capitalize()} not found."
                )
            else:
                raise DatabaseError(
                    reason=f"An unexpected database error occurred during {entity_name} operation.",
                    original_exception=e
                ) from e

        def get_by_id(self, item_id: int) -> Optional[ModelType]:
            try:
                stmt = select(self.model).where(self.model.id == item_id)
                return self.db_session.scalar(stmt)
            except SQLAlchemyError as e:
                self._handle_db_exception(e, self.model.__tablename__)

        def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
            try:
                stmt = select(self.model).offset(skip).limit(limit)
                return list(self.db_session.scalars(stmt).all())
            except SQLAlchemyError as e:
                self._handle_db_exception(e, self.model.__tablename__)

        def create(self, data: Dict[str, Any]) -> ModelType:
            try:
                item = self.model(**data)
                self.db_session.add(item)
                # Note: db_session.commit() is typically done by the service layer,
                # but for simplicity, we might do it here if it's a single operation
                # self.db_session.commit()
                # self.db_session.refresh(item)
                return item
            except SQLAlchemyError as e:
                self._handle_db_exception(e, self.model.__tablename__)

        # ... other common CRUD methods like update, delete, filter, etc.
    ```

*   **Specific Repositories:**
    *   Inherit from `BaseRepository`.
    *   Implement methods for domain-specific queries that go beyond generic CRUD (e.g., `get_project_by_code`, `get_active_circuits_for_project`).
    *   These methods also wrap database exceptions and translate them.

    ```python
    # src/core/repositories/project_repository.py
    from sqlalchemy.orm import Session
    from sqlalchemy import select
    from sqlalchemy.exc import NoResultFound, SQLAlchemyError

    from backend.core.models.project import Project # Assuming Project model
    from backend.core.repositories.base_repository import BaseRepository
    from backend.core.errors.exceptions import ProjectNotFoundError, DatabaseError

    class ProjectRepository(BaseRepository[Project]):
        def __init__(self, db_session: Session):
            super().__init__(db_session, Project)

        def get_by_code(self, code: str) -> Project:
            try:
                stmt = select(self.model).where(self.model.code == code)
                return self.db_session.scalars(stmt).one() # Use .one() for expected single result
            except NoResultFound:
                raise ProjectNotFoundError(project_id=code) # Specific error for clarity
            except SQLAlchemyError as e:
                self._handle_db_exception(e, "project") # Uses base handler for other DB errors

        # Example of a custom query with related objects
        def get_project_with_circuits(self, project_id: int) -> Optional[Project]:
            try:
                # Eagerly load circuits to avoid N+1 problem
                stmt = select(self.model).where(self.model.id == project_id).options(selectinload(self.model.circuits))
                return self.db_session.scalar(stmt)
            except SQLAlchemyError as e:
                self._handle_db_exception(e, "project")
    ```

**5. Interaction with Other Layers**

*   **Model Layer ([`src/core/models/`](../models/models-architecture.md)):** [Repositories](repositories-architecture.md) operate directly on the [SQLAlchemy ORM models](../models/models-architecture.md). They are the layer that instantiates, queries, and manipulates these model objects.
*   **Database Layer ([`src/core/database/`](../database/architecture.md)):** [Repositories](repositories-architecture.md) directly depend on the [Database Layer](../database/architecture.md) to provide active SQLAlchemy `Session` instances. They do not manage the lifecycle of the session; they simply use the session provided to them.
*   **Service Layer ([`src/core/services/`](../services/services-architecture.md) - Primary Consumer):** The [Service Layer](../services/services-architecture.md) is the **sole consumer** of the [Repositories](repositories-architecture.md). [Services](../services/services-architecture.md) call repository methods to perform all database operations (e.g., `project_service.create_project` calls `project_repository.create`). The [Service Layer](../services/services-architecture.md) is also responsible for orchestrating transactions across multiple repository calls (`db_session.commit()`, `db_session.rollback()`).

    For more details on service methods and transaction orchestration, see the [Service Layer Architecture](../services/services-architecture.md) and [Transaction Management Architecture](../database/transaction-architecture.md).

*   **Error Handling Layer ([`src/core/errors/`](../../errors/errors-architecture.md)):** [Repositories](repositories-architecture.md) actively use [custom exceptions](../../errors/errors-architecture.md#2-custom-exceptions-srccoreerrorsexceptionspy) from `src/core/errors/exceptions.py` to translate database-specific errors into application-meaningful errors. This ensures a consistent error reporting mechanism throughout the application.

**6. Key Principles**

*   **Single Responsibility Principle (SRP):** Each repository is responsible for persistence operations of a single aggregate or domain entity. It doesn't contain business logic.
*   **Testability:** By abstracting database interactions, the [Service Layer](../services/services-architecture.md) (which contains the business logic) can be easily tested using mocked repository implementations, without needing a live database.
*   **Database Agnostic (for Service Layer):** The [Service Layer](../services/services-architecture.md) does not need to know whether the data is stored in a relational database, a NoSQL database, or even a file system, as long as the repository interface is consistent.
*   **Explicit Dependencies:** [Repositories](repositories-architecture.md) clearly declare their dependency on a database session, making them easy to instantiate and test.
*   **ORM Encapsulation:** All SQLAlchemy-specific query building and session interactions are contained within this layer, preventing them from leaking into the [Service Layer](../services/services-architecture.md).

This completes the detailed specification for the Repositories Layer, solidifying the data access abstraction within your application's architecture.
