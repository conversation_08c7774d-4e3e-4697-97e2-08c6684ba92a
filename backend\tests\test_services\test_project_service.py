# backend/tests/test_services/test_project_service.py
"""
Unit tests for ProjectService.

Tests business logic layer functionality including CRUD operations,
validation, error handling, and orchestration.
"""

from datetime import datetime, timezone
from unittest.mock import Mock

import pytest

from backend.core.errors.exceptions import (
    DataValidationError,
    Du<PERSON>EntryError,
    ProjectNotFoundError,
)
from backend.core.models.project import Project
from backend.core.repositories.project_repository import ProjectRepository
from backend.core.schemas.project_schemas import (
    ProjectCreateSchema,
    ProjectListResponseSchema,
    ProjectReadSchema,
    ProjectUpdateSchema,
)
from backend.core.services.project_service import ProjectService


class TestProjectService:
    """Test ProjectService functionality."""

    @pytest.fixture
    def mock_repository(self):
        """Create a mock ProjectRepository for testing."""
        mock_repo = Mock(spec=ProjectRepository)
        mock_repo.db_session = Mock()
        return mock_repo

    @pytest.fixture
    def project_service(self, mock_repository):
        """Create a ProjectService instance with mocked repository."""
        return ProjectService(mock_repository)

    @pytest.fixture
    def sample_project_create_schema(self, sample_project_create_data):
        """Create a ProjectCreateSchema for testing."""
        return ProjectCreateSchema(**sample_project_create_data)

    @pytest.fixture
    def sample_project_update_schema(self, sample_project_update_data):
        """Create a ProjectUpdateSchema for testing."""
        return ProjectUpdateSchema(**sample_project_update_data)

    @pytest.fixture
    def mock_project_orm(self, sample_project_data):
        """Create a mock Project ORM object."""
        project = Mock(spec=Project)
        project.id = 1
        project.name = sample_project_data["name"]
        project.project_number = sample_project_data["project_number"]
        project.description = sample_project_data["description"]
        project.designer = sample_project_data["designer"]
        project.notes = sample_project_data["notes"]
        project.min_ambient_temp_c = sample_project_data["min_ambient_temp_c"]
        project.max_ambient_temp_c = sample_project_data["max_ambient_temp_c"]
        project.desired_maintenance_temp_c = sample_project_data[
            "desired_maintenance_temp_c"
        ]
        project.wind_speed_ms = sample_project_data["wind_speed_ms"]
        project.installation_environment = sample_project_data[
            "installation_environment"
        ]
        project.available_voltages_json = sample_project_data["available_voltages_json"]
        project.default_cable_manufacturer = sample_project_data[
            "default_cable_manufacturer"
        ]
        project.default_control_device_manufacturer = sample_project_data[
            "default_control_device_manufacturer"
        ]
        project.created_at = datetime.now(timezone.utc)
        project.updated_at = datetime.now(timezone.utc)
        project.is_deleted = False
        project.deleted_at = None
        project.deleted_by_user_id = None
        return project

    def test_create_project_success(
        self,
        project_service,
        mock_repository,
        sample_project_create_schema,
        mock_project_orm,
    ):
        """Test successful project creation."""
        # Setup mock
        mock_repository.create.return_value = mock_project_orm
        mock_repository.db_session.commit.return_value = None
        mock_repository.db_session.refresh.return_value = None

        # Execute
        result = project_service.create_project(sample_project_create_schema)

        # Verify
        assert isinstance(result, ProjectReadSchema)
        assert result.name == sample_project_create_schema.name
        assert result.project_number == sample_project_create_schema.project_number

        # Verify repository was called correctly
        mock_repository.create.assert_called_once()
        mock_repository.db_session.commit.assert_called_once()
        mock_repository.db_session.refresh.assert_called_once_with(mock_project_orm)

    def test_create_project_validation_error(self, project_service, mock_repository):
        """Test project creation with validation error."""
        # Create a valid schema but test service-level validation
        valid_data = {
            "name": "Test Project",
            "project_number": "TEST-001",
            "min_ambient_temp_c": -20.0,
            "max_ambient_temp_c": 40.0,
            "desired_maintenance_temp_c": 30.0,  # Invalid: maintenance temp <= max ambient
        }

        schema = ProjectCreateSchema(**valid_data)

        # Execute and verify - service should catch this business rule violation
        with pytest.raises(DataValidationError) as exc_info:
            project_service.create_project(schema)

        assert "maintenance_temperature" in exc_info.value.metadata["validation_errors"]

        # Repository should not be called
        mock_repository.create.assert_not_called()

    def test_create_project_duplicate_name_error(
        self, project_service, mock_repository, sample_project_create_schema
    ):
        """Test project creation with duplicate name."""
        from sqlalchemy.exc import IntegrityError

        # Setup mock to raise IntegrityError
        integrity_error = IntegrityError(
            "statement", "params", Exception("uq_project_name")
        )
        integrity_error.orig = Mock()
        integrity_error.orig.__str__ = Mock(return_value="uq_project_name")
        mock_repository.create.side_effect = integrity_error
        mock_repository.db_session.rollback.return_value = None

        # Execute and verify
        with pytest.raises(DuplicateEntryError) as exc_info:
            project_service.create_project(sample_project_create_schema)

        assert "name" in str(exc_info.value).lower()
        mock_repository.db_session.rollback.assert_called_once()

    def test_get_project_details_by_id(
        self, project_service, mock_repository, mock_project_orm
    ):
        """Test getting project details by numeric ID."""
        # Setup mock
        mock_repository.get_by_id.return_value = mock_project_orm

        # Execute
        result = project_service.get_project_details("1")

        # Verify
        assert isinstance(result, ProjectReadSchema)
        assert result.id == mock_project_orm.id
        mock_repository.get_by_id.assert_called_once_with(1)

    def test_get_project_details_by_code(
        self, project_service, mock_repository, mock_project_orm
    ):
        """Test getting project details by project number."""
        # Setup mock
        mock_repository.get_by_id.return_value = None
        mock_repository.get_by_code.return_value = mock_project_orm

        # Execute
        result = project_service.get_project_details("HT-TEST-001")

        # Verify
        assert isinstance(result, ProjectReadSchema)
        assert result.id == mock_project_orm.id
        mock_repository.get_by_code.assert_called_once_with("HT-TEST-001")

    def test_get_project_details_not_found(self, project_service, mock_repository):
        """Test getting project details when project doesn't exist."""
        # Setup mock
        mock_repository.get_by_id.return_value = None
        mock_repository.get_by_code.side_effect = ProjectNotFoundError("TEST-001")

        # Execute and verify
        with pytest.raises(ProjectNotFoundError):
            project_service.get_project_details("TEST-001")

    def test_update_project_success(
        self,
        project_service,
        mock_repository,
        sample_project_update_schema,
        mock_project_orm,
    ):
        """Test successful project update."""
        # Setup mock
        mock_repository.get_by_id.return_value = mock_project_orm
        mock_repository.get_by_code.return_value = None
        mock_repository.db_session.commit.return_value = None
        mock_repository.db_session.refresh.return_value = None

        # Execute
        result = project_service.update_project("1", sample_project_update_schema)

        # Verify
        assert isinstance(result, ProjectReadSchema)
        mock_repository.db_session.commit.assert_called_once()

    def test_delete_project_success(
        self, project_service, mock_repository, mock_project_orm
    ):
        """Test successful project deletion."""
        # Setup mock
        mock_repository.get_by_id.return_value = mock_project_orm
        mock_repository.get_by_code.return_value = None
        mock_repository.db_session.commit.return_value = None

        # Execute
        project_service.delete_project("1", deleted_by_user_id=123)

        # Verify
        assert mock_project_orm.is_deleted is True
        assert mock_project_orm.deleted_at is not None
        assert mock_project_orm.deleted_by_user_id == 123
        mock_repository.db_session.commit.assert_called_once()

    def test_get_projects_list_success(self, project_service, mock_repository):
        """Test getting projects list with pagination."""
        # Setup mock data
        mock_projects = []
        for i in range(5):
            project = Mock(spec=Project)
            project.id = i + 1
            project.name = f"Test Project {i + 1}"
            project.project_number = f"TEST-{i + 1:03d}"
            project.description = f"Description {i + 1}"
            project.designer = f"Engineer {i + 1}"
            project.created_at = datetime.now(timezone.utc)
            project.updated_at = datetime.now(timezone.utc)
            project.is_deleted = False
            mock_projects.append(project)

        # Setup mock repository
        mock_repository.get_all.return_value = mock_projects

        # Execute
        result = project_service.get_projects_list(page=1, per_page=10)

        # Verify
        assert isinstance(result, ProjectListResponseSchema)
        assert len(result.projects) == 5
        assert result.total == 5
        assert result.page == 1
        assert result.per_page == 10
        assert result.total_pages == 1
