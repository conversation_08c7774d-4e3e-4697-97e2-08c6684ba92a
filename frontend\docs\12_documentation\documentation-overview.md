## **Documentation**

### **Code Documentation**

* **JSDoc/TypeDoc:** Use JSDoc comments for documenting public APIs of functions, components, and hooks, especially their props, return values, and side effects. This facilitates auto-generated documentation and IDE autocompletion.
* **Inline Comments:** Use sparingly, primarily to explain *why* a piece of code exists or *how* a complex algorithm works, rather than *what* it does.

### **Project Documentation**

* **README.md:** Comprehensive project README with setup instructions, development scripts, and project overview.
* **Architectural Decision Records (ADRs):** Document significant architectural decisions, their rationale, alternatives considered, and consequences.
* **Wiki/Confluence:** Maintain a centralized knowledge base for broader architectural overviews, deployment guides, and team conventions.

### **API Documentation**

* Rely on the backend's OpenAPI/Swagger documentation as the single source of truth for API contracts.
