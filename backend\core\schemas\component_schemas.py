# backend/core/schemas/component_schemas.py
"""
Pydantic schemas for Component and ComponentCategory entities.

This module defines the validation schemas for component-related operations
including creation, updates, and API responses.
"""

import json
from typing import List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator

from .base import BaseSoftDeleteSchema, PaginatedResponseSchema


class ComponentCategoryBaseSchema(BaseModel):
    """Base schema for ComponentCategory with common fields."""

    name: str = Field(..., min_length=3, max_length=100, description="Category name")
    description: Optional[str] = Field(
        None, max_length=500, description="Category description"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate category name."""
        if not v or not v.strip():
            raise ValueError("Category name cannot be empty")

        # Normalize whitespace
        normalized = " ".join(v.strip().split())

        if len(normalized) < 3:
            raise ValueError("Category name must be at least 3 characters long")

        return normalized


class ComponentCategoryCreateSchema(ComponentCategoryBaseSchema):
    """Schema for creating a new component category."""

    parent_category_id: Optional[int] = Field(
        None, description="Parent category ID for hierarchical organization"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Electrical Components",
                "description": "Components for electrical systems",
                "parent_category_id": None,
            }
        }
    )


class ComponentCategoryUpdateSchema(BaseModel):
    """Schema for updating an existing component category."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Category name"
    )
    description: Optional[str] = Field(
        None, max_length=500, description="Category description"
    )
    parent_category_id: Optional[int] = Field(None, description="Parent category ID")

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(ComponentCategoryBaseSchema.validate_name)

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Updated Electrical Components",
                "description": "Updated description for electrical components",
            }
        }
    )


class ComponentCategoryReadSchema(ComponentCategoryBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying component category data."""

    parent_category_id: Optional[int] = Field(None, description="Parent category ID")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Electrical Components",
                "description": "Components for electrical systems",
                "parent_category_id": None,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ComponentBaseSchema(BaseModel):
    """Base schema for Component with common fields."""

    name: str = Field(..., min_length=3, max_length=100, description="Component name")
    manufacturer: Optional[str] = Field(
        None, max_length=100, description="Component manufacturer"
    )
    model: Optional[str] = Field(
        None, max_length=100, description="Component model number"
    )
    specific_data: Optional[str] = Field(
        None, description="JSON string containing component-specific attributes"
    )
    notes: Optional[str] = Field(
        None, max_length=1000, description="Additional notes about the component"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate component name."""
        if not v or not v.strip():
            raise ValueError("Component name cannot be empty")

        # Normalize whitespace
        normalized = " ".join(v.strip().split())

        if len(normalized) < 3:
            raise ValueError("Component name must be at least 3 characters long")

        return normalized

    @field_validator("specific_data")
    @classmethod
    def validate_specific_data(cls, v: Optional[str]) -> Optional[str]:
        """Validate that specific_data is valid JSON if provided."""
        if v is None or v.strip() == "":
            return None

        try:
            # Validate JSON format
            json.loads(v)
            return v
        except json.JSONDecodeError:
            raise ValueError("specific_data must be valid JSON")


class ComponentCreateSchema(ComponentBaseSchema):
    """Schema for creating a new component."""

    category_id: int = Field(..., description="ID of the component category")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Circuit Breaker 20A",
                "category_id": 1,
                "manufacturer": "Schneider Electric",
                "model": "QO120",
                "specific_data": '{"rating_amps": 20, "voltage": 120, "poles": 1}',
                "notes": "Standard single-pole circuit breaker",
            }
        }
    )


class ComponentUpdateSchema(BaseModel):
    """Schema for updating an existing component."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Component name"
    )
    category_id: Optional[int] = Field(None, description="ID of the component category")
    manufacturer: Optional[str] = Field(
        None, max_length=100, description="Component manufacturer"
    )
    model: Optional[str] = Field(
        None, max_length=100, description="Component model number"
    )
    specific_data: Optional[str] = Field(
        None, description="JSON string containing component-specific attributes"
    )
    notes: Optional[str] = Field(
        None, max_length=1000, description="Additional notes about the component"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(ComponentBaseSchema.validate_name)
    _validate_specific_data = field_validator("specific_data")(
        ComponentBaseSchema.validate_specific_data
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Updated Circuit Breaker 20A",
                "manufacturer": "Schneider Electric",
                "model": "QO120-NEW",
                "specific_data": '{"rating_amps": 20, "voltage": 120, "poles": 1, "updated": true}',
            }
        }
    )


class ComponentReadSchema(ComponentBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying component data."""

    category_id: int = Field(..., description="ID of the component category")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Circuit Breaker 20A",
                "category_id": 1,
                "manufacturer": "Schneider Electric",
                "model": "QO120",
                "specific_data": '{"rating_amps": 20, "voltage": 120, "poles": 1}',
                "notes": "Standard single-pole circuit breaker",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ComponentSummarySchema(BaseModel):
    """Lightweight schema for component listings."""

    id: int = Field(..., description="Component ID")
    name: str = Field(..., description="Component name")
    category_id: int = Field(..., description="Category ID")
    manufacturer: Optional[str] = Field(None, description="Manufacturer")
    model: Optional[str] = Field(None, description="Model")

    model_config = ConfigDict(from_attributes=True)


class ComponentListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated component list responses."""

    components: List[ComponentSummarySchema] = Field(
        ..., description="List of components"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "components": [
                    {
                        "id": 1,
                        "name": "Circuit Breaker 20A",
                        "category_id": 1,
                        "manufacturer": "Schneider Electric",
                        "model": "QO120",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class ComponentCategoryListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated component category list responses."""

    categories: List[ComponentCategoryReadSchema] = Field(
        ..., description="List of component categories"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "categories": [
                    {
                        "id": 1,
                        "name": "Electrical Components",
                        "description": "Components for electrical systems",
                        "parent_category_id": None,
                        "created_at": "2024-01-15T10:30:00Z",
                        "updated_at": "2024-01-15T10:30:00Z",
                        "is_deleted": False,
                        "deleted_at": None,
                        "deleted_by_user_id": None,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )
