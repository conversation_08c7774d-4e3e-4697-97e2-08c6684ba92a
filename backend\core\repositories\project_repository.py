# backend/core/repositories/project_repository.py
"""
Project Repository

This module provides data access layer for Project entities, extending the base
repository with project-specific query methods and operations.
"""

from typing import Any, Dict, List, Optional

from sqlalchemy import and_, select, update
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, selectinload

from config.logging_config import get_logger
from core.errors.exceptions import ProjectNotFoundError
from core.models.project import Project
from core.repositories.base_repository import BaseRepository

# Initialize logger for this module
logger = get_logger(__name__)


class ProjectRepository(BaseRepository[Project]):
    """
    Repository for Project entity data access operations.

    Extends BaseRepository with project-specific query methods and
    enhanced error handling for project operations.
    """

    def __init__(self, db_session: Session):
        """
        Initialize the Project repository.

        Args:
            db_session: SQLAlchemy database session
        """
        super().__init__(db_session, Project)
        logger.debug("ProjectRepository initialized")

    def get_by_code(self, code: str) -> Project:
        """
        Get project by project number/code.

        Args:
            code: Project number/code to search for

        Returns:
            Project: The found project

        Raises:
            ProjectNotFoundError: If project with given code doesn't exist
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching for project with code: {code}")

        try:
            stmt = select(self.model).where(self.model.project_number == code)
            result = self.db_session.scalar(stmt)

            if result is None:
                logger.debug(f"Project not found with code: {code}")
                raise ProjectNotFoundError(project_id=code)

            logger.debug(f"Found project: '{result.name}' (ID: {result.id})")
            return result

        except ProjectNotFoundError:
            raise
        except SQLAlchemyError as e:
            logger.error(f"Database error searching for project code {code}: {e}")
            self._handle_db_exception(e, "project")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_by_name(self, name: str) -> Optional[Project]:
        """
        Get project by name.

        Args:
            name: Project name to search for

        Returns:
            Optional[Project]: The found project or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching for project with name: {name}")

        try:
            stmt = select(self.model).where(self.model.name == name)
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(f"Found project: '{result.name}' (ID: {result.id})")
            else:
                logger.debug(f"Project not found with name: {name}")

            return result

        except SQLAlchemyError as e:
            logger.error(f"Database error searching for project name {name}: {e}")
            self._handle_db_exception(e, "project")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_active_projects(self, skip: int = 0, limit: int = 100) -> List[Project]:
        """
        Get list of active (non-deleted) projects.

        Args:
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Project]: List of active projects

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving active projects: skip={skip}, limit={limit}")

        try:
            stmt = (
                select(self.model)
                .where(self.model.is_deleted == False)
                .offset(skip)
                .limit(limit)
                .order_by(self.model.created_at.desc())
            )
            results = list(self.db_session.scalars(stmt).all())

            logger.debug(f"Retrieved {len(results)} active projects")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving active projects: {e}")
            self._handle_db_exception(e, "project")
            raise  # This will never be reached due to _handle_db_exception raising

    def update_project(
        self, project_id: int, update_data: Dict[str, Any]
    ) -> Optional[Project]:
        """
        Update project with given data.

        Args:
            project_id: ID of project to update
            update_data: Dictionary of fields to update

        Returns:
            Optional[Project]: Updated project or None if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(
            f"Updating project {project_id} with data: {list(update_data.keys())}"
        )

        try:
            # First check if project exists
            existing_project = self.get_by_id(project_id)
            if existing_project is None:
                logger.debug(f"Project {project_id} not found for update")
                return None

            # Update the project
            stmt = (
                update(self.model)
                .where(self.model.id == project_id)
                .values(**update_data)
            )
            self.db_session.execute(stmt)

            # Return updated project
            updated_project = self.get_by_id(project_id)
            logger.debug(f"Project {project_id} updated successfully")
            return updated_project

        except SQLAlchemyError as e:
            logger.error(f"Database error updating project {project_id}: {e}")
            self._handle_db_exception(e, "project")

    def soft_delete_project(
        self, project_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """
        Soft delete a project.

        Args:
            project_id: ID of project to delete
            deleted_by_user_id: ID of user performing the deletion

        Returns:
            bool: True if project was deleted, False if not found

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Soft deleting project {project_id}")

        try:
            from datetime import datetime, timezone

            update_data = {
                "is_deleted": True,
                "deleted_at": datetime.now(timezone.utc),
                "deleted_by_user_id": deleted_by_user_id,
            }

            stmt = (
                update(self.model)
                .where(
                    and_(self.model.id == project_id, self.model.is_deleted == False)
                )
                .values(**update_data)
            )

            result = self.db_session.execute(stmt)

            if result.rowcount > 0:
                logger.debug(f"Project {project_id} soft deleted successfully")
                return True
            else:
                logger.debug(f"Project {project_id} not found or already deleted")
                return False

        except SQLAlchemyError as e:
            logger.error(f"Database error soft deleting project {project_id}: {e}")
            self._handle_db_exception(e, "project")
            raise  # This will never be reached due to _handle_db_exception raising

    def get_project_with_related_data(self, project_id: int) -> Optional[Project]:
        """
        Get project with eagerly loaded related data to avoid N+1 queries.

        Args:
            project_id: ID of project to retrieve

        Returns:
            Optional[Project]: Project with related data or None

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Retrieving project {project_id} with related data")

        try:
            stmt = (
                select(self.model)
                .where(self.model.id == project_id)
                .options(
                    selectinload(self.model.switchboards),
                    selectinload(self.model.pipes),
                    selectinload(self.model.vessels),
                    selectinload(self.model.electrical_nodes),
                    selectinload(self.model.cable_routes),
                )
            )
            result = self.db_session.scalar(stmt)

            if result:
                logger.debug(
                    f"Retrieved project with related data: '{result.name}' (ID: {result.id})"
                )
            else:
                logger.debug(f"Project {project_id} not found")

            return result

        except SQLAlchemyError as e:
            logger.error(
                f"Database error retrieving project {project_id} with related data: {e}"
            )
            self._handle_db_exception(e, "project")

    def search_projects(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[Project]:
        """
        Search projects by name, project number, or description.

        Args:
            search_term: Term to search for
            skip: Number of records to skip (for pagination)
            limit: Maximum number of records to return

        Returns:
            List[Project]: List of matching projects

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug(f"Searching projects for term: '{search_term}'")

        try:
            search_pattern = f"%{search_term}%"

            stmt = (
                select(self.model)
                .where(
                    and_(
                        self.model.is_deleted == False,
                        (
                            self.model.name.ilike(search_pattern)
                            | self.model.project_number.ilike(search_pattern)
                            | self.model.description.ilike(search_pattern)
                        ),
                    )
                )
                .offset(skip)
                .limit(limit)
                .order_by(self.model.name)
            )

            results = list(self.db_session.scalars(stmt).all())
            logger.debug(f"Found {len(results)} projects matching '{search_term}'")
            return results

        except SQLAlchemyError as e:
            logger.error(f"Database error searching projects for '{search_term}': {e}")
            self._handle_db_exception(e, "project")
            raise  # This will never be reached due to _handle_db_exception raising

    def count_active_projects(self) -> int:
        """
        Count total number of active projects.

        Returns:
            int: Number of active projects

        Raises:
            DatabaseError: If database operation fails
        """
        logger.debug("Counting active projects")

        try:
            from sqlalchemy import func

            stmt = select(func.count(self.model.id)).where(
                self.model.is_deleted == False
            )
            result = self.db_session.scalar(stmt)

            logger.debug(f"Total active projects: {result}")
            return result or 0

        except SQLAlchemyError as e:
            logger.error(f"Database error counting active projects: {e}")
            self._handle_db_exception(e, "project")
            raise  # This will never be reached due to _handle_db_exception raising
