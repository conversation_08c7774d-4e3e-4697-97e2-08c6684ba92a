### Database Migrations Architecture Specification

**1. Purpose & Role**
Database migrations manage the evolution of the database schema over time. Their primary purpose is to:
* **Version Control Schema Changes:** Track all schema modifications (table creation, column additions/modifications, index changes) in a versioned, auditable history.
* **Automate Schema Updates:** Provide a programmatic and repeatable way to apply schema changes across different environments (development, testing, production deployments, and importantly, user's local machines for a desktop application).
* **Support Schema Rollbacks:** Allow for controlled reversion of schema changes if necessary.
* **Enable Collaboration:** Facilitate multiple developers working on schema changes concurrently without conflicts.

**2. Tool Choice (Alembic)**
* **Alembic:** The industry-standard migration tool for SQLAlchemy.
* **Justification:** Deep integration with SQLAlchemy's ORM, powerful auto-generation capabilities, robust versioning features, and a battle-tested history.

**3. Core Responsibilities & Functionality**

* **Version Tracking:** Alembic maintains a `versions/` directory containing Python scripts that represent each schema change, alongside a `alembic_version` table in the database to track the current applied version.
* **Migration Script Generation:**
    * **Auto-generation:** Alembic can automatically detect differences between the current SQLAlchemy ORM models (`Base.metadata`) and the actual database schema, generating a skeleton migration script.
    * **Manual Refinement:** Generated scripts often require manual review and refinement to ensure correct behavior, add custom DDL (Data Definition Language) or DML (Data Manipulation Language) operations, or handle complex data migrations.
* **Migration Application (Upgrade/Downgrade):**
    * **`upgrade`:** Applies pending migration scripts to bring the database schema to a newer version.
    * **`downgrade`:** Reverts applied migration scripts to roll back the schema to an older version (requires careful planning).
* **Branching & Merging (Advanced):** Alembic supports more complex scenarios like branching and merging migration histories, which may become relevant for larger teams or parallel feature development.

**4. Integration Points**

* **SQLAlchemy ORM (`src/core/models/`):** Alembic directly uses the `Base.metadata` object from your [SQLAlchemy ORM models](../../models/models-architecture.md) to detect schema changes for auto-generation.
* **Command-Line Interface (CLI):** Alembic is primarily managed via its command-line interface (e.g., `alembic revision -m "Add new table"`, `alembic upgrade head`).
* **Application Lifespan (`src/app.py` - For Desktop App):** For a desktop application that needs to update its local database seamlessly, migrations can be automatically applied during the application's [startup lifespan event](../../../lifespan-architecture.md).
    * **Mechanism:** During startup, after database connection is established, the application checks if the database is up-to-date and runs `alembic upgrade head` programmatically.
    * **User Experience:** This should be handled gracefully, potentially with a progress indicator if migrations are time-consuming, and robust error handling to inform the user if an update fails.
* **Configuration (`src/config/settings.py`):** Migration settings (e.g., database URL for Alembic CLI) can be derived from the application's [configuration](../../../config/config-architecture.md).

**5. Key Principles**

* **Reproducibility:** Migrations ensure that applying schema changes is a consistent and repeatable process across all environments.
* **Non-Destructive Changes:** Prioritize additive and non-destructive schema changes to minimize data loss risk.
* **Version Control:** Treat migration scripts as first-class citizens in source control.
* **Automated Updates:** For a desktop application, aim for silent and automatic database schema updates for end-users.