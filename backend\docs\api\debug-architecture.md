### Debugging Endpoints Architecture Specification

**1. Purpose & Role**
The Debugging Endpoints provide a set of HTTP endpoints primarily for **development, testing, and troubleshooting** purposes. Their role is to offer controlled visibility into the application's runtime state, configuration, and internal health, aiding developers and support staff in diagnosing issues without requiring direct server access or deep code debugging. They are *not* intended for general application functionality or end-user interaction.

**2. Location (`src/api/v1/debug_routes.py`)**
These endpoints reside within a dedicated router file (`debug_routes.py`) within the API layer's versioned directory. This ensures they are logically grouped and can be managed (especially in terms of enablement/disablement) as a single unit.

**3. Core Responsibilities & Functionality**

* **Application Information (`/debug/info`):** Expose general, non-sensitive details about the application's current state, such as:
    * Application name and version (`settings.APP_NAME`, `settings.APP_VERSION`).
    * Runtime environment (`settings.ENVIRONMENT`).
    * Python version.
    * FastAPI/Uvicorn versions.
    * Current date and time.
    * Status of `settings.DEBUG` flag.
    * Enabled feature flags (if applicable).
* **Database Status (`/debug/db-status`):** Provide a basic health check of the database connection:
    * Connectivity status (e.g., "connected", "disconnected").
    * Basic statistics (e.g., number of projects, users – ensuring no sensitive data exposure).
* **Authentication Debugging (`/dev/auth/generate-token`, `/dev/auth/validate-token`):** Offer utilities specifically for streamlining development of authentication flows:
    * Ability to generate test authentication tokens using simplified credentials (for dev use only).
    * Endpoint to validate an existing token and return its decoded, non-sensitive payload.
* **Log Access (`/debug/logs` - Highly Sensitive):** Potentially provide access to recent application logs, filtered by level or keyword. This is the most sensitive endpoint and requires extreme caution.

**4. Key Principles & Security Constraints (CRITICAL)**

* **Conditional Enablement (Mandatory):** Debugging endpoints **MUST ONLY BE ENABLED IN DEVELOPMENT OR TEST ENVIRONMENTS**. They are controlled by the `settings.DEBUG` flag (from `src/config/settings.py`) and are conditionally included in the main API router (`src/api/main_router.py`).
* **Strict Access Control:** Even in `DEBUG` mode, access to these endpoints **MUST BE RESTRICTED**. They should require:
    * **Authentication:** Only accessible by authenticated users.
    * **Authorization:** Access restricted to specific roles (e.g., `admin`, `developer`).
* **No Sensitive Data Exposure:** These endpoints **MUST NEVER** expose:
    * Database connection strings, credentials, or secrets.
    * API keys or other access tokens.
    * Raw environment variables.
    * User passwords (even hashed).
    * Any personally identifiable information (PII) beyond what is strictly necessary for debugging and already publicly available.
* **Limited Information:** Only expose information that is genuinely helpful for debugging and does not pose a security or privacy risk.
* **No Production Deployment:** Debugging endpoints **MUST NEVER BE INCLUDED OR ACCESSIBLE IN PRODUCTION BUILDS OR DEPLOYMENTS**. This often involves build-time exclusion, not just runtime conditional checks.
* **Excluded from OpenAPI Docs:** By default, these routes should be excluded from the auto-generated Swagger UI and ReDoc documentation (`include_in_schema=False` in FastAPI).
* **Read-Only Operations:** Primarily focus on providing information. Avoid endpoints that modify application state unless absolutely necessary for specific development testing, and then with extreme care and security.

**5. Interaction with Other Layers**

* **Application Root (`src/app.py` / `main.py`):** Responsible for conditionally including the `debug_routes` router based on `settings.DEBUG`.
* **Configuration Layer (`src/config/settings.py`):** Provides the `DEBUG` flag that controls the enablement of these endpoints.

    See the [Configuration Layer Architecture](../../config/config-architecture.md) for more details.

* **Logging Layer (`src/core/logging_config.py`):** Debugging endpoints might access log files or query logging states.

    See the [How-To: Configure Logging](../../how-to/how-to_logging.md) for more details on logging.

* **Security Middleware (`src/middleware/security.py`):** Enforces authentication and authorization for accessing these endpoints.

    See the [Security Layer Architecture](../../security/security-architecture.md) and [Middleware Layer Architecture](../../middleware/middleware-architecture.md) for more details.

---