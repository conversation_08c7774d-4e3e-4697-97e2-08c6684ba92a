# backend/app.py
"""
Ultimate Electrical Designer Backend Application

This module contains the FastAPI application instance and configuration
for the Ultimate Electrical Designer backend.
"""

from contextlib import asynccontextmanager

from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.responses import JSONResponse

# Import main API router
from backend.api.main_router import api_router

# Import core modules
from backend.config.logging_config import get_logger
from backend.config.settings import settings
from backend.core.database import close_engine, initialize_database

# Import middleware (these may not exist yet, so we'll comment them out)
from backend.middleware.error_handling import handle_application_error

# from backend.middleware.context import ContextMiddleware
# from backend.middleware.security import SecurityMiddleware

# Get logger for this module
logger = get_logger(__name__)


# --- Database Session Management ---
# Database session dependency is imported from backend.core.database.get_db
# This provides proper session management with automatic cleanup


# --- Application Lifespan Events ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Handles startup and shutdown events for the application with robust error handling.
    """
    logger.info("Application startup initiated.")

    try:
        # Initialize database with migrations and fallback handling
        logger.info("Initializing database...")
        engine = initialize_database(run_migrations=True, create_tables_if_needed=True)
        logger.info(f"Database initialized successfully using: {engine.url}")

        # Store engine in app state for access in other parts of the application
        app.state.db_engine = engine

        # Initialize any other startup resources here
        # cache_client = init_cache()
        # app.state.cache = cache_client

        logger.info("Application startup completed successfully.")
        yield  # Application runs

    except Exception as e:
        logger.critical(f"Application startup failed: {e}", exc_info=True)
        # In production, we might want to exit gracefully
        # For now, re-raise to prevent the app from starting with a broken state
        raise

    finally:
        logger.info("Application shutdown initiated.")

        try:
            # Clean up database connections
            close_engine()
            logger.info("Database connections closed.")

            # Clean up other resources
            # if hasattr(app.state, 'cache') and app.state.cache:
            #     app.state.cache.close()
            #     logger.info("Cache connections closed.")

        except Exception as e:
            logger.error(f"Error during shutdown cleanup: {e}", exc_info=True)

        logger.info("Application shutdown completed.")


# --- FastAPI Application Instance ---
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan,  # Attach lifespan context manager
)

# --- Register Middleware ---
# Middleware runs in the order they are added (bottom-up execution on response)
# Order is crucial! Error handling typically goes near the top (bottom of chain)
# so it can catch exceptions from other middleware.
# app.middleware("http")(ContextMiddleware)  # Inject context first
# app.middleware("http")(SecurityMiddleware)  # Authentication depends on context
# app.middleware("http")(LoggingMiddleware) # Log before/after request processing
# app.middleware("http")(RateLimitingMiddleware) # Rate limit early
# app.middleware("http")(CachingMiddleware) # Cache response after processing


# Custom exception handler for application-wide errors
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler that uses the centralized error handling logic.
    """
    error_response_schema = handle_application_error(
        exc, context_info={"path": request.url.path, "method": request.method}
    )
    return JSONResponse(
        status_code=error_response_schema.status_code,
        content=error_response_schema.model_dump(),  # Use .dict() for Pydantic v1
    )


# --- Include API Routers ---
app.include_router(
    api_router, prefix="/api"
)  # All API routes will be prefixed with /api


# Example root endpoint
@app.get("/")
async def read_root():
    return {"message": f"Welcome to {settings.APP_NAME} API v{settings.APP_VERSION}"}
