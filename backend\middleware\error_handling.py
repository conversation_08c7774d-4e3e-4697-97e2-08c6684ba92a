# src/middleware/error_handling.py (Updated snippet)
import logging

# Import your custom exceptions
from backend.core.errors.exceptions import (
    BaseApplicationException,
    DatabaseError,
    DataValidationError,
    NotFoundError,
)

# Import your application logger
from backend.core.logging_config import (
    app_logger as logger,  # Use the pre-configured logger
)

# Import your Pydantic error schema
from backend.schemas.error import ErrorResponseSchema


def handle_application_error(
    exc: Exception, context_info: dict = None
) -> ErrorResponseSchema:
    """
    Catches and processes application exceptions.
    Transforms them into a standardized error response and logs them.
    """
    context_info = context_info or {}

    if isinstance(exc, BaseApplicationException):
        # For our custom, predictable exceptions
        error_response = ErrorResponseSchema(
            code=exc.code,
            detail=exc.detail,
            category=exc.category,
            status_code=exc.status_code,
            metadata=exc.metadata,
        )
        # Determine log level based on error type/status code
        if isinstance(exc, (DataValidationError, NotFoundError)):
            log_level = logging.INFO  # These are expected client-side errors
        elif isinstance(exc, DatabaseError):
            log_level = logging.ERROR  # Database issues are critical
        else:
            log_level = logging.ERROR  # Default for other BaseApplicationExceptions

        logger.log(
            log_level,
            f"App Error [{exc.code}]: {exc.detail}",
            extra={"error_attributes": exc.metadata, "context_info": context_info},
        )

        # If it's a critical error, you might want to log the full traceback even for custom exceptions
        if exc.status_code >= 500:  # Or based on category "ServerError"
            logger.exception("Full traceback for server-side application error.")

    elif isinstance(exc, Exception):  # Catch all other unexpected Python exceptions
        # Fallback for truly unhandled, unexpected errors
        error_response = ErrorResponseSchema(
            code="500_000",  # Generic internal server error
            detail="An unexpected internal error occurred. Please contact support.",
            category="ServerError",
            status_code=500,
            metadata={
                "original_exception_type": type(exc).__name__,
                "message_preview": str(exc)[:200],
            },
        )
        # Use logger.exception() to automatically include stack trace for unhandled errors
        logger.exception(
            f"UNHANDLED EXCEPTION: {str(exc)}", extra={"context_info": context_info}
        )

    return error_response
