# backend/core/standards/iec_60079_30_1/temperature_class_limits.py
"""
IEC 60079-30-1 Temperature Class Limits.

This module implements temperature class validation according to
IEC 60079-30-1 for hazardous area applications.
"""

import logging
from typing import Dict, Any

from backend.core.errors.exceptions import StandardComplianceError

logger = logging.getLogger(__name__)

# IEC 60079-30-1 Temperature class limits (°C)
TEMPERATURE_CLASS_LIMITS = {
    "T1": 450,
    "T2": 300,
    "T3": 200,
    "T4": 135,
    "T5": 100,
    "T6": 85
}

# Safety margins for temperature classes (°C)
TEMPERATURE_SAFETY_MARGINS = {
    "T1": 50,
    "T2": 30,
    "T3": 20,
    "T4": 15,
    "T5": 10,
    "T6": 10
}


def validate_temperature_class(surface_temperature: float, temperature_class: str) -> bool:
    """
    Validate surface temperature against IEC 60079-30-1 temperature class.
    
    Args:
        surface_temperature: Surface temperature (°C)
        temperature_class: Temperature class (T1-T6)
        
    Returns:
        bool: True if temperature is within limits
        
    Raises:
        StandardComplianceError: If temperature exceeds limits
    """
    logger.debug(f"Validating temperature {surface_temperature}°C against class {temperature_class}")
    
    if temperature_class not in TEMPERATURE_CLASS_LIMITS:
        raise StandardComplianceError(
            f"Invalid temperature class: {temperature_class}. "
            f"Valid classes: {list(TEMPERATURE_CLASS_LIMITS.keys())}"
        )
    
    limit = TEMPERATURE_CLASS_LIMITS[temperature_class]
    safety_margin = TEMPERATURE_SAFETY_MARGINS[temperature_class]
    
    if surface_temperature > limit:
        raise StandardComplianceError(
            f"Surface temperature {surface_temperature}°C exceeds {temperature_class} "
            f"limit of {limit}°C (IEC 60079-30-1)"
        )
    
    if surface_temperature > limit - safety_margin:
        logger.warning(
            f"Surface temperature {surface_temperature}°C is close to {temperature_class} "
            f"limit ({limit}°C) - safety margin: {limit - surface_temperature:.1f}°C"
        )
    
    logger.debug(f"Temperature validation passed for {temperature_class}")
    return True


def get_temperature_class_for_temperature(surface_temperature: float) -> str:
    """
    Determine appropriate temperature class for given surface temperature.
    
    Args:
        surface_temperature: Surface temperature (°C)
        
    Returns:
        str: Appropriate temperature class
    """
    # Find the most restrictive class that accommodates the temperature
    for temp_class in ["T6", "T5", "T4", "T3", "T2", "T1"]:
        limit = TEMPERATURE_CLASS_LIMITS[temp_class]
        safety_margin = TEMPERATURE_SAFETY_MARGINS[temp_class]
        
        if surface_temperature <= limit - safety_margin:
            logger.debug(f"Recommended temperature class: {temp_class}")
            return temp_class
    
    # If temperature is too high for any class
    raise StandardComplianceError(
        f"Surface temperature {surface_temperature}°C exceeds all temperature class limits"
    )


def get_temperature_class_limits() -> Dict[str, Dict[str, float]]:
    """Get all temperature class limits and safety margins."""
    return {
        temp_class: {
            "limit": limit,
            "safety_margin": TEMPERATURE_SAFETY_MARGINS[temp_class],
            "recommended_max": limit - TEMPERATURE_SAFETY_MARGINS[temp_class]
        }
        for temp_class, limit in TEMPERATURE_CLASS_LIMITS.items()
    }


def validate_temperature_derating(
    ambient_temperature: float,
    surface_temperature: float,
    temperature_class: str
) -> Dict[str, Any]:
    """
    Validate temperature derating requirements.
    
    Args:
        ambient_temperature: Ambient temperature (°C)
        surface_temperature: Surface temperature (°C)
        temperature_class: Temperature class
        
    Returns:
        Dict with derating validation results
    """
    logger.debug(f"Validating temperature derating for {temperature_class}")
    
    limit = TEMPERATURE_CLASS_LIMITS[temperature_class]
    
    # Calculate temperature rise
    temperature_rise = surface_temperature - ambient_temperature
    
    # Check if derating is required (simplified rule)
    derating_required = ambient_temperature > 40  # °C
    
    if derating_required:
        # Apply derating factor (simplified)
        derating_factor = max(0.8, 1.0 - (ambient_temperature - 40) * 0.01)
        derated_limit = limit * derating_factor
    else:
        derating_factor = 1.0
        derated_limit = limit
    
    is_compliant = surface_temperature <= derated_limit
    
    return {
        "is_compliant": is_compliant,
        "temperature_class": temperature_class,
        "original_limit": limit,
        "derated_limit": derated_limit,
        "derating_factor": derating_factor,
        "derating_required": derating_required,
        "temperature_rise": temperature_rise,
        "ambient_temperature": ambient_temperature,
        "surface_temperature": surface_temperature
    }
