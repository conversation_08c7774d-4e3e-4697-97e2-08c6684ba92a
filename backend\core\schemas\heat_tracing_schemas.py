# backend/core/schemas/heat_tracing_schemas.py
"""
Pydantic schemas for Heat Tracing entities validation, serialization, and deserialization.

This module defines the data contracts for Heat Tracing-related operations including:
- Pipe schemas: For heat traced pipes with thermal calculations
- Vessel schemas: For heat traced vessels and tanks
- HTCircuit schemas: For heat tracing circuits with power calculations
- ControlCircuit schemas: For control circuit management
- Calculation schemas: For integration with calculations layer
- Validation schemas: For engineering constraints and business logic
"""

import json
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from ..models.enums import (
    ControlCircuitType,
    HeatingMethodType,
    HTCircuitApplicationType,
    SensorType,
)
from .base import BaseSoftDeleteSchema, PaginatedResponseSchema

# ============================================================================
# CALCULATION INTEGRATION SCHEMAS
# ============================================================================


class HeatLossCalculationInputSchema(BaseModel):
    """Schema for heat loss calculation inputs."""

    pipe_diameter: float = Field(..., gt=0, description="Pipe diameter in meters")
    pipe_length: float = Field(..., gt=0, description="Pipe length in meters")
    fluid_temperature: float = Field(
        ..., ge=-50, le=500, description="Fluid temperature in Celsius"
    )
    ambient_temperature: float = Field(
        ..., ge=-50, le=80, description="Ambient temperature in Celsius"
    )
    insulation_thickness: float = Field(
        ..., ge=0, description="Insulation thickness in meters"
    )
    insulation_type: str = Field(
        ..., min_length=1, description="Insulation material type"
    )
    wind_speed: Optional[float] = Field(
        0.0, ge=0, le=50, description="Wind speed in m/s"
    )
    pipe_material: str = Field("carbon_steel", description="Pipe material type")

    @model_validator(mode="after")
    def validate_fluid_temp(self):
        """Validate fluid temperature is above ambient."""
        if self.fluid_temperature <= self.ambient_temperature:
            raise ValueError(
                "Fluid temperature must be higher than ambient temperature"
            )
        return self


class HeatLossCalculationResultSchema(BaseModel):
    """Schema for heat loss calculation results."""

    heat_loss_rate: float = Field(..., description="Heat loss rate in W/m")
    total_heat_loss: float = Field(..., description="Total heat loss in W")
    surface_temperature: float = Field(
        ..., description="Surface temperature in Celsius"
    )
    required_power: float = Field(..., description="Required heating power in W")
    safety_factor: float = Field(..., description="Applied safety factor")
    calculation_metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Calculation metadata"
    )


class StandardsValidationInputSchema(BaseModel):
    """Schema for standards validation inputs."""

    heat_loss_result: HeatLossCalculationResultSchema
    design_parameters: Dict[str, Any] = Field(default_factory=dict)
    project_standards: List[str] = Field(default_factory=list)
    hazardous_area_zone: Optional[str] = Field(None, description="Hazardous area zone")
    gas_group: Optional[str] = Field(None, description="Gas group classification")
    temperature_class: Optional[str] = Field(None, description="Temperature class")


class StandardsValidationResultSchema(BaseModel):
    """Schema for standards validation results."""

    is_compliant: bool = Field(..., description="Overall compliance status")
    standard: str = Field(..., description="Applied standard")
    violations: List[str] = Field(
        default_factory=list, description="Compliance violations"
    )
    warnings: List[str] = Field(default_factory=list, description="Compliance warnings")
    applied_factors: Dict[str, float] = Field(
        default_factory=dict, description="Applied safety factors"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict, description="Validation metadata"
    )


# ============================================================================
# PIPE SCHEMAS
# ============================================================================


class PipeBaseSchema(BaseModel):
    """Base schema for Pipe with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Pipe name/identifier"
    )
    nominal_diameter_mm: Optional[float] = Field(
        None, gt=0, description="Nominal diameter in mm"
    )
    wall_thickness_mm: Optional[float] = Field(
        None, gt=0, description="Wall thickness in mm"
    )
    outer_diameter_mm: Optional[float] = Field(
        None, gt=0, description="Outer diameter in mm"
    )
    length_m: float = Field(..., gt=0, description="Pipe length in meters")
    insulation_thickness_mm: float = Field(
        ..., gt=0, description="Insulation thickness in mm"
    )
    fluid_type: Optional[str] = Field(None, max_length=100, description="Fluid type")
    specific_heat_capacity_jkgc: Optional[float] = Field(
        None, gt=0, description="Specific heat capacity in J/kg·C"
    )
    viscosity_cp: Optional[float] = Field(None, gt=0, description="Viscosity in cP")
    freezing_point_c: Optional[float] = Field(
        None, description="Freezing point in Celsius"
    )
    safety_margin_percent: float = Field(
        0.0, ge=0, le=100, description="Safety margin percentage"
    )

    # Process identification
    pid: Optional[str] = Field(None, max_length=50, description="P&ID reference")
    line_tag: Optional[str] = Field(None, max_length=50, description="Line tag")
    from_location: Optional[str] = Field(
        None, max_length=100, description="From location"
    )
    to_location: Optional[str] = Field(None, max_length=100, description="To location")
    valve_count: Optional[int] = Field(0, ge=0, description="Number of valves")
    support_count: Optional[int] = Field(0, ge=0, description="Number of supports")

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate pipe name."""
        if not v or not v.strip():
            raise ValueError("Pipe name cannot be empty")
        return v.strip()

    @field_validator("outer_diameter_mm")
    @classmethod
    def validate_outer_diameter(cls, v: Optional[float], info) -> Optional[float]:
        """Validate outer diameter is larger than nominal diameter."""
        if v is not None and "nominal_diameter_mm" in info.data:
            nominal = info.data["nominal_diameter_mm"]
            if nominal is not None and v <= nominal:
                raise ValueError("Outer diameter must be larger than nominal diameter")
        return v


class PipeCreateSchema(PipeBaseSchema):
    """Schema for creating a new pipe."""

    project_id: int = Field(..., description="Project ID")
    pipe_material_id: int = Field(..., description="Pipe material component ID")
    insulation_material_id: int = Field(
        ..., description="Insulation material component ID"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Main Process Line 001",
                "project_id": 1,
                "pipe_material_id": 10,
                "insulation_material_id": 15,
                "nominal_diameter_mm": 100.0,
                "wall_thickness_mm": 5.0,
                "outer_diameter_mm": 110.0,
                "length_m": 50.0,
                "insulation_thickness_mm": 50.0,
                "fluid_type": "Process Water",
                "specific_heat_capacity_jkgc": 4180.0,
                "viscosity_cp": 1.0,
                "freezing_point_c": 0.0,
                "safety_margin_percent": 20.0,
                "pid": "P&ID-001",
                "line_tag": "L-001",
                "from_location": "Tank A",
                "to_location": "Tank B",
                "valve_count": 3,
                "support_count": 5,
            }
        }
    )


class PipeUpdateSchema(BaseModel):
    """Schema for updating an existing pipe."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Pipe name/identifier"
    )
    pipe_material_id: Optional[int] = Field(
        None, description="Pipe material component ID"
    )
    insulation_material_id: Optional[int] = Field(
        None, description="Insulation material component ID"
    )
    nominal_diameter_mm: Optional[float] = Field(
        None, gt=0, description="Nominal diameter in mm"
    )
    wall_thickness_mm: Optional[float] = Field(
        None, gt=0, description="Wall thickness in mm"
    )
    outer_diameter_mm: Optional[float] = Field(
        None, gt=0, description="Outer diameter in mm"
    )
    length_m: Optional[float] = Field(None, gt=0, description="Pipe length in meters")
    insulation_thickness_mm: Optional[float] = Field(
        None, gt=0, description="Insulation thickness in mm"
    )
    fluid_type: Optional[str] = Field(None, max_length=100, description="Fluid type")
    specific_heat_capacity_jkgc: Optional[float] = Field(
        None, gt=0, description="Specific heat capacity in J/kg·C"
    )
    viscosity_cp: Optional[float] = Field(None, gt=0, description="Viscosity in cP")
    freezing_point_c: Optional[float] = Field(
        None, description="Freezing point in Celsius"
    )
    safety_margin_percent: Optional[float] = Field(
        None, ge=0, le=100, description="Safety margin percentage"
    )
    pid: Optional[str] = Field(None, max_length=50, description="P&ID reference")
    line_tag: Optional[str] = Field(None, max_length=50, description="Line tag")
    from_location: Optional[str] = Field(
        None, max_length=100, description="From location"
    )
    to_location: Optional[str] = Field(None, max_length=100, description="To location")
    valve_count: Optional[int] = Field(None, ge=0, description="Number of valves")
    support_count: Optional[int] = Field(None, ge=0, description="Number of supports")

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(PipeBaseSchema.validate_name)


class PipeReadSchema(PipeBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying pipe data."""

    project_id: int = Field(..., description="Project ID")
    pipe_material_id: int = Field(..., description="Pipe material component ID")
    insulation_material_id: int = Field(
        ..., description="Insulation material component ID"
    )
    calculated_heat_loss_wm: Optional[float] = Field(
        None, description="Calculated heat loss in W/m"
    )
    required_heat_output_wm: Optional[float] = Field(
        None, description="Required heat output in W/m"
    )
    imported_data_revision_id: Optional[int] = Field(
        None, description="Imported data revision ID"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Main Process Line 001",
                "project_id": 1,
                "pipe_material_id": 10,
                "insulation_material_id": 15,
                "nominal_diameter_mm": 100.0,
                "wall_thickness_mm": 5.0,
                "outer_diameter_mm": 110.0,
                "length_m": 50.0,
                "insulation_thickness_mm": 50.0,
                "fluid_type": "Process Water",
                "calculated_heat_loss_wm": 25.5,
                "required_heat_output_wm": 30.6,
                "safety_margin_percent": 20.0,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class PipeSummarySchema(BaseModel):
    """Lightweight schema for pipe listings."""

    id: int = Field(..., description="Pipe ID")
    name: str = Field(..., description="Pipe name")
    project_id: int = Field(..., description="Project ID")
    length_m: float = Field(..., description="Pipe length in meters")
    calculated_heat_loss_wm: Optional[float] = Field(
        None, description="Calculated heat loss in W/m"
    )
    line_tag: Optional[str] = Field(None, description="Line tag")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# VESSEL SCHEMAS
# ============================================================================


class VesselBaseSchema(BaseModel):
    """Base schema for Vessel with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Vessel name/identifier"
    )
    dimensions_json: str = Field(..., description="Vessel dimensions as JSON string")
    surface_area_m2: float = Field(..., gt=0, description="Surface area in m²")
    insulation_thickness_mm: float = Field(
        ..., gt=0, description="Insulation thickness in mm"
    )
    fluid_type: Optional[str] = Field(None, max_length=100, description="Fluid type")
    specific_heat_capacity_jkgc: Optional[float] = Field(
        None, gt=0, description="Specific heat capacity in J/kg·C"
    )
    viscosity_cp: Optional[float] = Field(None, gt=0, description="Viscosity in cP")
    freezing_point_c: Optional[float] = Field(
        None, description="Freezing point in Celsius"
    )
    safety_margin_percent: float = Field(
        0.0, ge=0, le=100, description="Safety margin percentage"
    )

    # Process identification
    pid: Optional[str] = Field(None, max_length=50, description="P&ID reference")
    equipment_tag: Optional[str] = Field(
        None, max_length=50, description="Equipment tag"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate vessel name."""
        if not v or not v.strip():
            raise ValueError("Vessel name cannot be empty")
        return v.strip()

    @field_validator("dimensions_json")
    @classmethod
    def validate_dimensions_json(cls, v: str) -> str:
        """Validate that dimensions_json is valid JSON."""
        try:
            dimensions = json.loads(v)
            if not isinstance(dimensions, dict):
                raise ValueError("Dimensions must be a JSON object")

            # Validate required dimension fields
            required_fields = ["type"]  # At minimum, vessel type is required
            for field in required_fields:
                if field not in dimensions:
                    raise ValueError(f'Dimensions JSON must contain "{field}" field')

            return v
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format for vessel dimensions")


class VesselCreateSchema(VesselBaseSchema):
    """Schema for creating a new vessel."""

    project_id: int = Field(..., description="Project ID")
    material_id: int = Field(..., description="Vessel material component ID")
    insulation_material_id: int = Field(
        ..., description="Insulation material component ID"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Storage Tank T-001",
                "project_id": 1,
                "material_id": 12,
                "insulation_material_id": 15,
                "dimensions_json": '{"type": "cylinder", "diameter": 2.0, "height": 3.0}',
                "surface_area_m2": 25.13,
                "insulation_thickness_mm": 75.0,
                "fluid_type": "Process Water",
                "specific_heat_capacity_jkgc": 4180.0,
                "viscosity_cp": 1.0,
                "freezing_point_c": 0.0,
                "safety_margin_percent": 25.0,
                "pid": "P&ID-001",
                "equipment_tag": "T-001",
            }
        }
    )


class VesselUpdateSchema(BaseModel):
    """Schema for updating an existing vessel."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="Vessel name/identifier"
    )
    material_id: Optional[int] = Field(None, description="Vessel material component ID")
    insulation_material_id: Optional[int] = Field(
        None, description="Insulation material component ID"
    )
    dimensions_json: Optional[str] = Field(
        None, description="Vessel dimensions as JSON string"
    )
    surface_area_m2: Optional[float] = Field(
        None, gt=0, description="Surface area in m²"
    )
    insulation_thickness_mm: Optional[float] = Field(
        None, gt=0, description="Insulation thickness in mm"
    )
    fluid_type: Optional[str] = Field(None, max_length=100, description="Fluid type")
    specific_heat_capacity_jkgc: Optional[float] = Field(
        None, gt=0, description="Specific heat capacity in J/kg·C"
    )
    viscosity_cp: Optional[float] = Field(None, gt=0, description="Viscosity in cP")
    freezing_point_c: Optional[float] = Field(
        None, description="Freezing point in Celsius"
    )
    safety_margin_percent: Optional[float] = Field(
        None, ge=0, le=100, description="Safety margin percentage"
    )
    pid: Optional[str] = Field(None, max_length=50, description="P&ID reference")
    equipment_tag: Optional[str] = Field(
        None, max_length=50, description="Equipment tag"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(VesselBaseSchema.validate_name)
    _validate_dimensions_json = field_validator("dimensions_json")(
        VesselBaseSchema.validate_dimensions_json
    )


class VesselReadSchema(VesselBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying vessel data."""

    project_id: int = Field(..., description="Project ID")
    material_id: int = Field(..., description="Vessel material component ID")
    insulation_material_id: int = Field(
        ..., description="Insulation material component ID"
    )
    calculated_heat_loss_w: Optional[float] = Field(
        None, description="Calculated heat loss in W"
    )
    required_heat_output_w: Optional[float] = Field(
        None, description="Required heat output in W"
    )
    imported_data_revision_id: Optional[int] = Field(
        None, description="Imported data revision ID"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Storage Tank T-001",
                "project_id": 1,
                "material_id": 12,
                "insulation_material_id": 15,
                "dimensions_json": '{"type": "cylinder", "diameter": 2.0, "height": 3.0}',
                "surface_area_m2": 25.13,
                "insulation_thickness_mm": 75.0,
                "calculated_heat_loss_w": 1250.0,
                "required_heat_output_w": 1562.5,
                "safety_margin_percent": 25.0,
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class VesselSummarySchema(BaseModel):
    """Lightweight schema for vessel listings."""

    id: int = Field(..., description="Vessel ID")
    name: str = Field(..., description="Vessel name")
    project_id: int = Field(..., description="Project ID")
    surface_area_m2: float = Field(..., description="Surface area in m²")
    calculated_heat_loss_w: Optional[float] = Field(
        None, description="Calculated heat loss in W"
    )
    equipment_tag: Optional[str] = Field(None, description="Equipment tag")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# CONTROL CIRCUIT SCHEMAS
# ============================================================================


class ControlCircuitBaseSchema(BaseModel):
    """Base schema for ControlCircuit with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="Control circuit name/identifier"
    )
    type: ControlCircuitType = Field(..., description="Control circuit type")
    sensor_type: SensorType = Field(..., description="Sensor type")
    primary_setpoint_c: float = Field(
        ..., ge=-50, le=500, description="Primary setpoint in Celsius"
    )
    limiting_setpoint_c: Optional[float] = Field(
        None, ge=-50, le=500, description="Limiting setpoint in Celsius"
    )
    has_limiting_function: bool = Field(False, description="Has limiting function")
    control_philosophy: Optional[str] = Field(
        None, max_length=500, description="Control philosophy description"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate control circuit name."""
        if not v or not v.strip():
            raise ValueError("Control circuit name cannot be empty")
        return v.strip()

    @field_validator("limiting_setpoint_c")
    @classmethod
    def validate_limiting_setpoint(cls, v: Optional[float], info) -> Optional[float]:
        """Validate limiting setpoint is higher than primary setpoint."""
        if v is not None and "primary_setpoint_c" in info.data:
            primary = info.data["primary_setpoint_c"]
            if v <= primary:
                raise ValueError(
                    "Limiting setpoint must be higher than primary setpoint"
                )
        return v


class ControlCircuitCreateSchema(ControlCircuitBaseSchema):
    """Schema for creating a new control circuit."""

    switchboard_id: Optional[int] = Field(None, description="Switchboard ID")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "Temperature Control TC-001",
                "switchboard_id": 1,
                "type": "temperature",
                "sensor_type": "rtd",
                "primary_setpoint_c": 65.0,
                "limiting_setpoint_c": 85.0,
                "has_limiting_function": True,
                "control_philosophy": "Maintain process temperature with high limit protection",
            }
        }
    )


class ControlCircuitUpdateSchema(BaseModel):
    """Schema for updating an existing control circuit."""

    name: Optional[str] = Field(
        None,
        min_length=3,
        max_length=100,
        description="Control circuit name/identifier",
    )
    switchboard_id: Optional[int] = Field(None, description="Switchboard ID")
    type: Optional[ControlCircuitType] = Field(None, description="Control circuit type")
    sensor_type: Optional[SensorType] = Field(None, description="Sensor type")
    primary_setpoint_c: Optional[float] = Field(
        None, ge=-50, le=500, description="Primary setpoint in Celsius"
    )
    limiting_setpoint_c: Optional[float] = Field(
        None, ge=-50, le=500, description="Limiting setpoint in Celsius"
    )
    has_limiting_function: Optional[bool] = Field(
        None, description="Has limiting function"
    )
    control_philosophy: Optional[str] = Field(
        None, max_length=500, description="Control philosophy description"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(ControlCircuitBaseSchema.validate_name)


class ControlCircuitReadSchema(ControlCircuitBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying control circuit data."""

    switchboard_id: Optional[int] = Field(None, description="Switchboard ID")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "Temperature Control TC-001",
                "switchboard_id": 1,
                "type": "temperature",
                "sensor_type": "rtd",
                "primary_setpoint_c": 65.0,
                "limiting_setpoint_c": 85.0,
                "has_limiting_function": True,
                "control_philosophy": "Maintain process temperature with high limit protection",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class ControlCircuitSummarySchema(BaseModel):
    """Lightweight schema for control circuit listings."""

    id: int = Field(..., description="Control circuit ID")
    name: str = Field(..., description="Control circuit name")
    type: ControlCircuitType = Field(..., description="Control circuit type")
    primary_setpoint_c: float = Field(..., description="Primary setpoint in Celsius")
    has_limiting_function: bool = Field(..., description="Has limiting function")

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# HT CIRCUIT SCHEMAS
# ============================================================================


class HTCircuitBaseSchema(BaseModel):
    """Base schema for HTCircuit with common fields."""

    name: str = Field(
        ..., min_length=3, max_length=100, description="HT circuit name/identifier"
    )
    number_of_circuits: int = Field(
        1, ge=1, le=10, description="Number of parallel circuits"
    )
    isometric_no: Optional[str] = Field(
        None, max_length=50, description="Isometric drawing number"
    )
    schedule_no: Optional[str] = Field(
        None, max_length=50, description="Schedule number"
    )
    schedule_page: Optional[str] = Field(
        None, max_length=20, description="Schedule page"
    )
    schedule_revision: Optional[str] = Field(
        None, max_length=10, description="Schedule revision"
    )
    application_type: Optional[HTCircuitApplicationType] = Field(
        None, description="Application type"
    )
    heating_method: Optional[HeatingMethodType] = Field(
        None, description="Heating method"
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate HT circuit name."""
        if not v or not v.strip():
            raise ValueError("HT circuit name cannot be empty")
        return v.strip()


class HTCircuitCreateSchema(HTCircuitBaseSchema):
    """Schema for creating a new HT circuit."""

    feeder_id: int = Field(..., description="Feeder ID")
    heat_tracing_cable_id: int = Field(
        ..., description="Heat tracing cable component ID"
    )
    pipe_id: Optional[int] = Field(None, description="Pipe ID (for pipe circuits)")
    vessel_id: Optional[int] = Field(
        None, description="Vessel ID (for vessel circuits)"
    )
    control_circuit_id: Optional[int] = Field(None, description="Control circuit ID")

    @model_validator(mode="after")
    def validate_pipe_or_vessel(self):
        """Validate that either pipe_id or vessel_id is provided, but not both."""
        pipe_id = self.pipe_id
        vessel_id = self.vessel_id

        if pipe_id is None and vessel_id is None:
            raise ValueError("Either pipe_id or vessel_id must be provided")
        if pipe_id is not None and vessel_id is not None:
            raise ValueError("Cannot specify both pipe_id and vessel_id")
        return self

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "name": "HTC-001-A",
                "feeder_id": 1,
                "heat_tracing_cable_id": 20,
                "pipe_id": 1,
                "vessel_id": None,
                "control_circuit_id": 1,
                "number_of_circuits": 1,
                "isometric_no": "ISO-001",
                "schedule_no": "HT-SCH-001",
                "schedule_page": "1",
                "schedule_revision": "A",
                "application_type": "PROCESS_TEMP",
                "heating_method": "PARALLEL",
            }
        }
    )


class HTCircuitUpdateSchema(BaseModel):
    """Schema for updating an existing HT circuit."""

    name: Optional[str] = Field(
        None, min_length=3, max_length=100, description="HT circuit name/identifier"
    )
    feeder_id: Optional[int] = Field(None, description="Feeder ID")
    heat_tracing_cable_id: Optional[int] = Field(
        None, description="Heat tracing cable component ID"
    )
    pipe_id: Optional[int] = Field(None, description="Pipe ID (for pipe circuits)")
    vessel_id: Optional[int] = Field(
        None, description="Vessel ID (for vessel circuits)"
    )
    control_circuit_id: Optional[int] = Field(None, description="Control circuit ID")
    number_of_circuits: Optional[int] = Field(
        None, ge=1, le=10, description="Number of parallel circuits"
    )
    isometric_no: Optional[str] = Field(
        None, max_length=50, description="Isometric drawing number"
    )
    schedule_no: Optional[str] = Field(
        None, max_length=50, description="Schedule number"
    )
    schedule_page: Optional[str] = Field(
        None, max_length=20, description="Schedule page"
    )
    schedule_revision: Optional[str] = Field(
        None, max_length=10, description="Schedule revision"
    )
    application_type: Optional[HTCircuitApplicationType] = Field(
        None, description="Application type"
    )
    heating_method: Optional[HeatingMethodType] = Field(
        None, description="Heating method"
    )

    # Apply the same validators as create schema
    _validate_name = field_validator("name")(HTCircuitBaseSchema.validate_name)


class HTCircuitReadSchema(HTCircuitBaseSchema, BaseSoftDeleteSchema):
    """Schema for reading/displaying HT circuit data."""

    feeder_id: int = Field(..., description="Feeder ID")
    heat_tracing_cable_id: int = Field(
        ..., description="Heat tracing cable component ID"
    )
    pipe_id: Optional[int] = Field(None, description="Pipe ID (for pipe circuits)")
    vessel_id: Optional[int] = Field(
        None, description="Vessel ID (for vessel circuits)"
    )
    control_circuit_id: Optional[int] = Field(None, description="Control circuit ID")
    calculated_load_amps: Optional[float] = Field(
        None, description="Calculated load in amps"
    )
    calculated_load_kw: Optional[float] = Field(
        None, description="Calculated load in kW"
    )
    required_length_m: Optional[float] = Field(
        None, description="Required cable length in meters"
    )

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": 1,
                "name": "HTC-001-A",
                "feeder_id": 1,
                "heat_tracing_cable_id": 20,
                "pipe_id": 1,
                "vessel_id": None,
                "control_circuit_id": 1,
                "calculated_load_amps": 5.2,
                "calculated_load_kw": 1.25,
                "required_length_m": 50.0,
                "number_of_circuits": 1,
                "isometric_no": "ISO-001",
                "schedule_no": "HT-SCH-001",
                "application_type": "PROCESS_TEMP",
                "heating_method": "PARALLEL",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "is_deleted": False,
                "deleted_at": None,
                "deleted_by_user_id": None,
            }
        },
    )


class HTCircuitSummarySchema(BaseModel):
    """Lightweight schema for HT circuit listings."""

    id: int = Field(..., description="HT circuit ID")
    name: str = Field(..., description="HT circuit name")
    feeder_id: int = Field(..., description="Feeder ID")
    pipe_id: Optional[int] = Field(None, description="Pipe ID")
    vessel_id: Optional[int] = Field(None, description="Vessel ID")
    calculated_load_kw: Optional[float] = Field(
        None, description="Calculated load in kW"
    )
    application_type: Optional[HTCircuitApplicationType] = Field(
        None, description="Application type"
    )

    model_config = ConfigDict(from_attributes=True)


# ============================================================================
# DESIGN WORKFLOW SCHEMAS
# ============================================================================


class HeatTracingDesignInputSchema(BaseModel):
    """Schema for heat tracing design workflow inputs."""

    project_id: int = Field(..., description="Project ID")
    pipe_ids: Optional[List[int]] = Field(
        None, description="List of pipe IDs to design"
    )
    vessel_ids: Optional[List[int]] = Field(
        None, description="List of vessel IDs to design"
    )
    design_parameters: Dict[str, Any] = Field(
        default_factory=dict, description="Design parameters"
    )
    standards_context: StandardsValidationInputSchema = Field(
        ..., description="Standards validation context"
    )
    auto_assign_circuits: bool = Field(
        True, description="Automatically assign circuits"
    )
    optimization_enabled: bool = Field(True, description="Enable power optimization")

    @field_validator("pipe_ids")
    @classmethod
    def validate_pipe_or_vessel_ids(
        cls, v: Optional[List[int]], info
    ) -> Optional[List[int]]:
        """Validate that either pipe_ids or vessel_ids is provided."""
        vessel_ids = info.data.get("vessel_ids")
        if (v is None or len(v) == 0) and (vessel_ids is None or len(vessel_ids) == 0):
            raise ValueError("Either pipe_ids or vessel_ids must be provided")
        return v


class HeatTracingDesignResultSchema(BaseModel):
    """Schema for heat tracing design workflow results."""

    project_id: int = Field(..., description="Project ID")
    designed_pipes: List[PipeReadSchema] = Field(
        default_factory=list, description="Designed pipes"
    )
    designed_vessels: List[VesselReadSchema] = Field(
        default_factory=list, description="Designed vessels"
    )
    created_circuits: List[HTCircuitReadSchema] = Field(
        default_factory=list, description="Created HT circuits"
    )
    calculation_results: List[HeatLossCalculationResultSchema] = Field(
        default_factory=list, description="Calculation results"
    )
    validation_results: List[StandardsValidationResultSchema] = Field(
        default_factory=list, description="Validation results"
    )
    design_summary: Dict[str, Any] = Field(
        default_factory=dict, description="Design summary statistics"
    )
    warnings: List[str] = Field(default_factory=list, description="Design warnings")
    errors: List[str] = Field(default_factory=list, description="Design errors")


# ============================================================================
# PAGINATED RESPONSE SCHEMAS
# ============================================================================


class PipeListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated pipe list responses."""

    pipes: List[PipeSummarySchema] = Field(..., description="List of pipes")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "pipes": [
                    {
                        "id": 1,
                        "name": "Main Process Line 001",
                        "project_id": 1,
                        "length_m": 50.0,
                        "calculated_heat_loss_wm": 25.5,
                        "line_tag": "L-001",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class VesselListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated vessel list responses."""

    vessels: List[VesselSummarySchema] = Field(..., description="List of vessels")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "vessels": [
                    {
                        "id": 1,
                        "name": "Storage Tank T-001",
                        "project_id": 1,
                        "surface_area_m2": 25.13,
                        "calculated_heat_loss_w": 1250.0,
                        "equipment_tag": "T-001",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class HTCircuitListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated HT circuit list responses."""

    circuits: List[HTCircuitSummarySchema] = Field(
        ..., description="List of HT circuits"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "circuits": [
                    {
                        "id": 1,
                        "name": "HTC-001-A",
                        "feeder_id": 1,
                        "pipe_id": 1,
                        "vessel_id": None,
                        "calculated_load_kw": 1.25,
                        "application_type": "PROCESS_TEMP",
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )


class ControlCircuitListResponseSchema(PaginatedResponseSchema):
    """Schema for paginated control circuit list responses."""

    control_circuits: List[ControlCircuitSummarySchema] = Field(
        ..., description="List of control circuits"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "control_circuits": [
                    {
                        "id": 1,
                        "name": "Temperature Control TC-001",
                        "type": "TEMPERATURE_CONTROL",
                        "primary_setpoint_c": 65.0,
                        "has_limiting_function": True,
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 10,
                "total_pages": 1,
            }
        }
    )
