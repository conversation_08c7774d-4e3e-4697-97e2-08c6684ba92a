# backend/core/calculations/heat_loss/vessel_heat_loss.py
"""
Vessel Heat Loss Calculations.

This module implements heat loss calculations for vessels and tanks.
"""

import math
import logging
from typing import Dict, Any

from backend.core.errors.exceptions import CalculationError, InvalidInputError

logger = logging.getLogger(__name__)


def calculate_vessel_heat_loss(
    diameter: float,
    height: float,
    fluid_temp: float,
    ambient_temp: float,
    insulation_thickness: float,
    insulation_conductivity: float,
    wind_speed: float = 0.0,
    vessel_type: str = "vertical_cylinder"
) -> Dict[str, Any]:
    """
    Calculate heat loss from an insulated vessel.
    
    Args:
        diameter: Vessel diameter (m)
        height: Vessel height (m)
        fluid_temp: Fluid temperature (°C)
        ambient_temp: Ambient temperature (°C)
        insulation_thickness: Insulation thickness (m)
        insulation_conductivity: Thermal conductivity (W/m·K)
        wind_speed: Wind speed (m/s)
        vessel_type: Type of vessel (vertical_cylinder, horizontal_cylinder, sphere)
        
    Returns:
        Dict with heat loss calculation results
        
    Raises:
        InvalidInputError: If input parameters are invalid
        CalculationError: If calculation fails
    """
    logger.debug(f"Calculating vessel heat loss: D={diameter}m, H={height}m")
    
    try:
        # Validate inputs
        _validate_vessel_inputs(diameter, height, fluid_temp, ambient_temp, insulation_thickness)
        
        if vessel_type == "vertical_cylinder":
            result = _calculate_cylinder_heat_loss(
                diameter, height, fluid_temp, ambient_temp,
                insulation_thickness, insulation_conductivity, wind_speed, "vertical"
            )
        elif vessel_type == "horizontal_cylinder":
            result = _calculate_cylinder_heat_loss(
                diameter, height, fluid_temp, ambient_temp,
                insulation_thickness, insulation_conductivity, wind_speed, "horizontal"
            )
        elif vessel_type == "sphere":
            result = _calculate_sphere_heat_loss(
                diameter, fluid_temp, ambient_temp,
                insulation_thickness, insulation_conductivity, wind_speed
            )
        else:
            raise InvalidInputError(f"Unsupported vessel type: {vessel_type}")
        
        logger.debug(f"Vessel heat loss calculated: {result['total_heat_loss']:.1f}W")
        return result
        
    except InvalidInputError:
        raise
    except Exception as e:
        logger.error(f"Vessel heat loss calculation failed: {e}", exc_info=True)
        raise CalculationError(f"Vessel heat loss calculation failed: {str(e)}")


def _validate_vessel_inputs(
    diameter: float, 
    height: float, 
    fluid_temp: float, 
    ambient_temp: float, 
    insulation_thickness: float
) -> None:
    """Validate vessel heat loss inputs."""
    if diameter <= 0:
        raise InvalidInputError("Vessel diameter must be positive")
    
    if height <= 0:
        raise InvalidInputError("Vessel height must be positive")
    
    if diameter > 50.0:  # 50m diameter seems unreasonable
        raise InvalidInputError("Vessel diameter exceeds reasonable limits")
    
    if height > 100.0:  # 100m height seems unreasonable
        raise InvalidInputError("Vessel height exceeds reasonable limits")
    
    if fluid_temp <= ambient_temp:
        raise InvalidInputError("Fluid temperature must be higher than ambient")
    
    if insulation_thickness < 0:
        raise InvalidInputError("Insulation thickness cannot be negative")


def _calculate_cylinder_heat_loss(
    diameter: float,
    height: float,
    fluid_temp: float,
    ambient_temp: float,
    insulation_thickness: float,
    insulation_conductivity: float,
    wind_speed: float,
    orientation: str
) -> Dict[str, Any]:
    """Calculate heat loss from cylindrical vessel."""
    
    # Calculate surface areas
    radius = diameter / 2
    insulated_radius = radius + insulation_thickness
    
    # Cylindrical surface area
    cylinder_area = 2 * math.pi * insulated_radius * height
    
    # End cap areas (2 circular ends)
    end_cap_area = 2 * math.pi * (insulated_radius ** 2)
    
    total_surface_area = cylinder_area + end_cap_area
    
    # Simplified heat loss calculation
    # In practice, this would be more complex with different heat transfer
    # coefficients for different surfaces
    
    delta_T = fluid_temp - ambient_temp
    
    # Thermal resistance through insulation
    if insulation_thickness > 0:
        # Cylindrical thermal resistance (simplified)
        R_cylinder = math.log(insulated_radius / radius) / (2 * math.pi * insulation_conductivity * height)
        
        # Flat surface thermal resistance for end caps
        R_end_caps = insulation_thickness / (insulation_conductivity * end_cap_area)
        
        # Combined thermal resistance (parallel combination)
        R_total = 1 / (1/R_cylinder + 1/R_end_caps)
        
        heat_loss = delta_T / R_total
    else:
        # No insulation - use overall heat transfer coefficient
        h_overall = 10.0  # W/m²·K (simplified)
        heat_loss = h_overall * total_surface_area * delta_T
    
    # Calculate surface temperature (simplified)
    surface_temp = ambient_temp + delta_T * 0.1  # Rough approximation
    
    return {
        "total_heat_loss": heat_loss,
        "surface_area": total_surface_area,
        "cylinder_area": cylinder_area,
        "end_cap_area": end_cap_area,
        "surface_temperature": surface_temp,
        "vessel_type": f"{orientation}_cylinder"
    }


def _calculate_sphere_heat_loss(
    diameter: float,
    fluid_temp: float,
    ambient_temp: float,
    insulation_thickness: float,
    insulation_conductivity: float,
    wind_speed: float
) -> Dict[str, Any]:
    """Calculate heat loss from spherical vessel."""
    
    radius = diameter / 2
    insulated_radius = radius + insulation_thickness
    
    # Surface area
    surface_area = 4 * math.pi * (insulated_radius ** 2)
    
    delta_T = fluid_temp - ambient_temp
    
    # Thermal resistance through insulation
    if insulation_thickness > 0:
        # Spherical thermal resistance
        R_insulation = (1/radius - 1/insulated_radius) / (4 * math.pi * insulation_conductivity)
        heat_loss = delta_T / R_insulation
    else:
        # No insulation
        h_overall = 12.0  # W/m²·K (slightly higher for sphere)
        heat_loss = h_overall * surface_area * delta_T
    
    # Calculate surface temperature (simplified)
    surface_temp = ambient_temp + delta_T * 0.08  # Spheres are more efficient
    
    return {
        "total_heat_loss": heat_loss,
        "surface_area": surface_area,
        "surface_temperature": surface_temp,
        "vessel_type": "sphere"
    }
